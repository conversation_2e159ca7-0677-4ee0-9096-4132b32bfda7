webpackJsonp([60],{

/***/ 1191:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


var template = __webpack_require__(2171);
var noMoreTips = __webpack_require__(1437);
var safeProductionController = __webpack_require__(1294);

var comm = Vue.extend({
    template: template,

    components: {
        'no-more-tips': noMoreTips
    },

    data: function data() {
        return {
            projectList: [] // 用户项目列表
        };
    },
    activated: function activated() {
        this.getProjectRelate();
    },


    methods: {
        /**
         * 获取打卡列表
         */
        getProjectRelate: function getProjectRelate() {
            var _this = this;

            var data = {
                pageNumber: 1,
                pageSize: 100
            };
            safeProductionController.getProjectRelate(data, function (res) {
                var projectList = [];
                if (res.projectList && res.projectList.length) {
                    projectList = res.projectList;
                }
                _this.projectList = projectList;
            }, function (err) {
                console.log('[失败]获取打卡列表', err);
            });
        },


        /**
         * 返回上一页
         */
        goBack: function goBack() {
            this.$router.go(-1);
        },


        /**
         * 跳转打卡记录页
         */
        goRecord: function goRecord() {
            this.$router.push({
                name: 'dailyDutyRecord',
                params: { shouldReload: true }
            });
        },


        /**
         * 跳转打卡页
         * @param item
         */
        goCheck: function goCheck(item) {
            var infoName = item.infoName,
                infoId = item.infoId,
                infoType = item.infoType,
                checkId = item.checkId,
                declareChildType = item.declareChildType,
                checkDispatchId = item.checkDispatchId;

            if (checkId || checkId === 0) {
                return this.$dialog.toast({ mes: '您已打卡，请勿重复操作' });
            };
            var query = {
                checkType: declareChildType,
                infoName: infoName,
                infoId: infoId,
                infoType: infoType,
                declareChildType: declareChildType,
                checkDispatchId: checkDispatchId
            };
            this.$router.push({
                name: 'dailyDutySubmit',
                query: query
            });
        }
    }
});

module.exports = comm;

/***/ }),

/***/ 1293:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAmCAYAAAAr+dCVAAAABHNCSVQICAgIfAhkiAAAAFhJREFUSInt1bEJgDAARNHDuUxrhnAgSeW8afJTWFoJBwG5P8CDq04yBTSgA4cLvHkawO4Gz4A/Ba+AAQN+ALGAkjZJ3QG9ss8PHDjwYtj7/YGXwdUND6BM2oYdqanvPEcAAAAASUVORK5CYII="

/***/ }),

/***/ 1294:
/***/ (function(module, exports, __webpack_require__) {

"use strict";
var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;

// 巡检controller
!(__WEBPACK_AMD_DEFINE_ARRAY__ = [__webpack_require__(6), __webpack_require__(1314)], __WEBPACK_AMD_DEFINE_RESULT__ = (function (serviceHelper, safeProductionService) {
    return {
        //首页-移动端首页统计信息
        indexPageStatistic: function indexPageStatistic(data, success, fail) {
            safeProductionService.indexPageStatistic(data, success, fail);
        },
        //隐患上报-提交隐患
        commitRiskInfo: function commitRiskInfo(data, success, fail) {
            safeProductionService.commitRiskInfo(data, success, fail);
        },
        //隐患上报-10分钟内修改上报隐患信息
        updateRiskInfo: function updateRiskInfo(data, success, fail) {
            safeProductionService.updateRiskInfo(data, success, fail);
        },
        //隐患字典-获取隐患类型、描述、分级字典
        getDictInfo: function getDictInfo(data, success, fail) {
            safeProductionService.getDictInfo(data, success, fail);
        },
        //隐患字典-获取隐患类型、分级、描述三者联动
        getRelateInfo: function getRelateInfo(data, success, fail) {
            safeProductionService.getRelateInfo(data, success, fail);
        },
        //隐患上报-获取隐患字典
        getSysDictInfo: function getSysDictInfo(data, success, fail) {
            safeProductionService.getSysDictInfo(data, success, fail);
        },
        //隐患上报-通过项目id获取整改人
        getProjectAndUser: function getProjectAndUser(data, success, fail) {
            safeProductionService.getProjectAndUser(data, success, fail);
        },
        //隐患上报-查询隐患详情
        getOneById: function getOneById(data, success, fail) {
            safeProductionService.getOneById(data, success, fail);
        },
        //隐患详情-查询隐患审批流程信息
        getReportOperation: function getReportOperation(data, success, fail) {
            safeProductionService.getReportOperation(data, success, fail);
        },
        //隐患相关流程指定审核人时选择获取用户列表
        getAssignUserList: function getAssignUserList(data, success, fail) {
            safeProductionService.getAssignUserList(data, success, fail);
        },
        //删除图片-根据图片id
        deleteUploadFile: function deleteUploadFile(data, success, fail) {
            safeProductionService.deleteUploadFile(data, success, fail);
        },
        //删除图片
        deleteUploadFileByBizTypeAndBizId: function deleteUploadFileByBizTypeAndBizId(data, success, fail) {
            safeProductionService.deleteUploadFileByBizTypeAndBizId(data, success, fail);
        },
        //隐患台账-隐患列表
        getAllRiskReportAbout: function getAllRiskReportAbout(data, success, fail) {
            safeProductionService.getAllRiskReportAbout(data, success, fail);
        },
        //隐患台账-我的上报
        getAllMyReport: function getAllMyReport(data, success, fail) {
            safeProductionService.getAllMyReport(data, success, fail);
        },
        //隐患台账-待整改
        getAllWaitingDispose: function getAllWaitingDispose(data, success, fail) {
            safeProductionService.getAllWaitingDispose(data, success, fail);
        },
        //隐患台账-待审批(含整改审批、延期审批)
        getAllWaitingAudit: function getAllWaitingAudit(data, success, fail) {
            safeProductionService.getAllWaitingAudit(data, success, fail);
        },
        //隐患台账-隐患处理列表
        rectifyDisposeList: function rectifyDisposeList(data, success, fail) {
            safeProductionService.rectifyDisposeList(data, success, fail);
        },
        //隐患台账-查询统计结果
        getCountGroupStatus: function getCountGroupStatus(data, success, fail) {
            safeProductionService.getCountGroupStatus(data, success, fail);
        },
        //隐患处理-上报分公司主要领导
        branchSendToLeader: function branchSendToLeader(data, success, fail) {
            safeProductionService.branchSendToLeader(data, success, fail);
        },
        //隐患处理-上报总公司分管领导、主要领导
        headSendToLeader: function headSendToLeader(data, success, fail) {
            safeProductionService.headSendToLeader(data, success, fail);
        },
        //隐患处理-延期申请
        extension: function extension(data, success, fail) {
            safeProductionService.extension(data, success, fail);
        },
        //隐患处理-整改上报
        commitRiskEliminateDispose: function commitRiskEliminateDispose(data, success, fail) {
            safeProductionService.commitRiskEliminateDispose(data, success, fail);
        },
        //隐患处理-延期申请审批
        extensionAudit: function extensionAudit(data, success, fail) {
            safeProductionService.extensionAudit(data, success, fail);
        },
        //隐患处理-上报审批
        riskReportAudit: function riskReportAudit(data, success, fail) {
            safeProductionService.riskReportAudit(data, success, fail);
        },
        //项目专栏-信息申报-项目信息管理字典
        getProjectDict: function getProjectDict(data, success, fail) {
            safeProductionService.getProjectDict(data, success, fail);
        },
        //项目专栏-信息申报-查询项目列表
        projectList: function projectList(data, success, fail) {
            safeProductionService.projectList(data, success, fail);
        },
        //项目专栏-信息申报-查询统计数据
        projectStatistic: function projectStatistic(data, success, fail) {
            safeProductionService.projectStatistic(data, success, fail);
        },
        //项目专栏-信息申报-获取用户id及所属公司id
        getProjectUserAndCompanyInfo: function getProjectUserAndCompanyInfo(data, success, fail) {
            safeProductionService.getProjectUserAndCompanyInfo(data, success, fail);
        },
        //项目专栏-信息申报-根据单位id获取单位负责人
        getUserByCompany: function getUserByCompany(data, success, fail) {
            safeProductionService.getUserByCompany(data, success, fail);
        },
        //项目专栏-信息申报-查询单个项目信息
        getOneInfo: function getOneInfo(data, success, fail) {
            safeProductionService.getOneInfo(data, success, fail);
        },
        //项目专栏-信息申报-项目申报-新增
        saveProject: function saveProject(data, cb) {
            safeProductionService.saveProject(data, cb);
        },
        //项目专栏-信息申报-项目申报-修改
        updateProject: function updateProject(data, cb) {
            safeProductionService.updateProject(data, cb);
        },
        //项目专栏-信息申报-泵站字典-泵站类型
        getPumpStationTypeDict: function getPumpStationTypeDict(data, success, fail) {
            safeProductionService.getPumpStationTypeDict(data, success, fail);
        },
        //项目专栏-信息申报-泵站字典-获取分公司
        getAllBranchCompany: function getAllBranchCompany(data, success, fail) {
            safeProductionService.getAllBranchCompany(data, success, fail);
        },
        //项目专栏-信息申报-泵站字典-获取分公司下泵站的人员
        getBranchAllPumpUser: function getBranchAllPumpUser(data, success, fail) {
            safeProductionService.getBranchAllPumpUser(data, success, fail);
        },
        //项目专栏-信息申报-新增泵站
        commitPumpStationInfo: function commitPumpStationInfo(data, success, fail) {
            safeProductionService.commitPumpStationInfo(data, success, fail);
        },
        //项目专栏-信息申报-修改泵站
        updatePumpStationInfo: function updatePumpStationInfo(data, success, fail) {
            safeProductionService.updatePumpStationInfo(data, success, fail);
        },
        //项目专栏-信息申报-修改闸站
        updateGateSationInfo: function updateGateSationInfo(data, success, fail) {
            safeProductionService.updateGateSationInfo(data, success, fail);
        },
        //项目专栏-信息申报-修改管网
        updatePipeMainInfo: function updatePipeMainInfo(data, success, fail) {
            safeProductionService.updatePipeMainInfo(data, success, fail);
        },
        //项目专栏-信息申报-新增闸站
        addGateSationInfo: function addGateSationInfo(data, success, fail) {
            safeProductionService.addGateSationInfo(data, success, fail);
        },
        //项目专栏-信息申报-获取管网相关信息
        getPipeRelateInfo: function getPipeRelateInfo(data, success, fail) {
            safeProductionService.getPipeRelateInfo(data, success, fail);
        },
        //项目专栏-信息申报-新增管网运营信息
        addPipeMainInfo: function addPipeMainInfo(data, success, fail) {
            safeProductionService.addPipeMainInfo(data, success, fail);
        },
        //项目专栏-泵站台账-获取列表
        findAllPumpDuty: function findAllPumpDuty(data, success, fail) {
            safeProductionService.findAllPumpDuty(data, success, fail);
        },
        //项目专栏-泵站台账-查询统计数据
        pumpStationStatistic: function pumpStationStatistic(data, success, fail) {
            safeProductionService.pumpStationStatistic(data, success, fail);
        },
        //项目专栏-泵站详情
        getOneInfoPump: function getOneInfoPump(data, success, fail) {
            safeProductionService.getOneInfoPump(data, success, fail);
        },
        //项目专栏-闸站详情
        getOneGateById: function getOneGateById(data, success, fail) {
            safeProductionService.getOneGateById(data, success, fail);
        },
        //项目专栏-管网详情
        getOnePipeMain: function getOnePipeMain(data, success, fail) {
            safeProductionService.getOnePipeMain(data, success, fail);
        },
        //项目专栏-我的申报
        getMyDeclareList: function getMyDeclareList(data, success, fail) {
            safeProductionService.getMyDeclareList(data, success, fail);
        },
        //项目专栏-申报处理-获取列表
        declareDisposeList: function declareDisposeList(data, success, fail) {
            safeProductionService.declareDisposeList(data, success, fail);
        },
        //项目专栏-申报处理-审批
        infoDeclareAudit: function infoDeclareAudit(data, success, fail) {
            safeProductionService.infoDeclareAudit(data, success, fail);
        },
        //项目专栏-判断项目是否已审批
        checkInfoAudit: function checkInfoAudit(data, success, fail) {
            safeProductionService.checkInfoAudit(data, success, fail);
        },
        //项目专栏-项目信息-获取审批流程
        getProjectProcess: function getProjectProcess(data, success, fail) {
            safeProductionService.getProjectProcess(data, success, fail);
        },
        //项目专栏-获取项目、运营台账列表
        getInfoPageList: function getInfoPageList(data, success, fail) {
            safeProductionService.getInfoPageList(data, success, fail);
        },
        //日常履职-获取项目列表
        getProjectRelate: function getProjectRelate(data, success, fail) {
            safeProductionService.getProjectRelate(data, success, fail);
        },
        //日常履职-获取打卡记录列表
        getCheckHistory: function getCheckHistory(data, success, fail) {
            safeProductionService.getCheckHistory(data, success, fail);
        },
        //日常履职-打卡
        saveCheck: function saveCheck(data, success, fail) {
            safeProductionService.saveCheck(data, success, fail);
        },
        //日常履职-删除打卡记录
        deleteDailyPerformCheck: function deleteDailyPerformCheck(data, success, fail) {
            safeProductionService.deleteDailyPerformCheck(data, success, fail);
        },
        //日常履职-修改打卡记录能否更新
        modifyUpdateStatus: function modifyUpdateStatus(data, success, fail) {
            safeProductionService.modifyUpdateStatus(data, success, fail);
        },
        //日常履职-打卡-获取可以指派的打卡人
        getAllUsersByUserCompany: function getAllUsersByUserCompany(data, success, fail) {
            safeProductionService.getAllUsersByUserCompany(data, success, fail);
        },
        //日常履职-打卡-保存日常履职打卡调度
        saveCheckDispatch: function saveCheckDispatch(data, cb) {
            safeProductionService.saveCheckDispatch(data, cb);
        },
        //日常履职-获取人员履职统计页面字典信息
        getCheckStatisticsDict: function getCheckStatisticsDict(data, success, fail) {
            safeProductionService.getCheckStatisticsDict(data, success, fail);
        },
        //日常履职-获取人员履职、项目履职统计信息
        getCheckStatisticsToApp: function getCheckStatisticsToApp(data, success, fail) {
            safeProductionService.getCheckStatisticsToApp(data, success, fail);
        },
        //每日报告台账-获取字典
        getDailyReportDict: function getDailyReportDict(data, success, fail) {
            safeProductionService.getDailyReportDict(data, success, fail);
        },
        //每日报告台账-获取列表
        getDailyReportList: function getDailyReportList(data, success, fail) {
            safeProductionService.getDailyReportList(data, success, fail);
        },
        //隐患分析-隐患排查治理统计字典
        riskEliminateStatisticDict: function riskEliminateStatisticDict(data, success, fail) {
            safeProductionService.riskEliminateStatisticDict(data, success, fail);
        },
        //隐患分析-隐患排查治理统计
        riskEliminateStatistic: function riskEliminateStatistic(data, success, fail) {
            safeProductionService.riskEliminateStatistic(data, success, fail);
        },
        //隐患分析-履职分析
        getCheckStatistics: function getCheckStatistics(data, success, fail) {
            safeProductionService.getCheckStatistics(data, success, fail);
        },
        //消息推送-获取消息列表
        getPushInfoByUserId: function getPushInfoByUserId(data, success, fail) {
            safeProductionService.getPushInfoByUserId(data, success, fail);
        },
        //消息推送-获取消息数量
        getUnReadInfoNum: function getUnReadInfoNum(data, success, fail) {
            safeProductionService.getUnReadInfoNum(data, success, fail);
        },
        //在线学习-学习资料
        onlineStudyPage: function onlineStudyPage(data, success, fail) {
            safeProductionService.onlineStudyPage(data, success, fail);
        },
        //在线学习-排行榜列表
        getTopRankList: function getTopRankList(data, success, fail) {
            safeProductionService.getTopRankList(data, success, fail);
        },
        //在线学习-个人学习报表
        getUserReport: function getUserReport(data, success, fail) {
            safeProductionService.getUserReport(data, success, fail);
        },
        //在线学习-更新阅读时长
        updateStudyDuration: function updateStudyDuration(data, cb) {
            safeProductionService.updateStudyDuration(data, cb);
        },
        //在线学习-获取用户总阅读时长
        loadUserStudyDuration: function loadUserStudyDuration(data, success, fail) {
            safeProductionService.loadUserStudyDuration(data, success, fail);
        },
        //在线学习-获取相关链接列表
        getAllRelateLinks: function getAllRelateLinks(data, success, fail) {
            safeProductionService.getAllRelateLinks(data, success, fail);
        },
        //在线考试-根据题组类型获取题组详细信息
        getSubjectGroupByType: function getSubjectGroupByType(data, success, fail) {
            safeProductionService.getSubjectGroupByType(data, success, fail);
        },
        //在线考试-获取个人积分情况
        getUserScoreInfo: function getUserScoreInfo(data, success, fail) {
            safeProductionService.getUserScoreInfo(data, success, fail);
        },
        //在线考试-获取积分排行榜列表
        getScoreTopRankInfo: function getScoreTopRankInfo(data, success, fail) {
            safeProductionService.getScoreTopRankInfo(data, success, fail);
        },
        //在线考试-试题相关信息获取
        getSubjectDict: function getSubjectDict(data, success, fail) {
            safeProductionService.getSubjectDict(data, success, fail);
        },
        //在线考试-获取每周答题个人成绩信息
        getWeekGroupAndUserGrade: function getWeekGroupAndUserGrade(data, success, fail) {
            safeProductionService.getWeekGroupAndUserGrade(data, success, fail);
        },
        //在线考试-获取每周和专项答题题目
        getExamGroupAnwserData: function getExamGroupAnwserData(data, success, fail) {
            safeProductionService.getExamGroupAnwserData(data, success, fail);
        },
        //在线考试-获取竞赛抢答答题题目
        getContestAnswerSubject: function getContestAnswerSubject(data, success, fail) {
            safeProductionService.getContestAnswerSubject(data, success, fail);
        },
        //在线考试-答案提交
        saveExamAnswerResult: function saveExamAnswerResult(data, cb) {
            safeProductionService.saveExamAnswerResult(data, cb);
        },
        //在线考试-竞赛抢答-答案提交
        saveContestAnswerResult: function saveContestAnswerResult(data, cb) {
            safeProductionService.saveContestAnswerResult(data, cb);
        },
        //在线考试-获取专项答题页面数据分页
        getSpecialGroupAndUserGrade: function getSpecialGroupAndUserGrade(data, cb) {
            safeProductionService.getSpecialGroupAndUserGrade(data, cb);
        },
        //在线考试-获取竞赛抢答答题页面数据分页
        userContestAnswerPageList: function userContestAnswerPageList(data, cb) {
            safeProductionService.userContestAnswerPageList(data, cb);
        },
        //在线考试-获取专项题库分数排行榜前五十
        getSpecailTopRankList: function getSpecailTopRankList(data, cb) {
            safeProductionService.getSpecailTopRankList(data, cb);
        },
        //在线考试-获取竞赛抢答题库分数排行榜前五十
        getContestAnswerTopRankList: function getContestAnswerTopRankList(data, cb) {
            safeProductionService.getContestAnswerTopRankList(data, cb);
        },
        //消息推送-单个消息获取是否需要处理
        getOneReportByPush: function getOneReportByPush(data, success, fail) {
            safeProductionService.getOneReportByPush(data, success, fail);
        },
        //消息推送-消息更新为已读状态
        updateHasRead: function updateHasRead(data, success, fail) {
            safeProductionService.updateHasRead(data, success, fail);
        },
        //我的处理
        getAllMyHaseDone: function getAllMyHaseDone(data, success, fail) {
            safeProductionService.getAllMyHaseDone(data, success, fail);
        },
        //我的处理-筛选
        getAllMyDoneScreenReportInfo: function getAllMyDoneScreenReportInfo(data, success, fail) {
            safeProductionService.getAllMyDoneScreenReportInfo(data, success, fail);
        },
        //筛选功能-获取筛选条件字典
        getScreenDict: function getScreenDict(data, cb) {
            safeProductionService.getScreenDict(data, cb);
        },
        //筛选功能-台账页
        getAllScreenReportInfo: function getAllScreenReportInfo(data, cb) {
            safeProductionService.getAllScreenReportInfo(data, cb);
        },
        //筛选功能-我的上报
        getAllMyScreenReportInfo: function getAllMyScreenReportInfo(data, cb) {
            safeProductionService.getAllMyScreenReportInfo(data, cb);
        },
        //次日计划-字典
        getNextDayPlanDict: function getNextDayPlanDict(data, success, fail) {
            safeProductionService.getNextDayPlanDict(data, success, fail);
        },
        //次日计划-项目列表
        getNextDayPlanProjectInfo: function getNextDayPlanProjectInfo(data, success, fail) {
            safeProductionService.getNextDayPlanProjectInfo(data, success, fail);
        },
        //次日计划-新增
        saveNextDayPlan: function saveNextDayPlan(data, cb) {
            safeProductionService.saveNextDayPlan(data, cb);
        },
        //次日计划-新增自定义计划
        saveNextDayPlanCustomProject: function saveNextDayPlanCustomProject(data, cb) {
            safeProductionService.saveNextDayPlanCustomProject(data, cb);
        },
        //次日计划-历史记录
        getAllMyNextDayPlanInfo: function getAllMyNextDayPlanInfo(data, success, fail) {
            safeProductionService.getAllMyNextDayPlanInfo(data, success, fail);
        },
        //次日计划-查询单个记录详情
        getOneNextDayPlanInfo: function getOneNextDayPlanInfo(data, success, fail) {
            safeProductionService.getOneNextDayPlanInfo(data, success, fail);
        },
        //次日计划-历史记录-条件查询
        getScreenNextDayPlan: function getScreenNextDayPlan(data, success, fail) {
            safeProductionService.getScreenNextDayPlan(data, success, fail);
        },
        //次日计划-历史记录-字典
        getAllNextPlanDict: function getAllNextPlanDict(data, success, fail) {
            safeProductionService.getAllNextPlanDict(data, success, fail);
        },
        //工作动态-查询-字典
        getWorkDynamicDict: function getWorkDynamicDict(data, success, fail) {
            safeProductionService.getWorkDynamicDict(data, success, fail);
        },
        //统计分析-学习分析-获取统计数据
        examSituationStatistics: function examSituationStatistics(data, success, fail) {
            safeProductionService.examSituationStatistics(data, success, fail);
        },
        //统计分析-学习分析-获取所有题库
        getAllGroupData: function getAllGroupData(data, success, fail) {
            safeProductionService.getAllGroupData(data, success, fail);
        },
        //统计分析-使用分析-获取统计数据
        safeProductionSystemStatistic: function safeProductionSystemStatistic(data, success, fail) {
            safeProductionService.safeProductionSystemStatistic(data, success, fail);
        },
        //统计分析-使用分析-获取系统各个分公司用户活跃度分析统计
        getUserActivityStatistic: function getUserActivityStatistic(data, success, fail) {
            safeProductionService.getUserActivityStatistic(data, success, fail);
        },
        //有限空间作业-督察填报-保存督察信息
        saveLimtspSupervision: function saveLimtspSupervision(data, success, fail) {
            safeProductionService.saveLimtspSupervision(data, success, fail);
        },
        //有限空间作业-作业详情-安全措施字典
        getWorkDict: function getWorkDict(success, fail) {
            safeProductionService.getWorkDict(success, fail);
        }
    };
}).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),
				__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));

/***/ }),

/***/ 1314:
/***/ (function(module, exports, __webpack_require__) {

"use strict";
var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;

!(__WEBPACK_AMD_DEFINE_ARRAY__ = [__webpack_require__(6)], __WEBPACK_AMD_DEFINE_RESULT__ = (function (serviceHelper) {
    return {
        //首页-移动端首页统计信息
        indexPageStatistic: function indexPageStatistic(data, success, fail) {
            var param = {
                id: 'indexPageStatistic',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患上报-提交隐患
        commitRiskInfo: function commitRiskInfo(data, success, fail) {
            var param = {
                id: 'commitRiskInfo',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患上报-10分钟内修改上报隐患信息
        updateRiskInfo: function updateRiskInfo(data, success, fail) {
            var param = {
                id: 'updateRiskInfo',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患上报-获取隐患字典
        getSysDictInfo: function getSysDictInfo(data, success, fail) {
            var param = {
                id: 'getSysDictInfo',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患字典-获取隐患类型、描述、分级字典
        getDictInfo: function getDictInfo(data, success, fail) {
            var param = {
                id: 'getDictInfo',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患字典-获取隐患类型、分级、描述三者联动
        getRelateInfo: function getRelateInfo(data, success, fail) {
            var param = {
                id: 'getRelateInfo',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患上报-通过项目id获取整改人
        getProjectAndUser: function getProjectAndUser(data, success, fail) {
            var param = {
                id: 'getProjectAndUser',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患上报-查询隐患详情
        getOneById: function getOneById(data, success, fail) {
            var param = {
                id: 'getOneById',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患详情-查询隐患审批流程信息
        getReportOperation: function getReportOperation(data, success, fail) {
            var param = {
                id: 'getReportOperation',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患相关流程指定审核人时选择获取用户列表
        getAssignUserList: function getAssignUserList(data, success, fail) {
            var param = {
                id: 'getAssignUserList',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //删除图片-根据图片id
        deleteUploadFile: function deleteUploadFile(data, success, fail) {
            var param = {
                id: 'deleteUploadFile',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //删除图片
        deleteUploadFileByBizTypeAndBizId: function deleteUploadFileByBizTypeAndBizId(data, success, fail) {
            var param = {
                id: 'deleteUploadFileByBizTypeAndBizId',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患台账-隐患列表
        getAllRiskReportAbout: function getAllRiskReportAbout(data, success, fail) {
            var param = {
                id: 'getAllRiskReportAbout',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患台账-我的上报
        getAllMyReport: function getAllMyReport(data, success, fail) {
            var param = {
                id: 'getAllMyReport',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患台账-待整改
        getAllWaitingDispose: function getAllWaitingDispose(data, success, fail) {
            var param = {
                id: 'getAllWaitingDispose',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患台账-待审批(含整改审批、延期审批)
        getAllWaitingAudit: function getAllWaitingAudit(data, success, fail) {
            var param = {
                id: 'getAllWaitingAudit',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患台账-隐患处理列表
        rectifyDisposeList: function rectifyDisposeList(data, success, fail) {
            var param = {
                id: 'rectifyDisposeList',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患台账-查询统计结果
        getCountGroupStatus: function getCountGroupStatus(data, success, fail) {
            var param = {
                id: 'getCountGroupStatus',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患处理-上报分公司主要领导
        branchSendToLeader: function branchSendToLeader(data, success, fail) {
            var param = {
                id: 'branchSendToLeader',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患处理-上报总公司分管领导、主要领导
        headSendToLeader: function headSendToLeader(data, success, fail) {
            var param = {
                id: 'headSendToLeader',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患处理-延期申请
        extension: function extension(data, success, fail) {
            var param = {
                id: 'extension',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患处理-整改上报
        commitRiskEliminateDispose: function commitRiskEliminateDispose(data, success, fail) {
            var param = {
                id: 'commitRiskEliminateDispose',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患处理-延期申请审批
        extensionAudit: function extensionAudit(data, success, fail) {
            var param = {
                id: 'extensionAudit',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患处理-隐患上报审批
        riskReportAudit: function riskReportAudit(data, success, fail) {
            var param = {
                id: 'riskReportAudit',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-项目申报-项目信息管理字典
        getProjectDict: function getProjectDict(data, success, fail) {
            var param = {
                id: 'getProjectDict',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-项目台账-查询列表
        projectList: function projectList(data, success, fail) {
            var param = {
                id: 'projectList',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-项目台账-查询统计数据
        projectStatistic: function projectStatistic(data, success, fail) {
            var param = {
                id: 'projectStatistic',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-项目申报-获取用户id及所属公司id
        getProjectUserAndCompanyInfo: function getProjectUserAndCompanyInfo(data, success, fail) {
            var param = {
                id: 'getProjectUserAndCompanyInfo',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-项目申报-根据单位id获取单位负责人
        getUserByCompany: function getUserByCompany(data, success, fail) {
            var param = {
                id: 'getUserByCompany',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-项目申报-查询单个项目信息
        getOneInfo: function getOneInfo(data, success, fail) {
            var param = {
                id: 'getOneInfo',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-项目申报-新增
        saveProject: function saveProject(data, success, fail) {
            var param = {
                id: 'saveProject',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-项目申报-修改
        updateProject: function updateProject(data, cb) {
            var param = {
                id: 'updateProject',
                parameter: data
            };
            serviceHelper.getResult(param, cb);
        },
        //项目专栏-信息申报-泵站字典-泵站类型
        getPumpStationTypeDict: function getPumpStationTypeDict(data, success, fail) {
            var param = {
                id: 'getPumpStationTypeDict',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-泵站字典-获取分公司
        getAllBranchCompany: function getAllBranchCompany(data, success, fail) {
            var param = {
                id: 'getAllBranchCompany',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-泵站字典-获取分公司下泵站的人员
        getBranchAllPumpUser: function getBranchAllPumpUser(data, success, fail) {
            var param = {
                id: 'getBranchAllPumpUser',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-新增泵站
        commitPumpStationInfo: function commitPumpStationInfo(data, success, fail) {
            var param = {
                id: 'commitPumpStationInfo',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-修改泵站
        updatePumpStationInfo: function updatePumpStationInfo(data, success, fail) {
            var param = {
                id: 'updatePumpStationInfo',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-修改闸站
        updateGateSationInfo: function updateGateSationInfo(data, success, fail) {
            var param = {
                id: 'updateGateSationInfo',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-修改管网
        updatePipeMainInfo: function updatePipeMainInfo(data, success, fail) {
            var param = {
                id: 'updatePipeMainInfo',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-新增闸站
        addGateSationInfo: function addGateSationInfo(data, success, fail) {
            var param = {
                id: 'addGateSationInfo',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-获取管网相关信息
        getPipeRelateInfo: function getPipeRelateInfo(data, success, fail) {
            var param = {
                id: 'getPipeRelateInfo',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-信息申报-新增管网运营信息
        addPipeMainInfo: function addPipeMainInfo(data, success, fail) {
            var param = {
                id: 'addPipeMainInfo',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-泵站台账-获取列表
        findAllPumpDuty: function findAllPumpDuty(data, success, fail) {
            var param = {
                id: 'findAllPumpDuty',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-泵站台账-查询统计数据
        pumpStationStatistic: function pumpStationStatistic(data, success, fail) {
            var param = {
                id: 'pumpStationStatistic',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-泵站详情
        getOneInfoPump: function getOneInfoPump(data, success, fail) {
            var param = {
                id: 'getOneInfoPump',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-闸站详情
        getOneGateById: function getOneGateById(data, success, fail) {
            var param = {
                id: 'getOneGateById',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-管网详情
        getOnePipeMain: function getOnePipeMain(data, success, fail) {
            var param = {
                id: 'getOnePipeMain',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-我的申报
        getMyDeclareList: function getMyDeclareList(data, success, fail) {
            var param = {
                id: 'getMyDeclareList',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-申报处理-获取列表
        declareDisposeList: function declareDisposeList(data, success, fail) {
            var param = {
                id: 'declareDisposeList',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-申报处理-审批
        infoDeclareAudit: function infoDeclareAudit(data, success, fail) {
            var param = {
                id: 'infoDeclareAudit',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-判断项目是否已审批
        checkInfoAudit: function checkInfoAudit(data, success, fail) {
            var param = {
                id: 'checkInfoAudit',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-项目信息-获取审批流程
        getProjectProcess: function getProjectProcess(data, success, fail) {
            var param = {
                id: 'getProjectProcess',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //项目专栏-获取项目、运营台账列表
        getInfoPageList: function getInfoPageList(data, success, fail) {
            var param = {
                id: 'getInfoPageList',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //日常履职-获取项目列表
        getProjectRelate: function getProjectRelate(data, success, fail) {
            var param = {
                id: 'getProjectRelate',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //日常履职-获取打卡记录列表
        getCheckHistory: function getCheckHistory(data, success, fail) {
            var param = {
                id: 'getCheckHistory',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //日常履职-打卡
        saveCheck: function saveCheck(data, success, fail) {
            var param = {
                id: 'saveCheck',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //日常履职-删除打卡记录
        deleteDailyPerformCheck: function deleteDailyPerformCheck(data, success, fail) {
            var param = {
                id: 'deleteDailyPerformCheck',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //日常履职-修改打卡记录能否更新
        modifyUpdateStatus: function modifyUpdateStatus(data, success, fail) {
            var param = {
                id: 'modifyUpdateStatus',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //日常履职-打卡-获取可以指派的打卡人
        getAllUsersByUserCompany: function getAllUsersByUserCompany(data, success, fail) {
            var param = {
                id: 'getAllUsersByUserCompany',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //日常履职-打卡-保存日常履职打卡调度
        saveCheckDispatch: function saveCheckDispatch(data, cb) {
            var param = {
                id: 'saveCheckDispatch',
                parameter: data
            };
            serviceHelper.getResult(param, cb);
        },
        //日常履职-获取人员履职统计页面字典信息
        getCheckStatisticsDict: function getCheckStatisticsDict(data, success, fail) {
            var param = {
                id: 'getCheckStatisticsDict',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //日常履职-获取人员履职、项目履职统计信息
        getCheckStatisticsToApp: function getCheckStatisticsToApp(data, success, fail) {
            var param = {
                id: 'getCheckStatisticsToApp',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //每日报告台账-获取字典
        getDailyReportDict: function getDailyReportDict(data, success, fail) {
            var param = {
                id: 'getDailyReportDict',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //每日报告台账-获取列表
        getDailyReportList: function getDailyReportList(data, success, fail) {
            var param = {
                id: 'getDailyReportList',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患分析-隐患排查治理统计字典
        riskEliminateStatisticDict: function riskEliminateStatisticDict(data, success, fail) {
            var param = {
                id: 'riskEliminateStatisticDict',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患分析-隐患排查治理统计
        riskEliminateStatistic: function riskEliminateStatistic(data, success, fail) {
            var param = {
                id: 'riskEliminateStatistic',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //隐患分析-履职分析
        getCheckStatistics: function getCheckStatistics(data, success, fail) {
            var param = {
                id: 'getCheckStatistics',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //消息推送-获取消息列表
        getPushInfoByUserId: function getPushInfoByUserId(data, success, fail) {
            var param = {
                id: 'getPushInfoByUserId',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //消息推送-获取消息数量
        getUnReadInfoNum: function getUnReadInfoNum(data, success, fail) {
            var param = {
                id: 'getUnReadInfoNum',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线学习-学习资料
        onlineStudyPage: function onlineStudyPage(data, success, fail) {
            var param = {
                id: 'onlineStudyPage',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线学习-排行榜列表
        getTopRankList: function getTopRankList(data, success, fail) {
            var param = {
                id: 'getTopRankList',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线学习-个人学习报表
        getUserReport: function getUserReport(data, success, fail) {
            var param = {
                id: 'getUserReport',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线学习-更新阅读时长
        updateStudyDuration: function updateStudyDuration(data, cb) {
            var param = {
                id: 'updateStudyDuration',
                parameter: data
            };
            serviceHelper.getResult(param, cb);
        },
        //在线学习-获取用户总阅读时长
        loadUserStudyDuration: function loadUserStudyDuration(data, success, fail) {
            var param = {
                id: 'loadUserStudyDuration',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线学习-获取相关链接列表
        getAllRelateLinks: function getAllRelateLinks(data, success, fail) {
            var param = {
                id: 'getAllRelateLinks',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线考试-根据题组类型获取题组详细信息
        getSubjectGroupByType: function getSubjectGroupByType(data, success, fail) {
            var param = {
                id: 'getSubjectGroupByType',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线考试-获取个人积分情况
        getUserScoreInfo: function getUserScoreInfo(data, success, fail) {
            var param = {
                id: 'getUserScoreInfo',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线考试-获取积分排行榜列表
        getScoreTopRankInfo: function getScoreTopRankInfo(data, success, fail) {
            var param = {
                id: 'getScoreTopRankInfo',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线考试-试题相关信息获取
        getSubjectDict: function getSubjectDict(data, success, fail) {
            var param = {
                id: 'getSubjectDict',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线考试-获取每周答题个人成绩信息
        getWeekGroupAndUserGrade: function getWeekGroupAndUserGrade(data, success, fail) {
            var param = {
                id: 'getWeekGroupAndUserGrade',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线考试-获取每周和专项答题题目
        getExamGroupAnwserData: function getExamGroupAnwserData(data, success, fail) {
            var param = {
                id: 'getExamGroupAnwserData',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线考试-获取竞赛抢答答题题目
        getContestAnswerSubject: function getContestAnswerSubject(data, success, fail) {
            var param = {
                id: 'getContestAnswerSubject',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线考试-答案提交
        saveExamAnswerResult: function saveExamAnswerResult(data, cb) {
            var param = {
                id: 'saveExamAnswerResult',
                parameter: data
            };
            serviceHelper.getResult(param, cb);
        },
        //在线考试-竞赛抢答-答案提交
        saveContestAnswerResult: function saveContestAnswerResult(data, cb) {
            var param = {
                id: 'saveContestAnswerResult',
                parameter: data
            };
            serviceHelper.getResult(param, cb);
        },
        //在线考试-获取专项答题页面数据分页
        getSpecialGroupAndUserGrade: function getSpecialGroupAndUserGrade(data, success, fail) {
            var param = {
                id: 'getSpecialGroupAndUserGrade',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线考试-获取竞赛抢答答题页面数据分页
        userContestAnswerPageList: function userContestAnswerPageList(data, success, fail) {
            var param = {
                id: 'userContestAnswerPageList',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线考试-获取专项题库分数排行榜前五十
        getSpecailTopRankList: function getSpecailTopRankList(data, success, fail) {
            var param = {
                id: 'getSpecailTopRankList',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //在线考试-获取竞赛抢答题库分数排行榜前五十
        getContestAnswerTopRankList: function getContestAnswerTopRankList(data, success, fail) {
            var param = {
                id: 'getContestAnswerTopRankList',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //消息推送-单个消息获取是否需要处理
        getOneReportByPush: function getOneReportByPush(data, success, fail) {
            var param = {
                id: 'getOneReportByPush',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //消息推送-消息更新为已读状态
        updateHasRead: function updateHasRead(data, success, fail) {
            var param = {
                id: 'updateHasRead',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //我的处理
        getAllMyHaseDone: function getAllMyHaseDone(data, success, fail) {
            var param = {
                id: 'getAllMyHaseDone',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //我的处理-筛选
        getAllMyDoneScreenReportInfo: function getAllMyDoneScreenReportInfo(data, success, fail) {
            var param = {
                id: 'getAllMyDoneScreenReportInfo',
                parameter: data
            };
            serviceHelper.getResult(param, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //筛选功能-获取筛选条件字典
        getScreenDict: function getScreenDict(data, success, fail) {
            var param = {
                id: 'getScreenDict',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //筛选功能-台账页
        getAllScreenReportInfo: function getAllScreenReportInfo(data, cb) {
            var param = {
                id: 'getAllScreenReportInfo',
                parameter: data
            };
            serviceHelper.getResult(param, cb);
        },
        //筛选功能-我的上报
        getAllMyScreenReportInfo: function getAllMyScreenReportInfo(data, success, fail) {
            var param = {
                id: 'getAllMyScreenReportInfo',
                parameter: data
            };
            serviceHelper.getResult(param, success, fail);
        },
        //次日计划-字典
        getNextDayPlanDict: function getNextDayPlanDict(data, success, fail) {
            var param = {
                id: 'getNextDayPlanDict',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //次日计划-项目列表
        getNextDayPlanProjectInfo: function getNextDayPlanProjectInfo(data, success, fail) {
            var param = {
                id: 'getNextDayPlanProjectInfo',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //次日计划-新增
        saveNextDayPlan: function saveNextDayPlan(data, cb) {
            var param = {
                id: 'saveNextDayPlan',
                parameter: data
            };
            serviceHelper.getResult(param, cb);
        },
        //次日计划-新增自定义计划
        saveNextDayPlanCustomProject: function saveNextDayPlanCustomProject(data, cb) {
            var param = {
                id: 'saveNextDayPlanCustomProject',
                parameter: data
            };
            serviceHelper.getResult(param, cb);
        },
        //次日计划-历史记录
        getAllMyNextDayPlanInfo: function getAllMyNextDayPlanInfo(data, success, fail) {
            var param = {
                id: 'getAllMyNextDayPlanInfo',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //次日计划-查询单个记录详情
        getOneNextDayPlanInfo: function getOneNextDayPlanInfo(data, success, fail) {
            var param = {
                id: 'getOneNextDayPlanInfo',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //次日计划-历史记录-条件查询
        getScreenNextDayPlan: function getScreenNextDayPlan(data, success, fail) {
            var param = {
                id: 'getScreenNextDayPlan',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //次日计划-历史记录-字典
        getAllNextPlanDict: function getAllNextPlanDict(data, success, fail) {
            var param = {
                id: 'getAllNextPlanDict',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //工作动态-查询-字典
        getWorkDynamicDict: function getWorkDynamicDict(data, success, fail) {
            var param = {
                id: 'getWorkDynamicDict',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //统计分析-学习分析-获取统计数据
        examSituationStatistics: function examSituationStatistics(data, success, fail) {
            var param = {
                id: 'examSituationStatistics',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //统计分析-学习分析-获取所有题库
        getAllGroupData: function getAllGroupData(data, success, fail) {
            var param = {
                id: 'getAllGroupData',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //统计分析-使用分析-获取统计数据
        safeProductionSystemStatistic: function safeProductionSystemStatistic(data, success, fail) {
            var param = {
                id: 'safeProductionSystemStatistic',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //统计分析-使用分析-获取系统各个分公司用户活跃度分析统计
        getUserActivityStatistic: function getUserActivityStatistic(data, success, fail) {
            var param = {
                id: 'getUserActivityStatistic',
                parameter: data
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        },
        //有限空间作业-作业督察填报-获取作业列表
        workPageList: function workPageList(param, success, fail) {
            serviceHelper.axiosPost('workPageList', param, { responseType: 'json' }).then(function (result) {
                if (!!result.success) {
                    success(result.data);
                } else fail && fail(result);
            });
        },
        //有限空间作业-作业督察填报-
        saveLimtspSupervision: function saveLimtspSupervision(param, success, fail) {
            serviceHelper.axiosPost('saveLimtspSupervision', param, { responseType: 'json' }).then(function (result) {
                if (!!result.success) {
                    success(result.data);
                } else fail && fail(result);
            });
        },
        getWorkDict: function getWorkDict(success, fail) {
            var param = {
                id: 'getWorkDict',
                parameter: ''
            };
            serviceHelper.getJson(serviceHelper.getPath(param.id), param.parameter, function (result) {
                success(result);
            }, function (err) {
                fail(err);
            });
        }
    };
}).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),
				__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));

/***/ }),

/***/ 1437:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


var template = __webpack_require__(1438);

var comm = Vue.extend({
    template: template,

    props: {
        text: {
            type: String,
            require: false,
            default: '暂无数据'
        }
    }
});

module.exports = comm;

/***/ }),

/***/ 1438:
/***/ (function(module, exports) {

module.exports = "<div class=\"noMoreTips\">\n    {{text}}\n</div>";

/***/ }),

/***/ 2171:
/***/ (function(module, exports, __webpack_require__) {

module.exports = "<div class=\"dailyDuty\">\n    <!-- navbar -->\n    <div class=\"navBarWrapper\">\n        <div class=\"left\" @click=\"goBack\">\n            <img class=\"icon\" src=\"" + __webpack_require__(1293) + "\"/>\n        </div>\n        <div class=\"middle\">\n            <span>日常履职</span>\n        </div>\n        <div class=\"right\"></div>\n    </div>\n    <!-- btn -->\n    <div class=\"btnWrapper\">\n        <div class=\"btn\" @click=\"goRecord\">打卡记录</div>\n    </div>\n    <!-- 数据列表 -->\n    <ul class=\"dataListWrapper\" v-if=\"projectList.length\">\n        <li\n                v-for=\"item in projectList\"\n                :key=\"item.id\"\n                class=\"datalistItemWrapper blue\"\n                :class=\"{'disabled': item.checkId || item.checkId === 0}\"\n                @click=\"goCheck(item)\"\n        >\n            {{item.infoName}}\n        </li>\n    </ul>\n    <!-- 暂无数据 -->\n    <no-more-tips text=\"您不属于任何项目\" v-else/>\n</div>\n";

/***/ })

});