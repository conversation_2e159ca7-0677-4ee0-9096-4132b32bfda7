package com.cesc.ewater.biz.commonsearch.service;

import com.cesc.ewater.biz.commonsearch.model.SearchOperationBean;
import com.cesc.ewater.biz.commonsearch.thread.CommonSearchByPointThread;
import com.cesc.ewater.biz.pipe.util.PipeQueryUtil;
import com.cesc.ewater.framework.login.entity.LoginUser;
import com.cesc.ewater.framework.login.service.TokenManagerService;
import com.cesc.ewater.util.convert.DataConvertUtil;
import com.cesc.ewater.util.convert.JsonConvertUtil;
import com.cesc.ewater.util.convert.OracleSqlUtil;
import com.cesc.ewater.util.page.PagingInfo;
import com.cesc.ewater.util.spring.SpringContextUtil;
import com.cesc.ewater.util.web.AESUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 通用sde查询 service
 */
@Service(value = "CommonSearchService")
public class CommonSearchService {

    Logger logger = LoggerFactory.getLogger(CommonSearchService.class);

    @Autowired
    @Qualifier("defaultJdbcTemplate")
    protected JdbcTemplate defaultJdbcTemplate;

    @Autowired
    @Qualifier("chartJdbcTemplate")
    protected JdbcTemplate chartJdbcTemplate;

    @Autowired
    @Qualifier("maintainJdbcTemplate")
    protected JdbcTemplate maintainJdbcTemplate;

//    @Autowired
//    @Qualifier("unitpipeJdbcTemplate")
//    protected JdbcTemplate unitpipeJdbcTemplate;
//
//    @Autowired
//    @Qualifier("unitpipeTempJdbcTemplate")
//    protected JdbcTemplate unitpipeTempJdbcTemplate;
//
//    @Autowired
//    @Qualifier("drawTempJdbcTemplate")
//    protected JdbcTemplate drawTempJdbcTemplate;

    private Map<String, JdbcTemplate> dataSourceUrlAndTemplateMap = new HashMap<>();

    @PostConstruct
    public void init() {
        dataSourceUrlAndTemplateMap.put("gxsde", defaultJdbcTemplate);
        dataSourceUrlAndTemplateMap.put("pipechart", chartJdbcTemplate);
        dataSourceUrlAndTemplateMap.put("maintain", maintainJdbcTemplate);
//        dataSourceUrlAndTemplateMap.put("unitpipe", unitpipeJdbcTemplate);
//        dataSourceUrlAndTemplateMap.put("unitpipeTemp", unitpipeTempJdbcTemplate);
//        dataSourceUrlAndTemplateMap.put("drawTemp", drawTempJdbcTemplate);
        logger.info("通用属性查询-刷新数据库名与jdbcTemple对应{}", dataSourceUrlAndTemplateMap.keySet());
    }

    /**
     * 获取图层数据库
     *
     * @return
     */
    public Object getDataBaseList() {
        Set<String> keySet = dataSourceUrlAndTemplateMap.keySet();
        return new ArrayList<String>(keySet);
    }

    /**
     * 获取数据库下的图层列表
     *
     * @return
     */
    public Object getTableList(HttpServletRequest request) throws Exception {
        String dataBase = DataConvertUtil.strToStr(request.getParameter("dataBase"));
        JdbcTemplate jdbcTemplate = this.getJdbcTemplate(dataBase);

        String sql = "SELECT " +
                "A.tablename, " +
                "CASE " +
                "WHEN B.geometry_type = 1 THEN " +
                "'point' " +
                "WHEN B.geometry_type = 9 THEN " +
                "'line' " +
                "WHEN B.geometry_type = 11 THEN " +
                "'polygon' " +
                "END as tabletype " +
                "FROM " +
                "pg_tables A, " +
                "sde_geometry_columns B " +
                "WHERE " +
                "A.tablename = B.f_table_name " +
                "AND tablename NOT LIKE 'i%' " +
                "AND tablename NOT LIKE 'gdb_%' " +
                "AND tablename NOT LIKE 'sde_%' " +
                "AND tablename NOT LIKE'sql_%' ORDER BY tablename";
        List<Map<String, Object>> tableList = jdbcTemplate.queryForList(sql);
        return tableList;
    }

    /**
     * 获取字段列表
     *
     * @return
     */
    public Object getColumList(HttpServletRequest request) throws Exception {
        String dataBase = DataConvertUtil.strToStr(request.getParameter("dataBase"));
        String table = DataConvertUtil.strToStr(request.getParameter("table"));
        JdbcTemplate jdbcTemplate = this.getJdbcTemplate(dataBase);

        String sql = " SELECT " +
                " format_type ( A.atttypid, A.atttypmod ) AS TYPE," +
                " A.attname AS NAME " +
                "FROM" +
                " pg_class AS C," +
                " pg_attribute AS A " +
                "WHERE" +
                " C.relname = ? " +
                " AND A.attrelid = C.oid " +
                " AND A.attnum > 0 " +
                " AND A.atttypid in (23,1043,1700,21,23);";
        List<Map<String, Object>> mapList = jdbcTemplate.queryForList(sql, table);
        return mapList;
    }

    /**
     * 查询
     *
     * @return
     */
    public Object search(HttpServletRequest request) throws Exception {
        String dataBase = DataConvertUtil.strToStr(request.getParameter("dataBase"));
        String table = DataConvertUtil.strToStr(request.getParameter("table"));
        JdbcTemplate jdbcTemplate = this.getJdbcTemplate(dataBase);

        String lstOrderByParamStr = request.getParameter("lstOrderByParam");
        String lstOperationParamStr = request.getParameter("lstOperationParam");

        //获取字段信息
        String fieldSql = "SELECT " +
                " A.attname AS NAME, " +
                " concat_ws ( '', T.typname, SUBSTRING ( format_type ( A.atttypid, A.atttypmod ) FROM '\\(.*\\)' ) ) AS TYPE  " +
                "FROM " +
                " pg_class AS C, " +
                " pg_attribute AS A, " +
                " pg_type AS T  " +
                "WHERE " +
                " C.relname = ? " +
                " AND A.attnum > 0  " +
                " AND A.attrelid = C.oid  " +
                " AND A.atttypid = T.oid ";
        List<Map<String, Object>> fieldList = jdbcTemplate.queryForList(fieldSql, table);

        //AES解密
        try {
            lstOrderByParamStr = AESUtil.aesDecrypt(lstOrderByParamStr, AESUtil.KEY);
            lstOperationParamStr = AESUtil.aesDecrypt(lstOperationParamStr, AESUtil.KEY);
        } catch (Exception e) {
            logger.error("通用查询,AES解密失败{}", e.getMessage());
            throw new RuntimeException("AES解密失败");
        }

        int pageNumber = DataConvertUtil.strToInteger(request.getParameter("pageNumber"));
        int pageSize = DataConvertUtil.strToInteger(request.getParameter("pageSize"));
        pageNumber = pageNumber == 0 ? 1 : pageNumber;
        pageSize = pageSize == 0 ? 20 : pageSize;

        String sql = "SELECT * , SDE.ST_ASTEXT (SHAPE) AS WKT FROM " + table + " WHERE 1 = 1";

        List<SearchOperationBean> pipeOperationBeanList = new ArrayList<>();
        //组装条件语句
        String addSql = "";
        if (!StringUtils.isEmpty(lstOperationParamStr)) {
            List<Map<String, Object>> operationBeans = JsonConvertUtil.fromJsonString(lstOperationParamStr, List.class);
            for (Map<String, Object> map : operationBeans) {
                SearchOperationBean pipeOperationBean = new SearchOperationBean();
                pipeOperationBean.setOperation((String) map.get("operation"));
                pipeOperationBean.setParam((String) map.get("param"));
                pipeOperationBean.setSearchValue((String) map.get("searchValue"));
                pipeOperationBeanList.add(pipeOperationBean);
            }
            addSql = this.getWhereSql(pipeOperationBeanList);
            sql = sql + addSql;
        }

        //获取总数
        String countSql = "SELECT COUNT(*) ";
        int i = sql.indexOf("FROM");
        if (i != -1) {
            countSql += sql.substring(i - 1);
        }
        SqlRowSet rs = jdbcTemplate.queryForRowSet(countSql);
        int count = 0;
        while (rs.next()) {
            count = rs.getInt(1);
        }

        //组装排序语句
        if (!StringUtils.isEmpty(lstOrderByParamStr)) {
            List<Map<String, Object>> orderByBeans = JsonConvertUtil.fromJsonString(lstOrderByParamStr, List.class);
            addSql += " ORDER BY ";
            for (Map<String, Object> map : orderByBeans) {
                addSql += (String) map.get("param") + " " + (String) map.get("sortType") + " , ";
            }
            addSql = addSql.substring(0, addSql.lastIndexOf(",") - 1);
            sql = sql + addSql;
        }

        //组装分页语句
        PagingInfo pagingInfo = new PagingInfo(count, pageNumber - 1, pageSize);
        if (pageNumber != -1) {
            sql += pagingInfo.getPagingSqlPg();
        }

        logger.info("通用查询语句：{}", sql.toUpperCase());
        List<Map<String, Object>> lstPage = jdbcTemplate.queryForList(sql);
        for (Map<String, Object> map : lstPage) {
            Object wktValue = map.get("wkt");
            map.put("wkt", OracleSqlUtil.objToString(wktValue));
        }
        Map<String, Object> mapResult = new HashMap<>();
        mapResult.put("records", lstPage);
        mapResult.put("fields", fieldList);
        mapResult.put("pageNumber", pageNumber);
        mapResult.put("pageSize", pagingInfo.getPageSize());
        mapResult.put("totalRecord", pagingInfo.getTotalRecord());
        mapResult.put("totalPage", pagingInfo.getTotalPage());
        return mapResult;
    }

    /**
     * 自定义SQL查询
     *
     * @return
     */
    public Object searchBySql(HttpServletRequest request) throws Exception {
        String dataBase = DataConvertUtil.strToStr(request.getParameter("dataBase"));
        String searchSql = DataConvertUtil.strToStr(request.getParameter("searchStr"));

        //AES解密
        try {
            searchSql = AESUtil.aesDecrypt(searchSql, AESUtil.KEY).toUpperCase();
        } catch (Exception e) {
            logger.error("通用查询,AES解密失败{}", e.getMessage());
            throw new RuntimeException("AES解密失败");
        }

        //检查insert ，update ，delete，drop关键字
        if (searchSql.contains("INSERT") || searchSql.contains("UPDATE") || searchSql.contains("DELETE") || searchSql.contains("DROP")) {
            logger.error("通用自定义查询语句含有非法关键字：{}", searchSql);
            throw new RuntimeException("通用自定义查询语句含有非法关键字");
        }

        logger.info("通用自定义查询语句：{}", searchSql);

        JdbcTemplate jdbcTemplate = this.getJdbcTemplate(dataBase);
        List<Map<String, Object>> mapList = jdbcTemplate.queryForList(searchSql);

        return mapList;
    }

    /**
     * 获取对应jdbcTemplate
     *
     * @param dataBase
     * @return
     */
    private JdbcTemplate getJdbcTemplate(String dataBase) {
        JdbcTemplate jdbcTemplate = null;
        for (Map.Entry<String, JdbcTemplate> entry : dataSourceUrlAndTemplateMap.entrySet()) {
            if (Objects.equals(dataBase, entry.getKey())) {
                jdbcTemplate = entry.getValue();
            }
        }
        if (jdbcTemplate == null) {
            throw new RuntimeException("当前环境无法查询该数据库下的数据");
        }
        return jdbcTemplate;
    }

    /**
     * 组装条件语句
     *
     * @param pipeOperationBeanList
     * @return
     */
    public String getWhereSql(List<SearchOperationBean> pipeOperationBeanList) {
        String addSql = "";
        for (SearchOperationBean pipeOperationBean : pipeOperationBeanList) {
            String operation = pipeOperationBean.getOperation();
            switch (operation) {
                case "包含":
                    addSql += " AND" + " " + pipeOperationBean.getParam() + " LIKE '%" + pipeOperationBean.getSearchValue() + "%'";
                    break;
                case "等于":
                    addSql += " AND" + " " + pipeOperationBean.getParam() + " = '" + pipeOperationBean.getSearchValue() + "'";
                    break;
                case "不包含":
                    addSql += " AND" + " " + pipeOperationBean.getParam() + " NOT LIKE '%" + pipeOperationBean.getSearchValue() + "%'";
                    break;
                case "不等于":
                    addSql += " AND" + " " + pipeOperationBean.getParam() + " <> '" + pipeOperationBean.getSearchValue() + "'";
                    break;
                case "是null":
                    addSql += " AND" + " " + pipeOperationBean.getParam() + " IS NULL";
                    break;
                case "不是null":
                    addSql += " AND" + " " + pipeOperationBean.getParam() + " IS NOT NULL";
                    break;
                case "大于":
                    addSql += " AND" + " " + pipeOperationBean.getParam() + " > " + pipeOperationBean.getSearchValue() + " ";
                    break;
                case "大于等于":
                    addSql += " AND" + " " + pipeOperationBean.getParam() + " >= " + pipeOperationBean.getSearchValue() + " ";
                    break;
                case "小于":
                    addSql += " AND" + " " + pipeOperationBean.getParam() + " < " + pipeOperationBean.getSearchValue() + " ";
                    break;
                case "小于等于":
                    addSql += " AND" + " " + pipeOperationBean.getParam() + " <= " + pipeOperationBean.getSearchValue() + " ";
                    break;
            }
        }
        return addSql;
    }

    /**
     * 点选查询管线要素
     *
     * @param request
     * @return
     * @throws Exception
     */
    public Object queryFeatureByPoint(HttpServletRequest request) {
        TokenManagerService tokenManagerService = SpringContextUtil.getBean(TokenManagerService.class);
        String token = DataConvertUtil.objToStr(request.getParameter("token"));

        Double x = DataConvertUtil.strToDou(request.getParameter("x"));
        Double y = DataConvertUtil.strToDou(request.getParameter("y"));
        //点查询缓冲半径
        Double bufferDistance = DataConvertUtil.strToDou(request.getParameter("bufferDistance"));
        //是否返回要素字段键值对
        boolean isReturnField = Boolean.parseBoolean(DataConvertUtil.strToStr(request.getParameter("isReturnField")));

        List<Map<String, Object>> lstLayerParam = JsonConvertUtil.fromJsonString(request.getParameter("layerParam"), List.class);

        LoginUser loginUser = tokenManagerService.getCurrentLoginUser(token);
        //点的坐标
        logger.info("通用属性查询-点选查询管线要素 x:{}, y:{}, 用户登录名:{}", x, y, loginUser.getLoginName());
        List<Map<String, List<Map<String, Object>>>> lstReturn = new ArrayList<>();
        lstLayerParam.forEach(map -> {
            String dataBase = DataConvertUtil.objToStr(map.get("dataBase"));
            List<String> lstLayerName = (List<String>) map.get("layerNames");
            List<Map<String, List<Map<String, Object>>>> lstLayerResult = this.queryPipeLayerFeatureByPoint(x, y, bufferDistance, lstLayerName, dataBase, isReturnField);
            lstReturn.addAll(lstLayerResult);
        });

        return lstReturn;
    }

    /**
     * 点选查询函数
     *
     * @param x
     * @param y
     * @param bufferDistance
     * @param lstLayerName
     * @param dataBase
     * @return
     */
    public List<Map<String, List<Map<String, Object>>>> queryPipeLayerFeatureByPoint(Double x, Double y, Double bufferDistance, List<String> lstLayerName, String dataBase, boolean isReturnField) {
        JdbcTemplate jdbcTemplate = this.getJdbcTemplate(dataBase);
        PipeQueryUtil.logByJdbcTemplate(jdbcTemplate, dataBase);

        //获取查询结果
        List<Map<String, List<Map<String, Object>>>> mapLayerFeatureList = new ArrayList<>();
        //多线程查询类
        CommonSearchByPointThread commonSearchByPointThread = new CommonSearchByPointThread();

        if (!CollectionUtils.isEmpty(lstLayerName)) {
            commonSearchByPointThread.getQueryLayerIds().addAll(lstLayerName);
        }

        commonSearchByPointThread.initQueryLayer();
        commonSearchByPointThread.setJdbcTemplate(jdbcTemplate);
        //设置点
        commonSearchByPointThread.setX(x);
        commonSearchByPointThread.setY(y);
        commonSearchByPointThread.setBufferDistance(bufferDistance);
        commonSearchByPointThread.setSchame(dataBase);
        commonSearchByPointThread.setReturnField(isReturnField);

        //创建3个固定数量的线程-sde连接池可能会异常还是小点好
        ExecutorService fixedThreadPool = Executors.newFixedThreadPool(3);
        for (int i = 0; i < commonSearchByPointThread.getThreadCount(); i++) {
            Thread thread = new Thread(commonSearchByPointThread);
            fixedThreadPool.execute(thread);
        }
        //关闭线程池
        fixedThreadPool.shutdown();

        while (true) {
            try {
                if (!!fixedThreadPool.awaitTermination(1, TimeUnit.SECONDS)) {
                    break;
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        logger.info("-- 查询线程执行完毕 --");

        //获取查询结果
        mapLayerFeatureList.add(commonSearchByPointThread.getMapLayerFeature());
        return mapLayerFeatureList;
    }
}
