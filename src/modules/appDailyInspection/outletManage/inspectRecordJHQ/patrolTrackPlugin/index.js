let template = require('./content.html');
let eventHelper = require('utils/eventHelper.js');
let mathUtils = require('utils/mathUtils.js');
let simplify = require('utils/simplify');
var inspectRecord = require('services/inspectRecord');
var moment = require('moment')

let comm = Vue.extend({
    template: template,
    data: function () {
        return {
            dialogVisible: false,//轨迹回放插件显示控制
            apiInstance: {},//地图工具类
            baseView: {},//地图视图类
            baseMap: {},//地图图层管理类
            tableData: [],//轨迹数据
            traceLineLayer: {},//地图轨迹展示图层
            traceArrowLayer: {},//地图轨迹展示图层
            tracePersonLayer:{},//地图轨迹人员图标名称展示图层
            trackInterval: {},//地图轨迹播放定时器
            countNum: 0,//轨迹当前播放下标值
            userAttendInfo: {},//当前查看的用户信息和打卡信息
            playerState: "stop",//轨迹回放播放指示值
            canMapGoTo:false,//地图操作指示器（防止地图绘制中进行放大缩小拖拽等操作引起的地图展示异常）
            facilityMaintDict:{},//设施养护问题上报字典
            riverReportDict:{},//河道巡查问题上报字典
            pumpInspectDict:{},//泵闸站巡检问题上报字典
            iconPath : "M 0 0 L 150 0 L 300 150 L 150 300 L 0 300 L 150 150 L 0 0 ",//地图绘制箭头svg路径
            realMapLine:[],//原始轨迹坐标数据
            reportDate:[],
        }
    },
    methods: {
        /***
         *  初始化获取地图对象
         *  设置地图操作事件
         * @param apiInstance
         * @param baseView
         * @param baseMap
         */
        initPlugin (apiInstance, currentMap, baseView) {
            this.apiInstance = apiInstance;
            this.currentMap = currentMap;
            this.baseView = baseView;
            this.baseMap = this.$parent.$refs.baseMap;
            console.log(apiInstance)
            console.log(baseView)
            this.traceLineLayer = this.apiInstance.createGraphicsLayer(this.baseView.map, this.baseView, "traceLine");
            this.traceArrowLayer = this.apiInstance.createGraphicsLayer(this.baseView.map, this.baseView, "traceArrow");
            this.tracePersonLayer = this.apiInstance.createGraphicsLayer(this.baseView.map, this.baseView, "tracePerson");
            //监听地图是否执行动作
            this.baseView.watch("animation", (response) =>{
                if(response && response.state === "running"){
                    this.canMapGoTo = false;
                } else{
                    this.canMapGoTo = true;
                }
            });
            //地图缩放后的箭头和轨迹线重绘
            this.baseView.watch("zoom", ()=>{
                if(this.dialogVisible) {
                    this.traceArrowLayer.removeAll();

                    //获取当前缩放比例屏幕像素地图坐标之比
                    let prePxToMap = this.calcPerPxToMap();
                    //轨迹数据简化处理
                    let simplifyMapLine = simplify(this.realMapLine, prePxToMap, true);

                    if (simplifyMapLine.length > 0) {
                        //遍历折线每段生成箭头
                        simplifyMapLine.forEach((item, index) => {
                            //如果循环到最后一个退出
                            if (simplifyMapLine.length > index + 1)
                                this.traceDrawArrow(simplifyMapLine[index], simplifyMapLine[index + 1]);
                        });
                    }

                    //重绘轨迹线
                    let traceLine = this.traceLineLayer.graphics.getItemAt(0);

                    let lineGeometry = new apiInstance.Polyline({
                        paths: [simplifyMapLine],
                        spatialReference: this.traceLineLayer.spatialReference
                    });

                    if(traceLine){
                        traceLine.geometry = lineGeometry;
                    }
                }
            });
        },
        /***
         * 根据两点坐标 进行地图箭头展示
         * @param startMapPoint
         * @param endMapPoint
         * @param iconPath
         */
        traceDrawArrow (startMapPoint, endMapPoint) {
            let startPixelPoint = this.baseView.toScreen(new this.apiInstance.Point({
                x: startMapPoint[0],
                y: startMapPoint[1],
                spatialReference: this.baseView.spatialReference
            }));
            let endPixelPoint = this.baseView.toScreen(new this.apiInstance.Point({
                x: endMapPoint[0],
                y: endMapPoint[1],
                spatialReference: this.baseView.spatialReference
            }));
            let pixelDistanc = mathUtils.getDistance(startPixelPoint, endPixelPoint);
            //两点间距太小，不绘制箭头
            console.log(pixelDistanc)
            if(pixelDistanc<=20)
                return;
            let heading = mathUtils.getAngle(startMapPoint[0], startMapPoint[1], endMapPoint[0], endMapPoint[1]);
            //计算箭头角度异常，不绘制箭头
            if (!mathUtils.isNumber(heading))
                return;
            let centerMapPoint = [(startMapPoint[0] + endMapPoint[0]) / 2, (startMapPoint[1] + endMapPoint[1]) / 2];
            let point = this.apiInstance.createPoint(centerMapPoint[0], centerMapPoint[1], this.baseView.spatialReference);
            let markerSymbol = new this.apiInstance.SimpleMarkerSymbol({
                angle: heading,
                path: this.iconPath,
                color: "white",
                size: "10px",
                outline: {
                    width: 0
                }
            });
            let markPoint = new this.apiInstance.Graphic({
                geometry: point,
                symbol: markerSymbol
            });
            this.traceArrowLayer.add(markPoint);
        },
        /***
         * 加载指定打卡轨迹信息
         * @param item
         */
        obtainAndDrawTrack (item, visible) {
            this.userAttendInfo = item;
            // this.closePatrolTrackPlugin(visible);
            let sendParam= {
                dailyPatrolDayId: item.id,
                reportStartDate: (this.reportDate || [])[0]?moment(this.reportDate[0]).format('YYYY-MM-DD HH:mm:ss'):"",
                reportEndDate: (this.reportDate || [])[1]?moment(this.reportDate[1]).format('YYYY-MM-DD HH:mm:ss'):"",
            };
            inspectRecord.dailyPatrolFindLstXAndY(sendParam, (data)=>{
                clearInterval(this.trackInterval);
                data.forEach((child) => {
                    child.uploadTime = moment(child.reportDate).format('YYYY-MM-DD HH:mm:ss')
                });

                this.countNum = 0;
                this.tableData = data;

                //轨迹数据格式化
                this.realMapLine = this.tableData.map((child) =>{
                    return [child.x,child.y];
                });
                //获取当前缩放比例屏幕像素地图坐标之比
                let prePxToMap = this.calcPerPxToMap();
                //轨迹数据简化处理
                let simplifyMapLine = simplify(this.realMapLine, prePxToMap, true);
                //地图绘制轨迹折线
                let styleObj = {
                    color: [255, 0, 0],
                    width: 5,
                    cap: "round",
                    join: "round",
                };
                // console.log(this.traceLineLayer)
                let traceLine = this.apiInstance.createPolyline(this.traceLineLayer, simplifyMapLine, styleObj);
                // console.log(traceLine)
                try {
                    this.baseView.goTo({
                        target: traceLine.geometry.extent.expand(2),
                        zoom:6,
                    });
                }catch (e) {
                    console.log(e)
                }
                //遍历折线每段生成箭头
                simplifyMapLine.forEach((item,index)=>{
                    //如果循环到最后一个退出
                    if(simplifyMapLine.length>index+1)
                        this.traceDrawArrow(simplifyMapLine[index],simplifyMapLine[index + 1]);
                });

                this.dialogVisible = true;
                try {
                    this.drawPerson();
                }catch (e) {
                    console.log(e)
                }
                this.resetInterval()
            });
        },
        /***
         * 轨迹数据表格点击事件
         * @param row
         * @param event
         * @param column
         */
        rowClick (row, event, column) {
            if (this.pointGraphic && this.pointGraphic.geometry) {
                this.traceLineLayer.remove(this.pointGraphic);
                this.pointGraphic = {};
            }
            var point = this.apiInstance.createPoint(row.x, row.y, this.baseView.spatialReference);
            var newView = {
                zoom: 16,
                target: point
            };
            var markerSymbol = {
                url: "../../../img/complaintManage/gr.png",
                width: "24px",
                height: "24px"
            };
            this.pointGraphic = this.apiInstance.createGeometry(this.traceLineLayer, point, markerSymbol);
            this.baseView.goTo(newView);
        },
        /***
         * 在地图绘制人员信息
         */
        drawPerson () {
            //当地图无动作时候执行
            if(this.canMapGoTo){
                if (this.tableData.length <= this.countNum)
                    this.countNum = 0;

                let iconGraphics = this.tracePersonLayer.graphics.filter((graphic)=>{
                    return graphic.attributes.type=="tracePersonIcon";
                });
                if(iconGraphics.length>0) {
                    let point = this.apiInstance.createPoint(this.tableData[this.countNum].x, this.tableData[this.countNum].y, this.baseView.spatialReference);
                    let iconGraphic = iconGraphics.getItemAt(0);
                    iconGraphic.geometry = point;
                } else {
                    let point = this.apiInstance.createPoint(this.tableData[this.countNum].x, this.tableData[this.countNum].y, this.baseView.spatialReference);
                    let markerSymbol = {
                        type:"picture-marker",
                        url: "../../../img/complaintManage/gr.png",
                        width: "32px",
                        height: "32px",
                    };
                    let pointGraphic = new this.apiInstance.Graphic({
                        geometry: point,
                        symbol: markerSymbol,
                        attributes: {type:"tracePersonIcon"},
                    });
                    this.tracePersonLayer.add(pointGraphic)
                }

                let textGraphics = this.tracePersonLayer.graphics.filter((graphic)=>{
                    return graphic.attributes.type=="tracePersonText";
                });
                if(textGraphics.length>0) {
                    let point = this.apiInstance.createPoint(this.tableData[this.countNum].x, this.tableData[this.countNum].y, this.baseView.spatialReference);
                    let textGraphic = textGraphics.getItemAt(0);
                    textGraphic.geometry = point;
                } else {
                    let point = this.apiInstance.createPoint(this.tableData[this.countNum].x, this.tableData[this.countNum].y, this.baseView.spatialReference);
                    let textObj = {
                        type:"text",
                        color: '#1d5aa8',
                        text: this.userAttendInfo.reportUserName,
                        font: {
                            size: 18
                        },
                        yoffset: -25,
                    };
                    let pointGraphic = new this.apiInstance.Graphic({
                        geometry: point,
                        symbol: textObj,
                        attributes: {type:"tracePersonText"},
                    });
                    this.tracePersonLayer.add(pointGraphic)
                }

                this.countNum++;
            }
        },
        /***
         * 关闭轨迹回放插件
         */
        closePatrolTrackPlugin (visible) {
            console.log(visible)
            console.log(!visible)
            if(!visible){
                this.dialogVisible = false;
            }

            this.tableData = [];
            clearInterval(this.trackInterval);
            this.trackInterval = {};
            eventHelper.emit('remove-bubble-byType', 'patrolTrackBubble');
            this.traceLineLayer.removeAll();
            this.traceArrowLayer.removeAll();
            this.tracePersonLayer.removeAll();
        },
        /***
         * 停止播放轨迹回放
         */
        stopPlay () {
            clearInterval(this.trackInterval);
            this.trackInterval = {};
            this.playerState = 'stop';
        },
        /***
         * 播放轨迹回放
         */
        startPlay () {
            this.trackInterval = setInterval(this.drawPerson, 500);
            this.playerState = 'play';
        },
        /***
         * 重新播放轨迹回放
         */
        resetInterval () {
            this.stopPlay();
            this.countNum = 0;
            this.drawPerson();
        },
        /***
         * 获取一个像素对应地图坐标差值
         */
        calcPerPxToMap (){
            let startPoint = this.baseView.toMap({x:0,y:0});
            let endPoint = this.baseView.toMap({x:1,y:0});
            let perPxToMap = endPoint.x - startPoint.x;
            return perPxToMap;
        }
    },
    mounted: function () {
        eventHelper.on('obtainAndDrawTrack', function (item) {
            this.reportDate = [item.reportStartDate, item.reportEndDate];
            this.dialogVisible = true;
            this.obtainAndDrawTrack(item);
        }.bind(this));
    }
});
module.exports = comm;