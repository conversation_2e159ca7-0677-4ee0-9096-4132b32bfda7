<div class="right-bottom-panel small serviceAttribute" style="height:50%;" v-show="showFacility">
    <div class="x-panel x-full drainage-list-content main-child-right">
        <!--<div class="goBack small" @click="closeDialog"><a href='javascript:void(0);' class="el-icon-d-arrow-right" ></a></div>-->
        <div class="x-panel-top panel-header" style="height: 43px;">
            <a class="close-panel" href="javascript:void(0);" @click="closeDialog"><i class="el-icon-close"></i></a>
            <div class="header-font header-pad" v-if="title">
                服务信息查询
            </div>
            <div class="header-font header-pad" v-else>
                暂时没有数据
            </div>
            <!--<div class="head-search" style="background-color: #fff;padding: 0 10px 7px;">-->
            <!--<el-input-->
            <!--placeholder="请输入"-->
            <!--class="search-street"-->

            <!--clearable>-->
            <!--&lt;!&ndash;v-model="name"&ndash;&gt;-->
            <!--&lt;!&ndash;@keyup.native.enter="streetSearchEnter(name)"&ndash;&gt;-->
            <!--</el-input>-->
            <!--</div>-->
        </div>
        <div class="el-tabs">
            <el-tabs type="border-card" v-model="tabsValue" closable @tab-remove="removeTab">
                <el-tab-pane label="水系" v-if="riverPanel" :name="tabs[0].value">
                    <div v-if="riverDataShow" class="inp-label">
                        <div v-for="(item,key) in riverData">
                            <el-input placeholder="暂无数据" :value="item" v-model="riverData[key]">
                                <template slot="prepend">{{key}}</template>
                            </el-input>
                        </div>
                        <div class="el-tabs-bottom"></div>
                    </div>
                    <span v-else style="font-size: 2em;">没有查询到相关数据...</span>
                </el-tab-pane>
                <el-tab-pane label="湖泊" v-if="lakePanel" :name="tabs[1].value">
                    <div v-if="lakeDataShow" class="inp-label">
                        <div v-for="(item,key) in lakeData">
                            <el-input placeholder="暂无数据" :value="item" v-model="lakeData[key]">
                                <template slot="prepend">{{key}}</template>
                            </el-input>
                        </div>
                        <div class="el-tabs-bottom"></div>
                    </div>
                    <span v-else style="font-size: 2em;">没有查询到相关数据...</span>
                </el-tab-pane>
                <el-tab-pane label="流域" v-if="basinPanel" :name="tabs[2].value">
                    <div v-if="basinDataShow" class="inp-label">
                        <div v-for="(item,key) in basinData">
                            <el-input placeholder="暂无数据" :value="item" v-model="basinData[key]">
                                <template slot="prepend">{{key}}</template>
                            </el-input>
                        </div>
                        <div class="el-tabs-bottom"></div>
                    </div>
                    <span v-else style="font-size: 2em;">没有查询到相关数据...</span>
                </el-tab-pane>
                <el-tab-pane label="交界断面" v-if="crossPanel" :name="tabs[3].value">
                    <div v-if="crossDataShow" class="inp-label">
                        <div v-for="(item,key) in crossData">
                            <el-input placeholder="暂无数据" :value="item" v-model="crossData[key]">
                                <template slot="prepend">{{key}}</template>
                            </el-input>
                        </div>
                        <div class="el-tabs-bottom"></div>
                    </div>
                    <span v-else style="font-size: 2em;">没有查询到相关数据...</span>
                </el-tab-pane>
                <el-tab-pane label="门牌地址信息" v-if="addressPanel" :name="tabs[4].value">
                    <div v-if="addressDataShow" class="inp-label">
                        <div v-for="(item,key) in addressData">
                            <el-input placeholder="暂无数据" :value="item" v-model="addressData[key]"
                                      :title="addressData[key]">
                                <template slot="prepend">{{key}}</template>
                            </el-input>
                        </div>
                        <div class="el-tabs-bottom"></div>
                    </div>
                    <span v-else style="font-size: 2em;">没有查询到相关数据...</span>
                </el-tab-pane>
                <el-tab-pane label="用水户信息" v-if="userPanel" :name="tabs[5].value">
                    <div v-if="userDataShow" class="inp-label">
                        <el-collapse v-model="activeNames">
                            <el-collapse-item :title="user['用户名']" :name="index" v-for="user,index in userData">
                                <div class="userPanel">
                                    <span class="columnName">用户名</span><span class="columnValue">{{user['用户名']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">出厂编号</span><span
                                        class="columnValue">{{user['出厂编号']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">序号</span><span class="columnValue">{{user['序号']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">日均用水量</span><span
                                        class="columnValue">{{user['日均用水量']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">水表地址</span><span
                                        class="columnValue">{{user['水表地址']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">用户编号</span><span
                                        class="columnValue">{{user['用户编号']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">纬度</span><span class="columnValue">{{user['纬度']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">经度</span><span class="columnValue">{{user['经度']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">营业区域</span><span
                                        class="columnValue">{{user['营业区域']}}</span>
                                </div>
                            </el-collapse-item>
                        </el-collapse>
                        <div class="el-tabs-bottom"></div>
                    </div>
                    <span v-else style="font-size: 2em;">没有查询到相关数据...</span>
                </el-tab-pane>
                <el-tab-pane label="厂站信息" v-if="facilityPanel" :name="tabs[6].value">
                    <div v-if="facilityDataShow" class="inp-label">
                        <el-collapse v-model="activeNames">
                            <el-collapse-item :title="pump['name']" :name="index" v-for="pump,index in pumpData">
                                <div class="userPanel">
                                    <span class="columnName">泵站名字</span><span
                                        class="columnValue">{{pump['name']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">地址</span><span
                                        class="columnValue">{{pump['addr']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">所在污水系统</span><span class="columnValue">{{pump['sewagesystem_id']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">所属工程</span><span class="columnValue">{{pump['project_name']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">泵站类别</span><span
                                        class="columnValue">{{pump['bz_sort']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">设计规模</span><span
                                        class="columnValue">{{pump['desginsize']}}（万m³/日）</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">扬程</span><span
                                        class="columnValue">{{pump['rangesize']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">设计运行水位</span><span
                                        class="columnValue">{{pump['desginwaterline']}}m</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">最低水位</span><span
                                        class="columnValue">{{pump['minwaterline']}}m</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">瞬时流量</span><span
                                        class="columnValue">{{pump['instraffic']}}m³/s</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">在用泵数</span><span
                                        class="columnValue">{{pump['pumpnum']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">备用泵数</span><span
                                        class="columnValue">{{pump['pumpbackupnum']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">每台泵扬程</span><span
                                        class="columnValue">{{pump['pumprangesize']}}m</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">每台泵流量</span><span
                                        class="columnValue">{{pump['traffic']}}㎡/h</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">每台泵功率</span><span
                                        class="columnValue">{{pump['power']}}kw</span>
                                </div>
                            </el-collapse-item>
                            <el-collapse-item :title="sewage['name']" :name="index" v-for="sewage,index in sewageData">
                                <div class="userPanel">
                                    <span class="columnName">污水厂名称</span><span
                                        class="columnValue">{{sewage['name']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">地址</span><span
                                        class="columnValue">{{sewage['addr']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">设计规模</span><span
                                        class="columnValue">{{sewage['designs_cope']}}（万m³/日）</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">初雨规模</span><span
                                        class="columnValue">{{sewage['初雨规模']}}（万m³/日）</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">出水标准</span><span
                                        class="columnValue">{{sewage['出水标准']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">污水处理工艺</span><span
                                        class="columnValue">{{sewage['technical']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">目标控制水位</span><span
                                        class="columnValue">{{sewage['目标控制水位']}}m</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">日处理水量</span><span
                                        class="columnValue">{{sewage['treatmentCapacity']}}万吨 ({{updateTime}})</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">COD</span><span
                                        class="columnValue">{{sewage['cod']}}mg/L ({{updateTime}})</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">氨氮</span><span
                                        class="columnValue">{{sewage['ammonia']}}mg/L ({{updateTime}})</span>
                                </div>

                                <div class="userPanel">
                                    <span class="columnName">排入河涌</span><span
                                        class="columnValue">{{sewage['river']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">权属单位</span><span
                                        class="columnValue">{{sewage['ownerdept']}}</span>
                                </div>
                                <div class="userPanel">
                                    <span class="columnName">管理单位</span><span
                                        class="columnValue">{{sewage['managedept']}}</span>
                                </div>
                            </el-collapse-item>
                        </el-collapse>
                        <div class="el-tabs-bottom"></div>
                    </div>
                    <span v-else style="font-size: 2em;">没有查询到相关数据...</span>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</div>

