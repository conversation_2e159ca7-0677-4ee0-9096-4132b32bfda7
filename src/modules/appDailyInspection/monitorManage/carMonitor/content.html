<div class="carMonitor" v-show="showRealTimeMonitor">
    <el-card class="box-card">
        <div slot="header" class="carMonitorTitle">
            车辆出勤实时监测
            <i class="el-icon-close" @click="closeRealTimeMonitor"></i>
        </div>
        <div style="padding: 0 10px 5px">
            <el-table
                    :data="tableData"
                    v-loading="isLoading"
                    :height="304"
                    :header-cell-style="{color:'#333333','font-weight':'bold'}"
                    @row-click="rowClick">
                <el-table-column
                        align="center"
                        prop="carNum"
                        label="车牌号">
                </el-table-column>
                <el-table-column
                        align="center"
                        prop="locationTime"
                        label="更新时间">
                </el-table-column>
                <el-table-column
                        align="center"
                        prop="departmentTreeName"
                        label="所属部门">
                </el-table-column>
            </el-table>
        </div>
    </el-card>
</div>