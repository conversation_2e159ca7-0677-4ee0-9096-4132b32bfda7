var template = require('./content.html');
var eventHelper = require('utils/eventHelper');
var facilityMaintController = require('controllers/facilityMaintController');

var moment = require('moment');
// 定义组件
var comm = Vue.extend({
    template: template,
    data: function () {
        const startDay = moment().format("YYYY-MM-DD");
        const endDay = moment().format("YYYY-MM-DD");
        return {
            maintUserAttendStaticRecords: [],
            newMaintUserAttendStaticRecords: [],
            tableHeight: 0,
            selectDate: [startDay, endDay],
            regionUuid:"",// 所属片区
            userName:"",// 人员名称

            areaSystemList:[],// 片区列表
            pageSize: 10,
            pageNumber: 1,
            isRecordLoading: false,
            totalRecord: 0,
            teamRegionDict:{},
        }
    },
    methods: {
        getDict(){
            // facilityMaintController.initMainTeamDirt({}, function (result) {
            //     result.forEach((item) => {
            //         this.teamRegionDict[item.value] = item.text
            //     })
            // }.bind(this),(err) => {
            //     this.$message.error('获取字典失败，请稍后再试')
            // })

            this.isRecordLoading = true;
            var promise = new Promise((resolve, reject) => {
                facilityMaintController.initMainTeamDirt({}, function (result) {
                    this.areaSystemList=result;
                    resolve()
                }.bind(this),(err) => {
                    this.isRecordLoading = false;
                    this.$message.error('获取字典失败，请稍后再试')
                    reject()
                })
            })
            promise.then((value) => {
                this.initTableDate();
            })
        },
        searchInit(){
            this.pageNumber = 1;
            this.initTableDate();
        },
        initTableDate: function () {
            this.isRecordLoading = true;
            facilityMaintController.maintUserAttendListPage({
                startDay:(this.selectDate|| [])[0]?moment(this.selectDate[0]).format('YYYY-MM-DD'):"",
                endDay:(this.selectDate|| [])[1]?moment(this.selectDate[1]).format('YYYY-MM-DD'):"",
                teamRegionName: this.regionUuid,
                teamUserName: this.userName,
            }, function (result) {
                var filterData = result.filter((item) => {
                    if(item.inspectDistance !== 0 && !!item.inspectDistance){
                        item.inspectDistance = item.inspectDistance.toFixed(3)
                    }
                    return !!item.name
                });
                this.maintUserAttendStaticRecords = filterData;
                this.newMaintUserAttendStaticRecords = this.maintUserAttendStaticRecords.filter((item, index) => {return ((index >= (this.pageNumber * this.pageSize - this.pageSize)) && (index < (this.pageNumber * this.pageSize)))})
                this.totalRecord = filterData.length;
                this.isRecordLoading = false;
            }.bind(this), (error) => {
                console.log(error);
            });
        },
        formatterRegion(row){
            return this.teamRegionDict[row.teamRegion]
        },
        calcHeight: function () {
            this.tableHeight = $(".maintUserAttend").height() - 130;
        },
        formatAttendStatus: function (row, column) {
            if (row.attendStatus == 1)
                return "正常出勤";
            else if (row.attendStatus == 0)
                return "离线";
            else
                return "未知";
        },
        sizeChange: function (pageSize) {
            this.pageSize = pageSize;
            // this.initTableDate();
            this.newMaintUserAttendStaticRecords = this.maintUserAttendStaticRecords.filter((item, index) => {return ((index >= (this.pageNumber * this.pageSize - this.pageSize)) && (index < (this.pageNumber * this.pageSize)))})
        },
        currentChange: function (pageNumber) {
            this.pageNumber = pageNumber;
            // this.initTableDate();
            this.newMaintUserAttendStaticRecords = this.maintUserAttendStaticRecords.filter((item, index) => {return ((index >= (this.pageNumber * this.pageSize - this.pageSize)) && (index < (this.pageNumber * this.pageSize)))})
        },
    },
    mounted: function () {
        this.$nextTick(() => {
            this.calcHeight();
        });
        this.getDict();

    },
    components: {}
});
module.exports = comm;