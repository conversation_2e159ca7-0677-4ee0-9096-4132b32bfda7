var template = require('./content.html');
var eventHelper = require('utils/eventHelper');
var serviceHelper = require('services/serviceHelper');
// var carService = require('services/carService');

var comm = Vue.extend({
    template: template,
    data: function () {
        return {
            carDispatchTable:[],//车辆调度表格
            carDispatchTitle:'',//弹窗标题
            carDispatchDialog:false,//车辆调度修改弹窗开关
            isLoading:false,//loading开关
            saveBtn:'新增',//按钮类型
            carId:'',//车辆id
            //车辆调度弹窗表单
            carDispatchForm:{
                id:'',//  空值表示新增，否则修改
                carId :'',//   车辆编号
                useDate:'',//   使用日期
                useDepartment:'',//   使用部门编号
                useTeam:'',//  使用班组编号
                reventDate :'',//  归还日期
                departmentName:'',
                tameName:''
            },
            useTeamtOpts:[],//团队下拉列表参数
            useDepartmentOpts:[],//部门下拉列表参数
            currentPage:1,//当前页码
            pageSize:10,//页码数量
            total:0,//表格的数量
        }
    },
    methods: {
        changeTeamName(){
            this.carDispatchForm.useTeam = this.carDispatchForm.tameName;
        },
        //改变每页的数量触发，重新获取数据
        handleSizeChange(val) {
            this.pageSize = val;
            this.loadCarDispatchData(this.carId);
        },
        //改变页码的数量触发，重新获取数据
        handleCurrentChange(val) {
            this.currentPage = val;
            this.loadCarDispatchData(this.carId);
        },
        /**
         * 打开车辆调度弹窗，初始化弹窗状态
         * @param type
         * @param carDispatch
         */
        openDialog(type,carDispatch){
            //新增车辆调度
            if(type==1){
                this.carDispatchTitle = '新增车辆调度';
                this.carDispatchDialog = true;
                this.saveBtn = '新增';
                //新增初始化表单
                this.carDispatchForm = {
                        id:'',//  空值表示新增，否则修改
                        carId :this.carId,//   车辆编号
                        useDate:'',//   使用日期
                        useDepartment:'',//   使用部门编号
                        useTeam:'',//  使用班组编号
                        reventDate :'',//  归还日期
                        departmentName:'',//部门名称
                        tameName:''//班组名称
                };
            }
            //修改车辆调度
            if(type == 2){
                this.carDispatchTitle = '修改车辆调度';
                this.carDispatchDialog = true;
                this.saveBtn = '修改';
                this.carDispatchForm = carDispatch;
                // console.log(this.carDispatchForm);
            }
        },
        /***
         * 修改或者新增车辆调度信息
         * @param picId
         */
        saveCarInfo(){
            // carService.saveCarDispatch(this.carDispatchForm,(res)=>{
            //     this.carDispatchDialog = false;
            //     if( this.saveBtn == '修改'){
            //         this.$message({
            //             message: '车辆信息修改成功',
            //             type: 'success'
            //         });
            //     }else {
            //         this.$message({
            //             message: '新增车辆信息成功',
            //             type: 'success'
            //         });
            //     }
            //     this.loadCarDispatchData(this.carId);
            // },(err)=>{
            //     this.$message.error('error:'+err);
            // });
        },
        //改变部门名称，从字典里获取对应的班组下拉列表
        changeDepartment(){
            this.carDispatchForm.useDepartment = this.carDispatchForm.departmentName;
            // carService.getAllIdAndName(this.carDispatchForm.departmentName,(res)=>{
            //     this.useTeamtOpts = res;
            // },(err)=>{
            //     this.$message.error('error:'+err);
            // })
        },
        /**
         * 根据车辆id获取车辆调度列表
         * @param carId
         */
        loadCarDispatchData(carId){
            let params = {};
            params.carId = carId;
            params.pageSize = this.pageSize;
            params.pageNumber = this.currentPage;
            this.carDispatchTable = [];
            this.isLoading = true;
            // carService.loadCarDispatchData(params,res=>{
            //     this.carDispatchTable = res.records;
            //     this.isLoading = false;
            // },err=>{
            //     this.$message.error('error:'+err);
            //     this.isLoading = false;
            // })
        },
        /**
         * 当前组件打开时触发，初始化参数
         * @param carId
         * @param departmentDict
         */
        open(carId,departmentDict){
            this.carId = carId;
            if(!!departmentDict){
                this.useDepartmentOpts = departmentDict;
            }
            this.loadCarDispatchData(carId);
        }
    },
    mounted: function () {

    },
    components: {

    }

});

module.exports = comm;
