var template = require('./content.html');
var eventHelper = require('utils/eventHelper');
var carService = require('services/carService');
var serviceHelper = require('services/serviceHelper');
var moment = require('moment');

var comm = Vue.extend({
    template: template,
    data: function () {
        return {
            //车辆信息表格，展示使用
            carInfoForm:{
                carNum:'',
                state:'',
                stateName:'',
                department:'',
                departmentName:'',
                carName:'',
                driverPerson:'',
                driverPersonName:'',
                carType:'',
                carTypeName:'',
                color:'',
                businessLicenseNo:'',
                ratePassenger:'',
                loadWeight:'',
                remark:'',
            },
            //最近一条保养记录
            carMaintainForm:{
                carId:'',//车辆ID
                maintainDate:'',//保养日期
                curKiloMetre:'',//当前公里数
                lastMaintainDate:'',//上次保养日期
                lastMaintainKiloMetre:'',//上次保养里程数
                nextMaintainKiloMetre:'',//下次保养里程数
                maintainUser:'',//责任人编号
                maintainUserName:'',
                remark:'',//备注
            },
            carMaintainTable:[],
            //新增或修改的保养记录
            carMaintainRecordForm:{
                id:'',
                carId:'',//车辆ID
                maintainDate:'',//保养日期
                curKiloMetre:'',//当前公里数
                lastMaintainDate:'',//上次保养日期
                lastMaintainKiloMetre:'',//上次保养里程数
                nextMaintainKiloMetre:'',//下次保养里程数
                maintainUser:'',//责任人编号
                remark:'',//备注
            },
            carMaintainTitle:'',//车辆保养新增或保存弹窗标题
            carMaintainDialog:false,//弹窗开关
            saveBtn:'新增',//按钮类型
            //车辆保养时间查询条件,初始化
            carMainRecord:{
                startTime:'2018-01-01',
                endTime:moment().format('YYYY-MM-DD')
            },
            showCarMaintainList:false,//车辆保养历史表开关
            currentPage:1,//当前页码
            pageSize:10,//页码数量
            total:0,//初始化页码总数
            isLoading:false,//loading开关
            personOpts:[],//人员参数列表
        }
    },
    methods: {
        /**
         * 打开当前页执行初始化车辆信息和最新的保养信息
         * @param carDetail
         */
        openCarManageDetail(carDetail, personOpts){
            this.personOpts = personOpts
            this.showCarMaintainList = false;
            this.carInfoForm = carDetail;
            let params = {};
            params.carInfoId = this.carInfoForm.id;
            this.carId = this.carInfoForm.id;
            this.isLoading = true;
            carService.findOneCarMaintain(params,(res)=>{
                //如果有数据，前端添加单位km
                if(!!res.curKiloMetre){
                    res.curKiloMetre = res.curKiloMetre+'km';
                }
                if(!!res.lastMaintainKiloMetre){
                    res.lastMaintainKiloMetre = res.lastMaintainKiloMetre+'km';
                }
                if(!!res.nextMaintainKiloMetre){
                    res.nextMaintainKiloMetre = res.nextMaintainKiloMetre+'km';
                }
                this.carMaintainForm = res;
                this.isLoading = false;
            },err=>{
                this.isLoading = false;
                this.$message.error('error:' + err);
            });
        },
        /**
         * 初始话新增或修改弹窗
         * @param type
         * @param carMaintain
         */
        openDialog(type,carMaintain){
            //新增状态，初始化数据
            if(type==1){
                this.carMaintainTitle = '新增车辆保养';
                this.carMaintainDialog = true;
                this.saveBtn = '新增';
                //新增初始化表单
                this.carMaintainRecordForm = {
                    id:'',
                    carInfoId:this.carId,//车辆ID
                    maintainDate:'',//保养日期
                    curKiloMetre:'',//当前公里数
                    lastMaintainDate:'',//上次保养日期
                    lastMaintainKiloMetre:'',//上次保养里程数
                    nextMaintainKiloMetre:'',//下次保养里程数
                    maintainUser:'',//责任人编号
                    remark:'',//备注
                };
            }
            //修改状态，初始化数据
            if(type == 2){
                this.carMaintainTitle = '修改车辆保养';
                this.carMaintainDialog = true;
                this.saveBtn = '修改';
                this.carMaintainRecordForm = carMaintain;
            }
        },
        /**
         * 增加或修改车辆保养信息
         */
        saveCarMaintain(){
            carService.saveCarMaintain(this.carMaintainRecordForm,(res)=>{
                this.carMaintainDialog = false;
                if( this.saveBtn == '修改'){
                    this.$message({
                        message: '车辆信息修改成功',
                        type: 'success'
                    });
                }else {
                    this.$message({
                        message: '新增车辆信息成功',
                        type: 'success'
                    });
                    /**
                     * 新增后，重新获取最新的保养记录
                     * @type {{}}
                     */
                    let params = {};
                    params.carInfoId = this.carId;
                    carService.findOneCarMaintain(params,(res)=>{
                        this.carMaintainForm = res;
                    },err=>{
                        this.$message.error('error:' + err);
                    });
                }
                this.loadCarMaintainData(this.carId);
            },(err)=>{

            })
        },
        // 车辆保养记录表
        openCarMaintain(){
            this.showCarMaintainList = true;
            this.loadCarMaintainData(this.carId);
        },


        //车辆保养时间筛选
        searchCarMaintainData(){
            this.loadCarMaintainData(this.carId);
        },
        //改变每页数量触发，重新加载表格
        handleSizeChange(val) {
            this.pageSize = val;
            this.loadCarMaintainData(this.carId);
        },
        //改变当前页触发，重新获取表格
        handleCurrentChange(val) {
            this.currentPage = val;
            this.loadCarMaintainData(this.carId);
        },


        /**
         * 加载车辆保养信息列表
         * @param carId
         */
        loadCarMaintainData(carId){
            let params={};
            params.carInfoId = carId;
            params.startTime = moment(this.carMainRecord.startTime).format('YYYY-MM-DD');
            params.endTime = moment(this.carMainRecord.endTime).format('YYYY-MM-DD');
            params.pageSize = this.pageSize;
            params.pageNumber = this.currentPage;
            carService.carMaintainPageList(params,(res)=>{
                this.carMaintainTable = res.records;
                this.total = res.totalRecord;
            },(err)=>{
                this.$message.error('error:' + err);
            })
        },

        goBack(){
            this.$parent.isCarMaintain = false;
            this.$parent.isCarDispatch = false;
        }
    },
    mounted: function () {
        //页面载入时获取用户字典，填入下拉列表
        // carService.findAllSysUser(res=>{
        //     this.personOpts = res;
        // },err=>{
        //     this.$message.error('获取驾驶员列表失败'+err);
        // });
    },
    components: {}

});

module.exports = comm;
