<div class="carPatrolTrackEmer"  v-show="dialogVisible">
    <!--组件标题栏-->
    <div slot="header" class="carPatrolTrackTitle">
        <div class="float-right">
            <el-button icon="el-icon-close" type="primary" @click="closePatrolTrackPlugin">关闭</el-button>
        </div>
    </div>
    <div>
        <div style="font-size: 20px;font-weight: bold;color: #464646;text-align: center;background-color: #fff;margin: 10px">车辆轨迹回放</div>
    </div>
    <!--组件内容-->
    <div class="carPatrolTrackContent" style="padding: 0 10px 5px">
        <!--组件查询栏-->
        <div class="searchBox">
            <el-form label-width="70px" :model="queryForm">
                <el-form-item label="车辆名称">
                    <el-input v-model="queryForm.carName" placeholder="请输入车辆名称"></el-input>
                </el-form-item>
                <el-form-item label="时间范围">
                    <el-date-picker
                        v-model="queryForm.queryDate"
                        type="datetimerange"
                        :picker-options="pickerOptions"
                        placeholder="选择时间范围">
                    </el-date-picker>
                </el-form-item>
            </el-form>
            <el-button icon="el-icon-search" circle @click="getGPSInfoByTime"></el-button>
        </div>
        <!--组件查询列表-->
        <div class="treeWarp">
            <el-tree
                :data="emergencyCarTreeData"
                node-key="label"
                @node-click="handleECPlayBackTreeNodeClick"
                :default-expanded-keys="emergencyCarTreeData.length ? [emergencyCarTreeData[0].label] : []"
                :filter-node-method="filterECTrackTreeNode"
                ref="ECPlayBackTree">
                <div class="carItemWrapper" slot-scope="data">
                    <img class="iconCar" v-if="data.data.isCar" :src="'./img/appEmergencyCommand/icon-car.png'"/>
                    <span class="textCar"
                          :class="{selectedCar: data.node.label === ECTrackPlayBackFilterForm.carNums[0]}">{{data.node.label}}</span>
                    <img class="iconTick" v-if="data.node.label === ECTrackPlayBackFilterForm.carNums[0]"
                         :src="'./img/appEmergencyCommand/icon-tick.png'"/>
                </div>
            </el-tree>
        </div>
        <!--组件按钮-->
<!--        <div class="carPatrolTrackBtn" style="display: flex;justify-content: center">-->
<!--            <el-button type="primary" :disabled="playerState=='play'" @click="startPlay">播放</el-button>-->
<!--            <el-button type="primary" :disabled="playerState=='stop'" @click="stopPlay">暂停</el-button>-->
<!--            <el-button type="danger" @click="resetInterval">重置</el-button>-->
<!--        </div>-->
    </div>
</div>