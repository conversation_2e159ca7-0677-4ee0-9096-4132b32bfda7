<div class="addDeployPoint" v-if="outletMapListVisible" v-loading="isLoading">
    <div class="outletMapListWrap" v-show="!outletDetailVisible">
        <div slot="header" class="userMonitorTitle">
            <el-button class="addBtn" type="primary" size="mini" icon="el-icon-plus" @click="showDetail({})">添加</el-button>
            布防点列表
            <i class="el-icon-close" @click="closePage"></i>
        </div>
        <div style="padding: 10px 10px 5px" class="content">
            <div class="contentWrap">
                <div class="contentTop">
                    <el-form class="itemWidth" :inline="true">
                        <el-form-item label="所属片区">
                            <el-select :clearable="true" placeholder="请选择"
                                       @change="search"
                                       v-model="searchForm.district">
                                <el-option v-for="item in districtList"
                                           :label="item"
                                           :value="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="布防点名称">
                            <el-input v-model="searchForm.name" @keyup.enter.native="search" placeholder="请输入内容"></el-input>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="contentBottom">
                    <el-table
                        :data="tableData"
                        height="100%"
                        @row-click="showDetail"
                        :header-cell-style="{color:'#333333','font-weight':'bold'}"
                        style="width: 100%">
                        <el-table-column
                            align="center"
                            prop="name"
                            label="布防点名称">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="district"
                            width="60"
                            label="所属片区">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="address"
                            label="位置">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            width="60"
                            prop="level"
                            label="布防点级别">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="reasonstate"
                            label="内涝原因">
                        </el-table-column>
<!--                        <el-table-column-->
<!--                            align="center"-->
<!--                            width="60"-->
<!--                            label="操作">-->
<!--                            <template slot-scope="scope">-->
<!--                                <div class="editBox">-->
<!--                                    <el-tooltip effect="dark" content="查看详情" placement="top">-->
<!--                                        <img @click="showDetail(scope.row)" src="../../../../../img/view.png">-->
<!--                                    </el-tooltip>-->
<!--                                </div>-->
<!--                            </template>-->
<!--                        </el-table-column>-->
                    </el-table>
                </div>
            </div>
        </div>
        <div class="footer">
            <el-pagination
                background
                small
                :current-page="page.pageNumber"
                @current-change="pageChange"
                :page-size="page.pageSize"
                layout="prev, pager, next,total"
                :total="page.totalRecord">
            </el-pagination>
        </div>
    </div>
    <!-- 详情 -->
    <div class="outletMapListWrap" v-show="outletDetailVisible">
        <div slot="header" class="userMonitorTitle">
            布防点信息
            <i class="el-icon-close" @click="closeOutletDetail"></i>
        </div>
        <div style="padding: 10px 10px 5px" class="content">
            <div class="contentWrap">
                <div class="contentTop" style="height: 100%">
                    <el-form :rules="checkRule" ref="checkForm" :model="outletInfo">
                        <el-form-item label="布防点名称" prop="name">
                            <el-input v-model="outletInfo.name" placeholder="请输入内容" :disabled="!allPermission.modifica"></el-input>
                        </el-form-item>
                        <el-form-item label="所属片区" prop="district">
                            <el-select :clearable="true" placeholder="请选择"
                                       :disabled="!allPermission.modifica"
                                       v-model="outletInfo.district">
                                <el-option v-for="item in districtList"
                                           :label="item"
                                           :value="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="位置" prop="address">
                            <el-input v-model="outletInfo.address" placeholder="请输入内容" :disabled="!allPermission.modifica"></el-input>
                        </el-form-item>
                        <el-form-item label="布防点级别" prop="level">
                            <el-select :clearable="true" placeholder="请选择"
                                       :disabled="!allPermission.modifica"
                                       v-model="outletInfo.level">
                                <el-option v-for="item in levelList"
                                           :label="item"
                                           :value="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="内涝原因" prop="reasonstate">
                            <el-input v-model="outletInfo.reasonstate" placeholder="请输入内容" :disabled="!allPermission.modifica"></el-input>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>
        <div class="footerButton">
            <el-button size="medium" style="width: 100px" type="danger" @click="resetLocation('current')">重新绘制</el-button>
            <el-button size="medium" style="width: 100px" type="primary" @click="saveDetail" v-if="allPermission.modifica">保存</el-button>
        </div>
    </div>
</div>