var template = require('./content.html');
var eventHelper = require('utils/eventHelper');
var mathUtils = require('utils/mathUtils');
// var carService = require('services/carService');
var carMonitorInfo = require('modules/realTimeMonitor/carMonitor/carMonitorInfo');

var comm = Vue.extend({
    template: template,
    data: function () {
        return {
            apiInstance:{},//地图API工具
            baseView:{},//地图视图对象
            baseMap:{},//地图vue组件对象
            showRealTimeMonitor: false,//车辆实时监测组件显示控制
            tableData: [],//车辆实时监测列表数据
            isLoading: false,//车辆列表数据加载控制
            carMonitorLayer: {},//车辆实时监测图层
            intervalReload:'',
            infoWinId:"",
        }
    },
    methods: {
        /***
         * 地图插件初始化
         * @param apiInstance
         * @param baseView
         * @param baseMap
         */
        initPlugin (apiInstance, baseView, baseMap) {
            this.apiInstance = apiInstance;
            this.baseView = baseView;
            this.baseMap = baseMap;
        },
        /***
         * 填充实时人员监测信息到表格中
         */
        loadTableData () {
            this.isLoading = true;
            this.tableData = [];

            carService.obtainCarInfoAndRealTime((result)=>{
                if (!this.baseMap.getLayerById('carMonitorLayer')) {
                    this.carMonitorLayer = this.baseMap.addGraphicLayer('carMonitorLayer');
                }
                this.carMonitorLayer.removeAll();

                result.list.forEach( (item) =>{
                    let tempObj = {};
                    if(item.carRealtimeGpsInfo){
                        tempObj = Object.assign({},item.carRealtimeGpsInfo,item);
                        if (mathUtils.isNumber(tempObj.x) && mathUtils.isNumber(tempObj.y)) {
                            let point = this.apiInstance.createPoint(tempObj.x, tempObj.y, this.baseView.spatialReference);
                            let markerSymbol = {
                                url: "./img/toolbar/car1.png",
                                width: "50px",
                                height: "28px",
                            };
                            this.pointGraphic = this.apiInstance.createGeometry(this.carMonitorLayer, point, markerSymbol);
                            let textObj = {
                                color: '#008cff',
                                text: item.carNum,
                                xoffset: 0,
                                yoffset: -20,
                                font: {
                                    size: 12
                                }
                            };
                            this.apiInstance.createTextSymbol(this.carMonitorLayer, tempObj.x, tempObj.y, textObj);
                        }
                    } else {
                        tempObj = Object.assign({},item);
                    }
                    this.tableData.push(tempObj);
                });
                //arcgis js 4.9版本 TextSymbol 偏移效果需要手动触发
                setTimeout(()=>{
                    this.baseView.goTo({
                        target: this.baseView.center
                    });
                },1000);
                this.isLoading = false;
            });
        },
        closeRealTimeMonitor () {
            let ids = [];
            this.tableData.forEach((item)=>{
                if(item.infoWinId)
                    ids.push(item.infoWinId);
            });
            eventHelper.emit('del-info-window-custom',ids);

            this.showRealTimeMonitor = false;
            this.tableData = [];
            clearInterval(this.intervalReload);
            this.removeCarLayer();
        },
        removeCarLayer () {
            this.baseMap.removeLayerById('carMonitorLayer');
            this.carMonitorLayer = {};
        },
        rowClick (row) {
            if (mathUtils.isNumber(row.x) && mathUtils.isNumber(row.y)) {
                this.baseMap.setCenter(row.x, row.y, 14);
                let point = this.apiInstance.createPoint(row.x, row.y, this.baseView.spatialReference);
                if(this.infoWinId){
                    eventHelper.emit('del-info-window-custom',[this.infoWinId]);
                    this.infoWinId = "";
                }
                let carMonitorInfoVue = new carMonitorInfo();
                carMonitorInfoVue.setParam(row);
                let item = {
                    geometry: point,
                    title: row.carNum,
                    containerStyle: {
                        width: "400px"
                    },
                    contentVue: carMonitorInfoVue,
                    infoWinId:this.infoWinId
                };
                eventHelper.emit('add-info-window-custom', item);
                if (item.infoWinId)
                    this.infoWinId = item.infoWinId;
            } else {
                this.$message({
                    message: '坐标有误，无法定位',
                    type: 'warning'
                });
            }
        },
    },
    mounted: function () {
        /*显示车辆出勤监测图层*/
        eventHelper.on('showCarRTM', function () {
            this.showRealTimeMonitor = true;
            this.loadTableData();
            this.intervalReload = setInterval(function () {
                this.loadTableData();
            }.bind(this), 30000);
            eventHelper.emit('change-menu', {funUrl: 'arcgis-plugin'});
        }.bind(this));
        /*移除车辆出勤监测图层*/
        eventHelper.on('removeCarRTM', function () {
            this.tableData = [];
            this.showRealTimeMonitor = false;
            clearInterval(this.intervalReload);
            this.intervalReload = {};
            this.removeCarLayer();
        }.bind(this));
    }
});
module.exports = comm;