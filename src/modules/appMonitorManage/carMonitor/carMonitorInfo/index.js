var template = require('./content.html');
var eventHelper = require('utils/eventHelper');

// 定义组件
var comm = Vue.extend({
    template: template,
    data: function () {
        return {
            form: {},
        }
    },
    methods: {
        setParam: function (paramItem) {
            this.form = Object.assign({},paramItem);
        }
    },
    mounted: function () {

    },
});
module.exports = comm;