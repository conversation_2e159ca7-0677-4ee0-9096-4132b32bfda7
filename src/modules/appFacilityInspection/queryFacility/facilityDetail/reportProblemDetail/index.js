const template = require('./content.html');
const moment = require("moment");
var serviceHelper = require("services/serviceHelper");
var appWorkOrderPipeInspectionService = require("services/appWorkOrderPipeInspectionServiceJh");
const eventHelper = require('utils/eventHelper');
var processInformation = require('modules/components/processInformation');
var workOrderInspectionData = require("controllers/model/workOrderInspectionData");
// 定义组件
let self;
const comm = Vue.extend({
    props: {},
    template: template,
    data: function () {
        return {
            picList: [{
                title: '周边环境',  //上传图片标题
                // bizType: '事件所在周边环境照片',//后台保存的bizType
                bizType: 'arround_infos',//后台保存的bizType
                limit: 0,
                date: moment().format('YYYY-MM-DD'),//水印日期（选填，不需要水印可以不填）
                uploadImgs: [],//保存图片的数组
                imgIds: [],//上传图片成功后回调的图片id数组
            }, {
                title: '具体位置',  //上传图片标题
                // bizType: '事件所在具体标识照片',//后台保存的bizType
                bizType: 'specific_infos',//后台保存的bizType
                limit: 0,
                date: moment().format('YYYY-MM-DD'),//水印日期（选填，不需要水印可以不填）
                uploadImgs: [],//保存图片的数组
                imgIds: [],//上传图片成功后回调的图片id数组
            }, {
                title: '设施细节',  //上传图片标题
                // bizType: '事件设施当前照片',//后台保存的bizType
                bizType: 'detail_infos',//后台保存的bizType
                limit: 0,
                date: moment().format('YYYY-MM-DD'),//水印日期（选填，不需要水印可以不填）
                uploadImgs: [],//保存图片的数组
                imgIds: [],//上传图片成功后回调的图片id数组
            }],//上传图片数组
            recordButVisible: false,//开始录音按钮显隐
            audioList: [],//录音数组
            orderDetail: {
                id: '',
                workOrderNo: '',//工单编号
                address: '',//地址
                problemType1: '维修上报',//工单类型
                workOrderSource: '',//工单来源
                reportDate: '',//上报时间
                facilityProblem: '',//设施问题
                concern: '3',
                jingOut: '',
                jingIn: '',
                rainWaterOut: '',
                rainWaterIn: '',
                jingType: '',
                problem: ''
            },
            selectFacilityList: [],//上报的设施数组
            bottomContentVisible: true,//底部处理按钮显隐
            detailVisible: false,//处理详情按钮显隐


        }
    },
    watch: {},
    computed: {},
    methods: {
        /**
         * 返回首页
         */
        goBack: function () {
            this.$router.go(-1);
        },
        /**
         * 处理工单
         */
        close: function () {
            this.$router.go(-1);
        },
        /**
         * 结束工单
         */
        closeOrder: function () {
            eventHelper.emit('loading-start')
            appWorkOrderPipeInspectionService.closeProblemWorkOrder({
                id: this.orderDetail.id,
                objectId: this.orderDetail.objectId
            }, res => {
                eventHelper.emit('loading-end');
                console.log('结束工单成功：' + res)
            }, err => {
                eventHelper.emit('loading-end');
                this.$toast({
                    message: '结束工单失败：' + err,
                });
            })
            this.$router.go(-1);
        },
        //获取处理工单的权限
        checkAllAuthority() {
            eventHelper.emit('loading-start')
            appWorkOrderPipeInspectionService.checkAllAuthority({
                ids: this.orderDetail.id,
            }, res => {
                eventHelper.emit('loading-end');
                if (res[this.orderDetail.id] == '无权限' && this.detailVisible == false) this.bottomContentVisible = false
            }, err => {
                eventHelper.emit('loading-end');
                this.$toast({
                    message: '获取处理工单权限失败：' + err,
                });
            })
        },
        getProblemWorkOrderByObjectId(id) {
            eventHelper.emit('loading-start')
            appWorkOrderPipeInspectionService.getProblemWorkOrderByObjectId({
                id: id
            }, res => {
                eventHelper.emit('loading-end');

            }, err => {
                eventHelper.emit('loading-end');
                this.$toast({
                    message: '获取工单失败：' + err,
                });
            });
        }

    },
    filters: {},
    activated: function () {
        this.orderDetail = this.$options.data().orderDetail;
        this.bottomContentVisible = this.$options.data().bottomContentVisible;
        let orderDetailParam = typeof this.$route.query.orderDetail != 'string' ? this.$route.query.orderDetail : JSON.parse(this.$route.query.orderDetail)
        this.orderDetail = Object.assign(this.orderDetail, orderDetailParam);
        // this.selectFacilityList = this.orderDetail.problemTypeDetail.lstReportWorkOrderFacility;
        this.orderDetail = res.data;
        if (this.orderDetail.problemtype === '窨井') {
            let jingOutTemp = this.orderDetail.mark.split('/')[0]
            jingOutTemp = jingOutTemp.substr(0, jingOutTemp.length - 1);
            this.orderDetail.jingOut = jingOutTemp
            this.orderDetail.jingIn = this.orderDetail.mark.split('/')[1].substr(1)
        } else if (this.orderDetail.problemtype === '雨水口') {
            let rainWaterOutTemp = this.orderDetail.mark.split('/')[0]
            rainWaterOutTemp = rainWaterOutTemp.substr(0, rainWaterOutTemp.length - 1);
            this.orderDetail.rainWaterOut = rainWaterOutTemp
            this.orderDetail.rainWaterIn = this.orderDetail.mark.split('/')[1].substr(1)
        } else {
            this.orderDetail.jingType = this.orderDetail.mark
        }
        //获取图片
        this.picList = this.$options.data().picList;
        this.picList.forEach(item => {
            if (!!this.orderDetail[item.bizType] && this.orderDetail[item.bizType].length > 0) {
                this.orderDetail[item.bizType].forEach(pic => {
                    item.uploadImgs.push({
                        facilityImageUri: serviceHelper.getGwxjPicUrl(pic.id),
                        showDelOperation: false
                    });
                    item.imgIds.push(pic.id)
                });
            }
        });
        // if(this.orderDetail.currentTask==='维修处理上报' || ['工单派发', '维修处理上报'].indexOf(this.orderDetail.currentTaskDisplay) > -1){
        //     this.detailVisible = false;
        // }else{
        //     this.detailVisible = true;
        // };
        this.checkAllAuthority()

        //更新流程信息
        // this.$nextTick(()=>{
        //     this.$refs.processInformation.init(this.orderDetail.lstProcessinst);
        // })
        //获取录音
        // this.audioList = this.$options.data().audioList;
        // if (!!this.orderDetail['上报时录音'] && this.orderDetail['上报时录音'].length > 0) {
        //     this.orderDetail['上报时录音'].forEach(audio => {
        //         this.audioList.push({
        //             url: serviceHelper.getGwxjPicUrl(audio.id),
        //             time:0,
        //             id:audio.id,
        //             currentState:'pause',
        //         })
        //     });
        // }
        // this.$refs.recordingMedia.initAudio(this.audioList)

    },
    mounted: function () {


    },
    components: {
        'process-information': processInformation
    }
});
module.exports = comm;
