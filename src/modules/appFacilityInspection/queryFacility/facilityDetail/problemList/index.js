var template = require('./content.html');
var eventHelper = require('utils/eventHelper');
var serviceHelper = require('services/serviceHelper');
var mapHelper = require('utils/mapHelper');
var oneMapService = require("services/oneMapService");
const appWorkOrderPipeInspectionService = require("services/appWorkOrderPipeInspectionServiceJh");

// 定义组件
var comm = Vue.extend({
    template: template,
    data: function () {
        return {
            workOrderObj: {
                '清疏上报-草稿': 'clearingReportJh',
                '维修上报-草稿': 'maintenanceReportJh',
                '发现问题清疏处理-草稿': 'clearingReportAndHandleJh',
                '发现问题维修处理-草稿': 'maintenanceReportAndHandleJh',
                '发现问题维修处理': 'maintenanceReportAndHandleDetailJh',
                '发现问题清疏处理': 'clearingReportAndHandleDetailJh',
                '维修上报': 'maintenanceReportDetailJh',
                '清疏上报': 'clearingReportDetailJh',
                '问题上报': 'reportProblemDetail',
                '外部施工': 'externalConStructionDetail',
            },
            // 详情
            detailQueryParam: {
                cuuid: '',
                isClosed: '',
                reportTime: [],
                handleDate: [],
                reportTimeStr: '',
                handleDateStr: '',
            },
            detailInfo: {
                problemList: [],
            },
            reportTimeShow: false,
            handleDateShow: false,
        }
    },
    activated: function () {
    },
    mounted: function () {
    },
    methods: {
        //打开页面
        open(data) {
            // 获取设施信息
            this.detailQueryParam.cuuid = data.cuuid;
            this.viewProblemList();
        },
        close() {
        },
        goBack() {
        },
        detailClosedChange(e) {
            this.detailQueryParam.isClosed = e;
            this.viewProblemList();
        },
        detailReportTimeChange(e) {
            this.detailQueryParam.reportTime = e;
            this.viewProblemList();
        },
        detailHandleDateChange(e) {
            this.detailQueryParam.handleDate = e;
            this.viewProblemList();
        },
        getPic(id) {
            return serviceHelper.getGwxjPicUrl(id)
        },
        viewProblemList() {
            const formData = Object.assign({}, this.detailQueryParam);
            oneMapService.findProblemByCuuid(formData, data => {
                this.detailInfo.problemList = data;
            }, err => {
                console.error("error", err);
                this.$message.error(err);
            });
        },
        viewDetail(row) {
            this.$parent.$parent.open('问题上报', 'problem', row);
        },
        back() {
            this.$parent.$parent.backCallbackHandle();
        },
        //获取隐患点视频
        //初始化数据
        initData() {

        },
        showImgPic(imgs, index) {
            this.$parent.$parent.showImgPic(imgs, index);
        },
        // 跳转工单详情
        getDetail(item) {
            eventHelper.emit('loading-start')
            this.getWorkOrderDetail(item.id, 'reportProblemDetail');//this.workOrderObj[res[item.id]]->对应路由名字
            // appWorkOrderPipeInspectionService.checkAllAuthority({
            //     ids:item.id,
            // },res=>{
            //     this.getWorkOrderDetail(item.id, this.workOrderObj[res[item.id]]);//this.workOrderObj[res[item.id]]->对应路由名字
            // },err=>{
            //     eventHelper.emit('loading-end');
            //     this.$toast({
            //         message: '获取状态失败：'+err,
            //     });
            // })
        },
        getWorkOrderDetail(id, routeName) {
            appWorkOrderPipeInspectionService.getProblemWorkOrderByObjectId({
                id: id
            }, res => {
                eventHelper.emit('loading-end');
                res.isHasReporting = true;//判断是否暂存，用于区分上报页的id是工单id还是投诉id，
                this.$router.push({
                    name: routeName,
                    query: {
                        orderDetail: JSON.stringify(res),
                        merge: true,
                        isFirst: false
                    }
                })
            }, err => {
                eventHelper.emit('loading-end');
                this.$toast({
                    message: '获取工单详情失败：' + err,
                });
            })
        },
        onConfirmReportTime(date) {
            const [start, end] = date;
            const startStr = moment(start).format('YYYY-MM-DD');
            const endStr = moment(end).format('YYYY-MM-DD');
            this.detailQueryParam.reportTime = [startStr, endStr];
            this.detailQueryParam.reportTimeStr = startStr + '-' + endStr;
            this.detailReportTimeChange(this.detailQueryParam.reportTime);
            this.reportTimeShow = false;
        },
        clearReportTime() {
            this.detailQueryParam.reportTime = [];
            this.detailQueryParam.reportTimeStr = '';
        },
        onConfirmHandleDate(date) {
            const [start, end] = date;
            const startStr = moment(start).format('YYYY-MM-DD');
            const endStr = moment(end).format('YYYY-MM-DD');
            this.detailQueryParam.handleDate = [startStr, endStr];
            this.detailQueryParam.handleDateStr = startStr + '-' + endStr;
            this.detailHandleDateChange(this.detailQueryParam.handleDate);
            this.handleDateShow = false;
        },
        clearHandleDate() {
            this.detailQueryParam.handleDate = [];
            this.detailQueryParam.handleDateStr = '';
        }
    },
    components: {},
});
module.exports = comm;