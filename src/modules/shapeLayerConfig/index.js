//引用组件或视图

//此功能对应的视图（html）
var template = require('./shapeLayerConfig.html');
//用于获取token，url头部等
var serviceHelper = require('services/serviceHelper');
//增删改列表基类
var crudBase = require('modules/common/crud/crudBase');
//增删改列表容器（代表一个表格的列表）
var container = require('modules/common/crud/listContainer');
//表单验证
var eFormValid = require('modules/common/eFormValid.js');

var containerShapeFieldConfig = require('./shapeFieldConfig');

var comm = crudBase.extend({
    //设置模板
    template: template,
    // data: function () {
    //     return {}
    // },
    components: {
        'containerShapeFieldConfig': containerShapeFieldConfig,
    },
    created: function () {
        //列表容器定义，一个容器代表一个表的编辑，有子表就要为子表设置容器
        //默认容器叫containerMain，第二个后之后的容器名称自己命名
        //容器必须要在created时new，而不能在mounted，否则vue绑定会有错
        this.containerMain = new container({
            data: function () {
                return {
                    //后台url的key，默认是ewater，因为此基类大部分用于ewater功能因此作为默认
                    //访问其他后台可以重写此值，值的用法同serviceHelper.getBasicPath()
                    pathKey: "",
                    addDefaultOperateColumn: false,
                    showList: true,
                    eFormValid: null,
                }
            },
            methods: {
                afterRefreshFormHandler: function () {

                    //清除上一次验证的痕迹
                    if (this.eFormValid) {
                        this.eFormValid.clearValidResult();
                    }
                },
                //获取自定义保存的值（如果默认的从界面获取保存的值不满足需求，可以重写此方法，自定义获取值）
                getCustomSaveValue: function () {
                },
                // inputLayer:function(){
                //     var formData=serviceHelper.getDefaultAjaxParam();
                //
                //
                // },
                refreshList: function (pageNumber, pageSize) {
                    //获取刷新列表的查询参数
                    var formData = this.getRefreshListParam(pageNumber, pageSize);

                    serviceHelper.getJson(serviceHelper.getBasicPath() + this.listUrl, formData, function (result) {
                        //填充数据到grid

                        //按bootstrap table要求的格式组织数据，并加载数据到table
                        var tableData = {};
                        tableData.total = result.totalRecord;
                        tableData.rows = result.records;

                        //当当前页码大于总页数（例如本来在100页，输入某个查询条件后查询结果总共只有20页）时，把当前页码设为1，并重新往后台查询数据
                        if (result.pageNumber > result.totalPage && result.pageNumber > 1) {
                            var pageSize = $("#" + this.tableId, $("#" + this.vm.mainContentDivId)).bootstrapTable("getOptions").pageSize;
                            this.refreshList(1, pageSize);
                            return;
                        }

                        $("#" + this.tableId, $("#" + this.vm.mainContentDivId)).bootstrapTable("load", tableData);

                        //分页部分，选择每页行数下拉菜单不能弹出的修复
                        //此下拉菜单使用bootstrap的Dropdowns控件，可能由于已经过了bootstrap初始化的时机，导致此控件没有正常初始化，因此在此手动初始化
                        $("#" + this.tableId, $("#" + this.vm.mainContentDivId)).parent().parent().find('.dropdown-toggle').dropdown();

                        //触发刷新列表后的回调
                        if (this.afterRefreshListHandler)
                            this.afterRefreshListHandler();
                    }.bind(this));
                },
                //初始化表单
                initForm: function () {
                    //表单验证
                    this.eFormValid = new eFormValid();
                    this.eFormValid.mainContentDivId = this.vm.mainContentDivId;
                    this.eFormValid.addNotNullRule("layerId", "图层Id");
                    this.eFormValid.addNotNullRule("name", "图层名");
                    this.eFormValid.addNotNullRule("type", "图层类型");
                },
                beforeSaveForm: function () {
                    return this.eFormValid.valid();
                },
                //刷新图层配置缓存
                refreshPipeLayerConfig: function () {
                    var formData = serviceHelper.getDefaultAjaxParam();

                    serviceHelper.getJson(serviceHelper.getBasicPath() + "/shapeLayerConfig/refreshPipeLayerConfig", formData, function (result) {
                        layer.msg("操作成功");
                    }.bind(this));
                },
            }
        });
    },
    mounted: function () {
        this.containerShapeFieldConfig = this.$refs.containerShapeFieldConfig1;

        this.containerShapeFieldConfig.initContainer(this);
        this.containerShapeFieldConfig.initForm();
        //容器初始化，初始化必须在页面加载完成，也就是mounted时触发
        //参数：this（传入全局vue对象）；controller的url；grid的列头设置
        this.containerMain.init(this, "/shapeLayerConfig", [{
            //checkbox列，用于勾选多选行
            field: 'state',
            checkbox: 'true'
        }, {
            field: 'name',
            title: '图层名'
        }, {
            field: 'aliasName',
            title: '图层别名'
        }, {
            field: 'layerId',
            title: '图层id'
        }, {
            field: 'type',
            title: '图层类型'
        },
            {
                field: 'sort',
                title: '序号'
            },
            {
                field: 'operate',
                title: '操作',
                align: 'center',
                //点击事件
                events: {
                    //事件字符串格式：click .+a的class名称
                    'click .edit': function (e, value, row, index) {
                        //编辑
                        this.edit(row.id);
                    }.bind(this.containerMain),
                    'click .delete': function (e, value, row, index) {
                        //删除（一条）
                        this.deleteOne(row.id);
                    }.bind(this.containerMain),
                    'click .fieldConfig': function (e, value, row, index) {
                        //跳转到规则表
                        this.vm.showFieldConfigList(e, value, row, index);
                    }.bind(this.containerMain),
                },
                //操作类的内容
                formatter: function (value, row, index) {
                    return [
                        '<a class="fieldConfig" href="javascript:;" >',
                        '字段配置',
                        '</a>  ',
                        // '<a class="edit" href="javascript:;" title="编辑">',
                        // '<i class="glyphicon glyphicon-edit"></i>',
                        // '</a>  ',
                        // '<a class="delete" href="javascript:;" title="删除">',
                        // '<i class="glyphicon glyphicon-remove"></i>',
                        // '</a>'
                        //统一操作栏 添加tooltips
                        '<div class="commonTableEditBox edit">',
                        '<div class="tooltipBox">编辑</div>',
                        '<img src="./img/edit.png" width="18" height="18" />',
                        '</div>',
                        '<div class="commonTableEditBox delete">',
                        '<div class="tooltipBox">删除</div>',
                        '<img src="./img/delete.png" width="18" height="18" />',
                        '</div>'
                    ].join('');
                }
            }]);

        //刷新列表
        this.containerMain.refreshList();
        this.containerMain.initForm();
        this.$refs.tableMain.firstElementChild.style.backgroundColor = '#eee';
    },
    methods: {
        /**
         * 跳转到下一列表
         * @param row
         * @param containerThis 当前列表容器
         * @param containerNext 下一列表容器
         * @param fkFieldName 下一列表容器字段名
         */
        showNextList: function (fkValue, containerThis, containerNext, fkFieldName) {
            //隐藏当前列表
            containerThis.showList = false;
            //显示下一列表
            containerNext.showList = true;

            //对下一列表容器赋值外键
            containerNext[fkFieldName] = fkValue;
            //对下一列表容器赋值当前列表容器
            containerNext.lastContainer = containerThis;
            //刷新下一列表
            containerNext.refreshList();
        },
        showFieldConfigList: function (e, value, row, index) {
            this.showNextList(row.layerId, this.containerMain, this.containerShapeFieldConfig, "currentLayerId");
        }

    }
});

module.exports = comm;
