<div class="mainContent">
    <div :id="mainContentDivId" class="row clearfix cesc-tab-content">
        <div class="col-md-12 col-sm-12 col-xs-12  bigPanel" v-show="containerMain.showList">
            <div class="x_panel animated fadeIn">
                <div class="x_title">
                    <!--标题-->
                    <h2>
                        管点公共字段配置
                    </h2>
                    <div class="clearfix"></div>
                </div>
                <div class="x_content">
                    <!--左侧工具栏-->
                    <div class="btn-group hidden-xs" role="group">
                        <!--vue的单击事件绑定 v-on:click="xxx"-->
                        <button type="button" class="btn btn-outline btn-default" title="新增"
                                @click="containerMain.add">
                            <i class="glyphicon glyphicon-plus" aria-hidden="true"></i>
                        </button>
                        <button type="button" class="btn btn-outline btn-default" title="删除"
                                @click="containerMain.del">
                            <i class="glyphicon glyphicon-remove" aria-hidden="true"></i>
                        </button>
                    </div>
                    <!--右侧搜索栏-->
                    <form class="form-inline pull-right">
                        <!--查询条件弹出框，查询条件都放里面-->
                        <el-popover placement="left" width="400" trigger="click">
                            <form>
                                <div class="form-group">
                                    <label>字段id</label>
                                    <input type="text" class="form-control"
                                           v-model="containerMain.toolbarQueryParam.fieldId">
                                </div>
                                <div class="form-group">
                                    <label>字段名</label>
                                    <input type="text" class="form-control"
                                           v-model="containerMain.toolbarQueryParam.name">
                                </div>
                                <div class="form-group">
                                    <label>字段别名</label>
                                    <input type="text" class="form-control"
                                           v-model="containerMain.toolbarQueryParam.aliasName">
                                </div>
                            </form>
                            <form class="form-inline pull-right">
                                <!--查询按钮-->
                                <button type="button" class="btn btn-default btn-outline"
                                        @click="containerMain.search">
                                    查询
                                </button>
                                <!--清空查询条件-->
                                <button type="button" class="btn btn-default btn-outline"
                                        @click="containerMain.initToolbarQueryParam">
                                    清空
                                </button>
                            </form>
                            <!--打开查询弹窗的按钮-->
                            <button type="button" class="btn btn-default btn-outline" title="查询" slot="reference">
                                <i class="glyphicon glyphicon-search"></i>
                            </button>
                        </el-popover>
                        <!--刷新功能按钮-->
                        <button type="button" class="btn btn-default btn-outline" title="刷新"
                                @click="containerMain.search">
                            <i class="glyphicon glyphicon-refresh"></i>
                        </button>
                    </form>
                    <!--表格控件，控件定义在js-->
                    <!--一个页面如果有多个这样的table，这里的id要改，容器对应的id也要改-->
                    <table id="tableMain">
                    </table>
                </DIV>
            </div>
        </div>

        <!--表单（弹窗）-->
        <!--一个页面如果有多个这样的table，这里的id要改，容器对应的id也要改-->
        <div id="formMain" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <!--标题部分-->
                        <!--关闭按钮-->
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <!--标题-->
                        <h4 class="modal-title"></h4>
                        <!--顶部按钮-->
                        <button type="button" class="btn btn-primary" @click='containerMain.submit'>保存</button>
                    </div>
                    <div class="modal-body">
                        <!--内容部分-->
                        <form class="form-horizontal">
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">字段Id：</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="fieldId" v-model="containerMain.currentEntity.fieldId">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">字段名：</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="name" v-model="containerMain.currentEntity.name">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">字段别名：</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="aliasName" v-model="containerMain.currentEntity.aliasName">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">字段长度：</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="fieldLength" v-model="containerMain.currentEntity.fieldLength">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">序号：</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="sort" v-model="containerMain.currentEntity.sort">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">类型：</label>
                                    <div class="col-sm-9">
                                        <select class="form-control" v-model="containerMain.currentEntity.type" id="type">
                                            <option value="string">字符</option>
                                            <option value="double">数字</option>
                                            <option value="int">整数</option>
                                            <option value="date">日期</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">列表是否显示：</label>
                                    <div class="col-sm-9">
                                        <select class="form-control" v-model="containerMain.currentEntity.identifyShow" id="identifyShow">
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">属性表是否显示：</label>
                                    <div class="col-sm-9">
                                        <select class="form-control" v-model="containerMain.currentEntity.attrDisplay" id="attrDisplay">
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">可否编辑：</label>
                                    <div class="col-sm-9">
                                        <select class="form-control" v-model="containerMain.currentEntity.enableEdit" id="enableEdit">
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <!--这里还可以加底部（footer），详见bootstrap官网-->
                </div>
            </div>
        </div>
    </div>
</div>