<div class='mapContainer' v-loading="mapLoading" :class="{pipeExecuteMode:!!isPipeExecute}"
     element-loading-text="地图初始化...">
    <water-mark></water-mark>
    <div class="mapItem" id="mapDxt" v-show="isTaskCheck">
        <cesc-map ref="dxtMap"></cesc-map>
    </div>
    <div class="mapItem" id="mapWxt" :class="{mapItemChange:!isTaskCheck}">
        <cesc-map ref="baseMap"></cesc-map>
    </div>
    <div class="mapTools" id="topbar">
        <button class="action-button" id="pointButton" type="button" title="点选">
            <img src="../../img/index/selectPoint.png" alt="" class="imgIcon">
        </button>
        <button class="action-button" id="polylineButton" type="button" title="线选">
            <img src="../../img/index/selectLine.png" alt="" class="imgIcon">
        </button>
        <button class="action-button" id="polygonButton" type="button" title="面选">
            <img src="../../img/index/selectArea.png" alt="" class="imgIcon">
        </button>
        <button class="action-button" id="resetBtn" type="button" title="清除"
                @click="clearGraLayer">
            <img src="../../img/index/delete.png" alt="" class="imgIcon">
        </button>
        <button class="action-button" type="button" id="checkBtn" title="发起核查工单" style="font-size: 13px;"
                v-show="dataCheckOrderBtnVisible" v-if="permission.dataCheckOrderReport || permission.createAndRead"
                @click="showQuestionArea">
            <img src="../../img/index/reportProblem.png" alt="" class="imgIcon">
        </button>
        <!--   <button class="action-button el-icon-warning" type="button" :class="{'active':isQueryMark}"
                title="标注点" style="margin-top: 2px;" @click="queryMarker"></button>-->
        <!--<button class="el-icon-remove-outline action-button " id="stopEdit" type="button" title="测试停止"></button>-->
        <legend-control></legend-control>
        <legend-view></legend-view>
    </div>
    <div class="mapTools connipSelect">
        <el-select v-model="connipType" clearable size="mini" @change="changeConnipType">
            <el-option label="摸查库" value=""></el-option>
            <el-option label="运行库" value="chart"></el-option>
            <el-option label="临时库" value="draw"></el-option>
        </el-select>

    </div>
    <div class="mapTools" style="padding:0 !important;top:0;left:calc(50% - 125px);">
        <weather></weather>
    </div>
    <!-- <div class="pipeMapTools" id="topbar2" v-show="!toolsVisible">
         <button class="action-button icon iconfont icon-dianxuan" id="pointButton2" type="button"
                 title="卫星图"></button>
         <button class="action-button esri-icon-polyline" id="polylineButton2" type="button"
                 title="工具箱"></button>
         <button class="action-button esri-icon-polygon" id="polygonButton2" type="button"
                 title="查询"></button>
         <button class="action-button el-icon-warning" type="button"
                 title="侧弹" style="margin-top: 2px;" @click="showQuestionArea"></button>
     </div>-->

    <!--图例展示-->
    <!-- <legend-view></legend-view>
    <legend-control></legend-control> -->
    <!--右侧栏控制-->
    <!-- <right-panel-control></right-panel-control> -->
    <div id="toolBar" v-show="showtools"></div>
    <!--地图弹出-->
    <!--<info-window ref="mapInfoWindow"></info-window>-->
    <!--雨污接处理分析弹窗-->
    <!--<info-window-custom ref="infoWindow" draggable="true" id="ywDialog" :baseView="baseView"></info-window-custom>-->
    <analysis-dialog ref="infoWindow" id="ywDialog"></analysis-dialog>
    <!--雨污混接分析-->
    <pollution-analysis ref="analysis"></pollution-analysis>
    <pipe-handover ref="pipeHandover"></pipe-handover>
    <pipe-road-edit ref="pipeRoadEdit"></pipe-road-edit>
    <!--标注点列表-->
    <mark-point-list ref="markPointList"></mark-point-list>
    <!--进展展示-->
    <sewage-water-thematic-map ref="sewageWaterThematicMap"></sewage-water-thematic-map>
    <!--修改管线信息-->
    <edit-pipe-info ref="editPipeInfo"></edit-pipe-info>
    <!--任务核查-->
    <task-check ref="taskCheck"></task-check>
    <!--数据核查工单-->
    <data-check-work-order ref="dataCheckWorkOrder"></data-check-work-order>
    <!--养护明细-->
    <maintain-detail ref="maintainDetail"></maintain-detail>
    <!--管网追溯分析-->
    <retrospect-detail ref="retrospectDetail"></retrospect-detail>
    <!--混接点-->
    <mix-connect-point ref="mixConnectPoint"></mix-connect-point>
    <!--隐患点管理-->
    <risk-point-panel :baseView="baseView"></risk-point-panel>
    <!--排水户列表-->
    <drainage-list ref="drainageList"></drainage-list>
    <!--管网抽查-->
    <pipe-check ref="pipeCheck"></pipe-check>
    <!--溯源分析-->
    <sy-analysis ref="syAnalysis"></sy-analysis>
    <!--溯源分析-->
    <traceability-analysis ref="graLayer"></traceability-analysis>
    <!--溯源分析窗口-->
    <traceability-dialog ref="traceabilityDetail"></traceability-dialog>
    <!--集水区域实时监控-->
    <!--<flow-analyze ref="flowAnalyze"></flow-analyze>-->
    <!--设备状态按钮-->
    <status-tools v-show="showFacilityList" ref="statusTools"></status-tools>
    <!--搜索工具箱-->
    <!--<searchToolbox></searchToolbox>-->
    <!--排水户-->
    <drainage-mapList ref="drainageMapList"></drainage-mapList>
    <drainage-map-list-new ref="drainageMapListNew"></drainage-map-list-new>
    <!-- 排水设施->管网数据->修补测工单 -->
    <data-check-draw ref="dataCheckDraw"></data-check-draw>
    <!--人员出勤监测-->
    <user-monitor ref="userMonitor"></user-monitor>
    <!--网格划定-->
    <grid-defined ref="gridDefined"></grid-defined>
    <!--新增计划-->
    <new-plan ref="newPlan"></new-plan>
    <!--计划详情台账-->
    <plan-account ref="planAccount"></plan-account>

    <!-- 一张图 -->
    <risk-one-map ref="riskOneMap"></risk-one-map>
    <clear-one-map ref="clearOneMap"></clear-one-map>

    <all-detail ref="allDetail"></all-detail>

    <!-- <addPipeWellDialog ref="AddPipeWellDialog" /> -->
    <!--车辆出行实时监测-->
    <car-monitor ref="carMonitor"></car-monitor>
    <!-- 新增水质分析点 -->
    <add-analysis ref="addAnalysis"></add-analysis>
    <!-- 专题图 -->
    <thematic-map ref="thematicMap"></thematic-map>
    <!--设备列表-->
    <device-list ref="deviceList" v-if="showFacilityList"></device-list>
    <!--设备dialog面板-->
    <device-panel ref="devicePanel" v-if="showFacilityList"></device-panel>
    <!-- 设备详情-->
    <device-detail></device-detail>
    <!--车辆轨迹回放-->
    <car-patrol-track ref="carPatrolTrack"></car-patrol-track>
    <car-patrol-track-emer ref="carPatrolTrackEmer"></car-patrol-track-emer>
    <!--巡查轨迹回放-->
    <patrol-track-plugin ref="patrolTrackPlugin"></patrol-track-plugin>
    <!--工程管理->问题上报-->
    <problem-report-map-list ref="problemReportMapList"></problem-report-map-list>
    <problem-report-map-list-show ref="problemReportMapListShow"></problem-report-map-list-show>
    <!--清疏管理->计划清疏-->
    <plan-clear-hyd-map-list ref="planClearHydMapList"></plan-clear-hyd-map-list>
    <!-- 排口管理->地图展示 -->
    <outlet-map-list ref="outletMapList"></outlet-map-list>
    <!-- 河涌管理->地图展示 -->
    <river-map-list ref="riverMapList"></river-map-list>
    <river-map-list2 ref="riverMapList2"></river-map-list2>
    <!-- 河涌排口地图展示 -->
    <river-outlet-map-list ref="riverOutletMapList"></river-outlet-map-list>
    <river-outlet-map-list-pic ref="riverOutletMapListPic"></river-outlet-map-list-pic>
    <!-- 追溯工具 -->
    <edit-trace-tool ref="editTraceTool"></edit-trace-tool>
    <trace-main-pipe ref="traceMainPipe"></trace-main-pipe>
    <!-- 外委图片 -->
    <entrust-user-pic ref="entrustUserPic"></entrust-user-pic>
    <drainage-construct-list ref="drainageConstructList"></drainage-construct-list>
    <!-- 在建工地new -->
    <drainage-construct-list-new ref="drainageConstructListNew"></drainage-construct-list-new>
    <!-- 布防点新增修改 -->
    <add-deploy-point ref="addDeployPoint"></add-deploy-point>
    <!-- 启动布防 -->
    <start-protection ref="startProtection"></start-protection>
    <!-- 管线增删改查 -->
    <yxt-pipe-add ref="yxtPipeAdd"></yxt-pipe-add>
    <yxt-pipe-connect ref="yxtPipeConnect"></yxt-pipe-connect>
    <yxt-pipe-connect-sewage ref="yxtPipeConnectSewage"></yxt-pipe-connect-sewage>
    <yxt-pipe-delete ref="yxtPipeDelete"></yxt-pipe-delete>
    <yxt-pipe-recycle-bin ref="yxtPipeRecycleBin"></yxt-pipe-recycle-bin>
    <yxt-pipe-edit ref="yxtPipeEdit"></yxt-pipe-edit>
    <yxt-pipe-connect-slice ref="yxtPipeConnectSlice"></yxt-pipe-connect-slice>
    <!-- 轨迹播放插件 -->
    <play-handler-plugin ref="playHandlerPlugin"></play-handler-plugin>

    <!--泵站管理-->
    <map-add-edit ref="mapAddEdit"></map-add-edit>
    <pump-inspection-plan-draw-line ref="pumpInspectionPlanDrawLine"></pump-inspection-plan-draw-line>
    <pump-inspection-plan-draw-line-back></pump-inspection-plan-draw-line-back>
    <!--隐患点管理-->
    <app-risk-point-manager ref="appRiskPointManager"></app-risk-point-manager>
    <!-- 地图调试信息 -->
    <map-debug ref="mapDebug"></map-debug>
    <!--普通图层-->
    <general-layer ref="generalLayer"></general-layer>
    <!-- 上报工单浮窗 -->
    <div v-for="item in workOrderDetailList" :key="item">
        <work-order-detail-plugin ref="workOrderDetailPlugin"></work-order-detail-plugin>
    </div>
    <div class="movingTarget" v-show="yxtPipeIconList.addVisible"><i class="el-icon-plus"></i></div>
    <!-- 地图服务图片 -->
    <start-protection-pic ref="startProtectionPic"></start-protection-pic>
    <div class="map-tools x-row">
        <div class="toolControl x-col-fixed x-col-center">
            <el-dropdown @command="mapDropdownClick">
                <el-button type="primary" class="left-primary">
                    {{ getDropDownName }}<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown" class="my-dropdown">
                    <el-dropdown-item command="search">设施查询</el-dropdown-item>
                    <!--<el-dropdown-item command="sewerage">排水户录入</el-dropdown-item>-->
                    <el-dropdown-item command="pickupToDg2000">平面坐标拾取</el-dropdown-item>
                    <el-dropdown-item command="location">平面坐标定位</el-dropdown-item>
                    <el-dropdown-item command="pickup">经纬度拾取</el-dropdown-item>
                    <el-dropdown-item command="latLongLocation">经纬度定位</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
        <div class="toolContent x-col-center" v-show="command!==0">
            <!--地图搜索-->
            <div v-show="mapSearch" class="map-search">
                <el-input v-model="searchWord" placeholder="可查询作业编号、道路名和地标名" ref="searchInput"
                          @keyup.native.enter="setInputeValue(searchWord)" @change="mapSearchChange"
                          @input="handleInput(searchWord)" clearable></el-input>
                <el-button type="warning" class="searchBtn" @click="setInputeValue(searchWord)">搜索
                </el-button>
            </div>
            <div class="search-resource" v-show="searchResourceVisible && command!='location'&&searchWord!==''">
                <div v-show="searchLoadingVisible" class="searchLoading text-center">
                    <i style="font-size: 20px;" class="el-icon-loading"></i>搜索中...
                </div>
                <ul v-show="searchResource.length>0">
                    <li v-for="item in searchResource" class="res-li" @click="searchLocation(item)">
                        <div class="x-row">
                            <div><span style="line-height: 18px;font-size: 15px; padding-top: 2px;"
                                       class="el-icon-search"></span></div>
                            <div>
                                <span style="color: #333;">{{ item.value1 }}</span>
                                <span class="" style="color: #409eff;">({{ item.belong1 }})</span>
                                <span class="" style="color: #409eff;"
                                      v-if="!!item.regionText">{{ item.regionText }}</span>
                                <span style="color: #999;">
                                    <span class="" v-if="!!item.value2">{{ item.value2 }}</span>
                                    <span class="" v-if="!!item.value3">{{ item.value3 }}</span>
                                </span>
                            </div>
                        </div>


                    </li>
                </ul>
                <li v-show="searchResource.length==0 && searchLoadingVisible == false" class="noSearch"
                    @click="closeSearchLoading">未找到相关道路
                </li>
            </div>
        </div>
        <!--排水户录入-->
        <div v-show="searchAddXY" class="add-content  x-col-center">
            <div class="x-row">
                <div class="x-col">
                    <el-input v-model="searchX" class="input-control first-x" placeholder="GIS坐标 X" clearable>
                    </el-input>
                </div>
                <div class="x-col two-y">
                    <el-input v-model="searchY" class="input-control" placeholder="GIS坐标 Y" clearable></el-input>
                </div>
                <div class="x-col x-col-fixed">
                    <el-button type="warning" class="searchBtn" icon="search"
                               @click="clickLocation(searchX,searchY,'add')">录入定位
                    </el-button>
                </div>
            </div>
            <!--排水户录入-->
            <div class="sewerage-container" v-show="sewerageEntry">
                <span class="el-icon-close close-btn" @click="closeSewerage"></span>
                <div style="padding-top: 15px;background-color: #f8f8f8;">
                    <div style="padding: 10px 15px;">
                        <span
                                style="margin-right: 10px;margin-right: 25px;display: inline-block;color: #888;">X：{{ sewerageClickX }}</span>
                        <span>Y：{{ sewerageClickY }}</span>
                    </div>
                    <div v-show="!upload">
                        <div class=" inputRow numberform">
                            <span class="label-span">编号：</span>
                            <el-input placeholder="必填" v-model="sewerageCode" clearable>
                            </el-input>
                        </div>
                        <div class=" inputRow">
                            <span class="label-span">备注：</span>
                            <el-input placeholder="选填" v-model="remark" clearable>
                            </el-input>
                        </div>
                        <div class="nextDiv">
                            <!--<span v-show="false" style="color: red;">排水户编码已存在，请重新输入</span>-->
                            <el-button type="primary" @click="addSewerageInfo">保存</el-button>
                            <el-button @click="cancelSewerageInfo">取消</el-button>
                        </div>
                    </div>
                    <div v-show="upload" class="upAfter">
                        <div class="row-di">
                            编号：{{ sewerageCode }}
                        </div>
                        <div class="row-di" v-show="remark" style="max-height: 5.7em;overflow-y: auto;">
                            备注：{{ remark }}
                        </div>
                    </div>

                    <div v-show="upload" class="uploadDiv">
                        <el-upload class="upload-demo" ref="upload" :data="uploadParm" :file-list="fileList"
                                   :action="uploadURL" :on-success="handleSuccess" :on-error="handleError"
                                   :auto-upload="false"
                                   multiple>
                            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
                            <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">
                                上传到服务器
                            </el-button>
                        </el-upload>

                    </div>
                </div>
            </div>
        </div>
        <!--坐标定位-->
        <div class="x-col-center">
            <div class="locationSearch  x-row" v-show="searchXY">
                <el-input v-model="dgTowgs.x" class="input-control first-x" placeholder="平面坐标 X"
                          clearable></el-input>
                <el-input v-model="dgTowgs.y" class="input-control " placeholder="平面坐标 Y" clearable></el-input>
                <el-button type="warning" class="searchBtn" icon="search"
                           @click="clickLocation(dgTowgs.x,dgTowgs.y,'location')">
                    定位
                </el-button>
            </div>
        </div>
        <div class="x-col-center">
            <div class="locationSearch  x-row" v-show="latLongLocationXY">
                <el-input v-model="latSearchX" class="input-control first-x" placeholder="经度 E" clearable></el-input>
                <el-input v-model="latSearchY" class="input-control " placeholder="纬度 N" clearable></el-input>
                <el-button type="warning" class="searchBtn" icon="search"
                           @click="wgs84ToMercator(latSearchX,latSearchY,'location')">
                    定位
                </el-button>
            </div>
        </div>
        <!--经纬度拾取-->
        <div class="pickupLocation" v-show="pickupXY">
            <div class="x-col-center">
                <div class="locationSearch  x-row" v-show="pickupXY" style="width: 280px;">
                    <el-input v-model="mapPointX" class="input-control first-x" placeholder="GIS坐标 X" clearable>
                    </el-input>
                    <el-input v-model="mapPointY" class="input-control " placeholder="GIS坐标 Y" clearable>
                    </el-input>
                </div>
            </div>
        </div>
        <!--坐标拾取-->
        <div class="pickupLocation" v-show="wgs84ToDg2000XY">
            <div class="x-col-center">
                <div class="locationSearch  x-row" v-show="wgs84ToDg2000XY" style="width: 280px;">
                    <el-input v-model="wgsTodg.x" class="input-control first-x" placeholder="平面坐标 X" clearable>
                    </el-input>
                    <el-input v-model="wgsTodg.y" class="input-control " placeholder="平面坐标 Y" clearable>
                    </el-input>
                </div>
            </div>
        </div>
    </div>
    <!--线选/多边形查询数据展示-->
    <div class="map-search-container data-container" v-show="isComplete">
        <el-container class="detail-content" v-show="!isDetail">
            <el-header class="clearfloat" height="40px">
                <span>图层列表</span>
                <span class="el-icon-close" @click="isComplete=false"></span>
            </el-header>
            <el-main>
                <div v-show="isSearchLoading" class="text-center loading-data">
                    <i class="el-icon-loading" style="font-size: 18px;"></i><span>拼命加载中...</span>
                </div>
                <div v-show="mapSearchArr.length==0 && !isSearchLoading" class="text-center"
                     style="color: #999;padding: 5px;">
                    <span>未找到相关数据</span>
                </div>

                <ul v-show="!isSearchLoading">
                    <li v-for="item in mapSearchArr" class="layer-li clearfloat" @click="toDetail(item)">
                        <el-tooltip placement="right" v-show="item.aliasName!='雨水口' && item.aliasName!='排放口'">
                            <div slot="content"
                                 v-if="item.aliasName==='排水管道'|| item.aliasName==='窨井'|| item.aliasName==='管线点'">
                                <div style="margin: 4px 0;font-size: 14px"
                                     v-if="pipeStatistics[item.tableName]['污水']">
                                    {{ pipeStatistics[item.tableName]['污水'].label }}：{{ pipeStatistics[item.tableName]['污水'].value }}
                                    <span v-show="item.aliasName==='排水管道'">米</span>
                                    <span v-show="item.aliasName!='排水管道'">个</span>
                                </div>
                                <div style="margin: 4px 0;font-size: 14px"
                                     v-if="pipeStatistics[item.tableName]['雨水']">
                                    {{ pipeStatistics[item.tableName]['雨水'].label }}：{{ pipeStatistics[item.tableName]['雨水'].value }}
                                    <span v-show="item.aliasName==='排水管道'">米</span>
                                    <span v-show="item.aliasName!='排水管道'">个</span>
                                </div>
                                <div style="margin: 4px 0;font-size: 14px"
                                     v-if="pipeStatistics[item.tableName]['雨污合流']">
                                    {{ pipeStatistics[item.tableName]['雨污合流'].label }}：{{ pipeStatistics[item.tableName]['雨污合流'].value }}
                                    <span v-show="item.aliasName==='排水管道'">米</span>
                                    <span v-show="item.aliasName!='排水管道'">个</span>
                                </div>
                            </div>
                            <div slot="content" v-else-if="item.aliasName==='沟渠'">
                                <div style="margin: 4px 0;font-size: 14px"
                                     v-if="!!pipeStatistics[item.tableName]['明渠']">
                                    {{ pipeStatistics[item.tableName]['明渠'].label }}：{{ pipeStatistics[item.tableName]['明渠'].value }}米
                                </div>
                                <div style="margin: 4px 0;font-size: 14px"
                                     v-if="!!pipeStatistics[item.tableName]['渠箱']">
                                    {{ pipeStatistics[item.tableName]['渠箱'].label }}：{{ pipeStatistics[item.tableName]['渠箱'].value }}米
                                </div>
                            </div>
                            <span>{{ item.aliasName }}</span>
                        </el-tooltip>
                        <span v-show="item.aliasName==='雨水口'|| item.aliasName==='排放口'">{{ item.aliasName }}</span>
                        <span class="feature-count">{{ item.featureCount }}</span>
                    </li>

                </ul>
            </el-main>
        </el-container>
        <el-container class="detail-content" v-show="isDetail && !isAttributes">
            <el-header class="text-center clearfloat" height="40px">
                <span>数据列表</span>
                <span class="el-icon-arrow-left detail-back-btn" @click="isDetail=false"></span>
                <span class="el-icon-close" @click="isComplete=false"></span>
            </el-header>
            <el-main>
                <div v-show="isDetailLoading" class="text-center loading-data" style="padding: 10px 0;">
                    <i class="el-icon-loading" style="font-size: 18px;"></i><span>拼命加载中...</span>
                </div>
                <div v-show="mapSearchArr.length==0 && !isDetailLoading" class="text-center"
                     style="color: #999;padding: 5px;">
                    <span>未找到相关数据</span>
                </div>
                <ul v-show="!isDetailLoading">
                    <li v-for="items in detailDataArr" class="layer-li detail-li clearfloat">
                        <h6 class="primary-id" @click="toAttributes(items)">{{ items.primaryField }}</h6>
                        <div class="x-row" v-for="item in items.fieldLists" @click="fieldItem(items)">
                            <span class="x-col x-col-center x-col-fixed text-right">{{ item.key }}:</span>
                            <span class="x-col x-col-center">{{ item.value }}</span>
                        </div>
                    </li>
                </ul>
            </el-main>
            <el-footer height="36px" v-show="mapSearchArr.length>0 && !isDetailLoading">
                <div class="text-center">
                    <el-pagination small :current-page="listCurrentPage" :page-count="listPageCount"
                                   @current-change="mapListCurrentChange" layout="prev, pager, next">
                    </el-pagination>
                </div>
            </el-footer>
        </el-container>
        <el-container class="detail-content" v-show="isAttributes">
            <el-header class="clearfloat text-center" height="70px">
                <span @click="isAttributes=false"><i class="el-icon-arrow-left detail-back-btn"></i></span>
                <span class="el-icon-close" @click="isComplete=false"></span>
                <div style="margin-top: 30px;">
                    <h5 style="padding-top: 2px;">{{ fieldItemName }}</h5>
                </div>
            </el-header>
            <el-main>
                <div v-show="isAttributesLoading" class="text-center loading-data">
                    <i class="el-icon-loading" style="font-size: 18px;"></i><span>拼命加载中...</span>
                </div>
                <div v-show="fieldListsAttr.length==0 && !isAttributesLoading" class="text-center"
                     style="color: #999;padding: 5px;">
                    <span>未找到相关数据</span>
                </div>
                <ul v-show="fieldListsAttr.length>0 && !isAttributesLoading" class="detail-li">
                    <li class="x-row" v-for="item in fieldListsAttr">
                        <span class="x-col x-col-center x-col-fixed text-right">{{ item.key }}:</span>
                        <span class="x-col x-col-center">{{ item.value }}</span>
                    </li>
                </ul>
            </el-main>
        </el-container>
    </div>
    <!--点选排版-->
    <el-container class="point-content data-container" v-show="isPointContent">
        <el-header class="layer-view" height="auto">
            <el-container>
                <el-header class="top-header-select" height="75px">
                    <div style="padding-top: 5px;" class="text-right">
                        <i class="el-icon-close" @click="isPointContent=false"></i>
                    </div>
                    <el-dropdown class="clearfloat" trigger="click" @command="clickLayers">
                        <div class="el-dropdown-link">
                            <i class="icon iconfont icon-tuceng el-icon--left"></i>
                            <span>{{ selectLayer }}</span>
                            <i class="el-icon-caret-bottom el-icon--right"></i>
                        </div>
                        <el-dropdown-menu slot="dropdown" class="select-layer">
                            <el-dropdown-item v-for="item in pipeLayers" :command="item">
                                <span>{{ item.text }}</span>
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <div v-show="isPointLoading" class="text-center loading-data">
                        <i class="el-icon-loading" style="font-size: 18px;"></i><span>拼命加载中...</span>
                    </div>
                    <div v-show="pointClickDataArr.length==0 && !isPointLoading" class="text-center"
                         style="padding: 10px;">
                        <span>未找到相关数据</span>
                    </div>
                </el-header>
                <el-main class="header-layer-main" v-show="isAliasLi">
                    <el-collapse accordion>
                        <el-collapse-item v-for="items in pointClickDataArr">
                            <template slot="title">
                                {{ items.aliasName }}
                            </template>
                            <div v-for="item in items.features" @click="clickPointLayer(item)">
                                {{ item.primaryField }}
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                </el-main>
            </el-container>
        </el-header>
        <el-main class="point-attr" v-show="isShowPointAttr">
            <el-container>
                <el-header class="clearfloat text-center" height="60px">
                    <div style="margin-top: 18px;">
                        <h5 style="padding-top: 5px;">{{ pointAttrValue.primaryField }}</h5>
                    </div>
                </el-header>
                <el-main style="overflow-y: visible">
                    <ul class="detail-li">
                        <li class="x-row" v-for="item in pointAttrValue.fieldDetails">
                            <span class="x-col x-col-center x-col-fixed text-right">{{ item.key }}:</span>
                            <span class="x-col x-col-center">{{ item.value }}</span>
                        </li>
                    </ul>
                </el-main>
            </el-container>
        </el-main>
    </el-container>
    <!--截图图片的放大-->
    <el-dialog :visible.sync="showBigImg" :show-close="false" class="imageBox">
        <div class="imgEnlarge" @click="closeBigImg">
            <img :src="currentPic" alt="图片最大化">
        </div>
    </el-dialog>
    <!-- 撤销布防 -->
    <el-dialog :visible.sync="cancelDeployVisible" class="cancelDeployDialog">
        <div class="cancelIcon">
            <img width="68" height="68" src="../../img/appDailyInspection/感叹号.png" alt="">
        </div>
        <div class="cancelTitle">
            是否确定撤销布防
        </div>
        <div class="bottomBtn">
            <el-button type="warning" round @click.stop="cancelDeployVisible = false">取消</el-button>
            <el-button type="primary" round @click="cancelDeploy">确定</el-button>
        </div>
    </el-dialog>
    <!--区域裁剪提交-->
    <region-cut-form ref="regionForm"></region-cut-form>
    <!-- 分析区块 -->
    <analysis-block-cut-form ref="analysisBlockCutForm"></analysis-block-cut-form ref="regionForm">
    <!--用户信息编辑-->
    <user-info-update></user-info-update>
    <!--修改密码-->
    <update-password></update-password>

    <system-selector ref="systemSelector"></system-selector>
    <!-- 排水单元去向分析 -->
    <unit-analyze ref="unitAnalyze"></unit-analyze>
    <!-- 排水单元情况统计 -->
    <unit-statistics ref="unitStatistics"></unit-statistics>
    <!-- 关注点管理 -->
    <concern-manage ref="concernManage"></concern-manage>
    <execute-log ref="executeLog"></execute-log>
    <region-info-edit ref="regionInfoEdit"></region-info-edit>
    <!--自相交提示-->
    <el-dialog title="绘制图形存在自相交，需要重新绘制，可能存在以下情况:" :visible.sync="dialogVisible" width="30%">
        <div class="x-row exampleRow">
            <div class="x-col x-col-center">
                <img class="x-col" src="../../img/example/example1.png"/>
            </div>
            <div class="x-col x-col-center">
                <img class="x-col" src="../../img/example/example2.png"/>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
        </span>
    </el-dialog>
    <!--河流、湖泊、流域-->
    <service-attribute ref="serviceAttribute"></service-attribute>
    <el-dialog title="问题描述" class="questionDialog" append-to-body :visible.sync="questionDialogVisible"
               :before-close="handleClose" width="550px">
        <el-form :model="questionForm" ref="questionForm" label-width="80px">
            <el-form-item label="问题类型">
                <el-select v-model="questionForm.problemType" placeholder="请选择问题类型"
                           :disabled="!questionForm.editable">
                    <el-option label="管网连接关系异常" value="管网连接关系异常"></el-option>
                    <el-option label="管网排水类型异常" value="管网排水类型异常"></el-option>
                    <el-option label="管网数据与现状不符" value="管网数据与现状不符"></el-option>
                    <el-option label="其他类型" value="其他类型"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="问题描述" :disabled="!questionForm.editable">
                <el-input v-model="questionForm.description" autocomplete="off" type="textarea" rows="4"
                          :disabled="!questionForm.editable"></el-input>
            </el-form-item>
            <el-form-item label="上报人" :disabled="!questionForm.editable" v-show="!questionForm.editable">
                <el-input v-model="questionForm.problemReporter" autocomplete="off" :disabled="!questionForm.editable">
                </el-input>
            </el-form-item>
            <el-form-item label="上报时间" :disabled="!questionForm.editable" v-show="!questionForm.editable">
                <el-input v-model="questionForm.reportTime" autocomplete="off" :disabled="!questionForm.editable">
                </el-input>
            </el-form-item>

        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="createQuestionArea" v-show="questionForm.editable"
                       :disabled="!questionForm.problemType">确 定</el-button>
            <el-button type="warning" @click="deleteQuestionArea" v-show="!questionForm.editable">删 除</el-button>
        </span>
    </el-dialog>
    <div class="controlPanel" v-show="questionCommand>-1">
        <span class="panelTitle">数据问题专题图</span>
        <el-button size="medium" :class="{highLight:questionCommand==1}" @click="showQuestionMark"><i
                class="buttonIcon esri-icon-plus-circled"></i>新增
        </el-button>
        <el-button size="medium" @click="redraw"><i class="buttonIcon esri-icon-refresh"></i>重绘
        </el-button>
        <el-button size="medium" @click="closeQuestionArea"><i class="buttonIcon esri-icon-close"></i>关闭</el-button>
    </div>
    <div class="facilityContainer" style="display: none">
        <div class="containerTitle" :class="currentStatus">{{ highLightFacility.item.name }}</div>
        <div class="containerRow">
            <div class="rowTitle">测站别名</div>
            <div class="rowDetail">{{ highLightFacility.item.aliasName }}</div>
        </div>
        <div class="containerRow">
            <div class="rowTitle">测站标识</div>
            <div class="rowDetail">{{ highLightFacility.item.fid }}</div>
        </div>
        <!-- <div class="rowTitle">原设备名</div>
        <div class="rowDetail">{{highLightFacility.item.oldName}}</div> -->
    </div>
    <div class="containerRow" v-if="highLightFacility.unknowPoint">
        <div class="rowTitle">管理单位</div>
        <div class="rowDetail">市净水公司</div>
    </div>

    <div class="containerRow"
         v-if="!highLightFacility.unknowPoint && highLightFacility.facilityTypeName=='WD' && highLightFacility.item.onlineState">
        <div class="rowTitle">测量水深</div>
        <div class="rowDetail">{{ highLightFacility.item.dValue }}m</div>
    </div>
    <div class="containerRow"
         v-if="!highLightFacility.unknowPoint && highLightFacility.facilityTypeName=='WD'&& highLightFacility.item.onlineState">
        <div class="rowTitle">城建液位</div>
        <div class="rowDetail">{{ highLightFacility.item.urbanWaterLine }}m</div>
    </div>
    <div class="containerRow"
         v-if="!highLightFacility.unknowPoint && highLightFacility.facilityTypeName=='WD'&& highLightFacility.item.onlineState && highLightFacility.item.checkPoint=='1'">
        <div class="rowTitle">COD</div>
        <div class="rowDetail">{{ highLightFacility.item.cod }}mg/L</div>
    </div>
    <div class="containerRow"
         v-if="!highLightFacility.unknowPoint && highLightFacility.facilityTypeName=='WD'&& highLightFacility.item.onlineState && highLightFacility.item.checkPoint=='1'">
        <div class="rowTitle">氨氮</div>
        <div class="rowDetail">{{ highLightFacility.item.ammonia }}mg/L</div>
    </div>
    <div class="containerRow"
         v-if="!highLightFacility.unknowPoint && highLightFacility.facilityTypeName=='WD'&& highLightFacility.item.onlineState && highLightFacility.item.checkPoint=='1'&& !!highLightFacility.item.cod2">
        <div class="rowTitle">COD#2</div>
        <div class="rowDetail">{{ highLightFacility.item.cod2 }}mg/L</div>
    </div>
    <div class="containerRow"
         v-if="!highLightFacility.unknowPoint && highLightFacility.facilityTypeName=='WD'&& highLightFacility.item.onlineState && highLightFacility.item.checkPoint=='1'&& !!highLightFacility.item.ammonia2">
        <div class="rowTitle">氨氮#2</div>
        <div class="rowDetail">{{ highLightFacility.item.ammonia2 }}mg/L</div>
    </div>
    <div class="containerRow"
         v-if="!highLightFacility.unknowPoint && highLightFacility.facilityTypeName=='WD'&& highLightFacility.item.onlineState && highLightFacility.item.checkPoint=='1'">
        <div class="rowTitle">检测日期</div>
        <div class="rowDetail">{{ highLightFacility.item.codUpdateTime }}</div>
    </div>
    <div class="containerRow" v-if="highLightFacility.facilityTypeName=='RF'">
        <div class="rowTitle">实时降雨量</div>
        <div class="rowDetail">{{ highLightFacility.item.dValue }}mm</div>
    </div>
    <div class="containerRow" v-if="highLightFacility.facilityTypeName=='RF'">
        <div class="rowTitle">3小时累计</div>
        <div class="rowDetail">{{ highLightFacility.item.dValue3 }}mm</div>
    </div>
    <div class="containerRow" v-if="highLightFacility.facilityTypeName=='RF'">
        <div class="rowTitle">6小时累计</div>
        <div class="rowDetail">{{ highLightFacility.item.dValue6 }}mm</div>
    </div>
    <div class="containerRow" v-if="highLightFacility.facilityTypeName=='RF'">
        <div class="rowTitle">12小时累计</div>
        <div class="rowDetail">{{ highLightFacility.item.dValue12 }}mm</div>
    </div>
    <div class="containerRow" v-if="!highLightFacility.unknowPoint && highLightFacility.facilityTypeName=='WD'">
        <div class="rowTitle">安装地址</div>
        <div class="rowDetail">{{ highLightFacility.item.position }}</div>
    </div>

</div>
</div>