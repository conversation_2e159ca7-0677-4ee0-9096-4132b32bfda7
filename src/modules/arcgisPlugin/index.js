var Vue = require("vue");
var template = require("./map.html");
var serviceHelper = require("services/serviceHelper");
var eventHelper = require("utils/eventHelper");
var toolBar = require("./plugin/toolBar/toolBar");
var coordinateTopick = require("utils/coordinateTopick");
var commonUtil = require("utils/common");
var pipeHandover = require("./plugin/pipeHandover");
var pipeRoadEdit = require("./plugin/pipeRoadEdit");
var mixConnectPoint = require("./plugin/mixConnectPoint");
var markPointList = require("./plugin/markPointList");
var sewageWaterThematicMap = require("./plugin/sewageWaterThematicMap");
var editPipeInfo = require("./plugin/editPipeInfo");
var taskCheck = require("./plugin/taskCheck");
var dataCheckWorkOrder = require("./plugin/dataCheckWorkOrder");
var maintainDetail = require("./plugin/maintainDetail");
var facilityController = require("controllers/facilityController");
//var infoWindow = require('./plugin/infoWindow');
var facilityModel = require("controllers/model/facilityModel");
var mapHelper = require("utils/mapHelper");
var retrospectDetail = require("modules/executionMap/retrospectDetail");
var appRiskPoint = require("modules/executionMap/facilityMainy/appRiskPoint");
var appRiskPointStatistic = require("modules/executionMap/facilityMainy/appRiskPointStatistics");
var drainageList = require("./plugin/drainageList");
var pollutionAnalysis = require("./plugin/pollutionAnalysis");
var pipeCheck = require("./plugin/pipeCheck");
var traceabilityAnalysis = require("./plugin/traceabilityAnalysis");
var syAnalysis = require("./plugin/syAnalysis");
var analysisDialog = require("./plugin/pollutionAnalysisDialog");
var statusTools = require("modules/onlineMonitor/mapPlugin/statusTools");
//var infoWindowCustom = require("./plugin/infoWindowCustom");
var arcgisMapController = require("controllers/arcgisMapController");
// var flowAnalyze = require('modules/onlineMonitor/flowAnalyze');//监测
var regionCutForm = require("./plugin/regionCutForm");
var analysisBlockCutForm = require("./plugin/analysisBlockCutForm");
var initPlugin = {};
var traceabilityDialog = require("./plugin/traceabilityDialog");
var regionCutController = require("controllers/regionCutController");
var legendView = require("./plugin/legendView");
var legendControl = require("./plugin/legendControl");
var serviceAttribute = require("./plugin/serviceAttribute");
var userInfoUpdate = require("modules/system/authentication/userInfoUpdate");
var updatePassword = require("modules/system/authentication/updatePassword");
var waterMark = require("modules/appWaterMark");
var systemSelector = require("./plugin/systemSelector");
var unitAnalyze = require("./plugin/unitAnalyze");
var unitStatistics = require("./plugin/unitStatistics");
var concernManage = require("./plugin/concernManage");
var executeLog = require("./plugin/executeLog");
var regionInfoEdit = require("./plugin/regionInfoEdit");
var deviceList = require("modules/onlineMonitor/mapPlugin/deviceList");
var devicePanel = require("modules/onlineMonitor/mapPlugin/devicePanel");
var deviceDetail = require("modules/onlineMonitor/mapPlugin/deviceDetail");
var pipeService = require("services/pipeService");
var rightPanelCtrl = require("./plugin/rightPanelControl");
var splitContrl = require("controllers/appSplitScreenController");
let appSplitScreenController = require("controllers/appSplitScreenController");
let analysisBlockService = require("services/analysisBlockService");
var arcgisAPI = require("utils/arcgisAPI");
let permission = require("utils/permission");
//let CanvasLayer = require('./plugin/canvasLayer/CanvasLayer');
//let CanvasArrow = require('./plugin/canvasLayer/CanvasArrow');
var cacheFacilities = [];
var vipPoints = require("services/mock/device").vipStations;
var interval;
var appMenuModel = require("controllers/model/appMenuModel");
var standerSortLayerIds = [
    "pipe-line-sort-rain",
    "pipe-line-sort-dirty",
    "pipe-line-sort-mix",
];
var dataCheckDraw = require("./plugin/dataCheckDraw"); //数据核查工单数据导出

var layerControlHelper = require("./layerControlHelper");
var initJOmap = require("./initJOmap");

//新增分析点
var addAnalysis = require("./plugin/addAnalysis");
//专题图
var thematicMap = require("./plugin/thematicMap");

//排水户
let drainageMapList = require("../drainageUserManage/mapList");
//在建工地
let drainageConstructList = require("../drainageUserManage/mapConstructList");

//排水户
let drainageMapListNew = require("../drainageUserManageNew/mapList");
//在建工地
let drainageConstructListNew = require("../drainageUserManageNew/mapConstructList");

//人员出勤监测
let userMonitor = require("../appDailyInspection/monitorManage/userMonitor");
//车辆出行实时监测
let carMonitor = require("../appDailyInspection/monitorManage/carMonitor");
//车辆轨迹回放
let carPatrolTrack = require("../appDailyInspection/monitorManage/carPatrolTrack");
let carPatrolTrackEmer = require("../appDailyInspection/monitorManage/carPatrolTrackEmer");
//巡查轨迹回放
let patrolTrackPlugin = require("../appDailyInspection/monitorManage/userPatrolTrack/patrolTrackPlugin");

// 工程管理->问题上报
let problemReportMapList = require("../appDailyInspection/engineeringManage/problemReportMapList");
let problemReportMapListShow = require("../appDailyInspection/engineeringManage/problemReportMapListShow");
// 清疏管理->计划清疏
let planClearHydMapList = require("../appDailyInspection/planClearHyd/planClearHydMapList");
// 河涌管理->详情
let riverMapList = require("../appDailyInspection/outletManage/riverMapList");
let riverMapList2 = require("../appDailyInspection/outletManage/riverMapList2");

// 河涌排口地图展示点击显示片区
// let riverOutletMapListPic = require('../appDailyInspection/outletManage/riverOutletMapListPic');

// 管线追溯工具
var editTraceTool = require("modules/executionMap/pipeTraceManage/editTraceTool");
var traceMainPipe = require("modules/executionMap/pipeTraceManage/traceMainPipe");
// 外委用户图片
var entrustUserPic = require("../appDailyInspection/entrustUserPic");
const userModel = require("controllers/model/userModel");
// 布防点
let addDeployPoint = require("../appDailyInspection/protectionManage/deployPointManage/addDeployPoint");
let startProtection = require("../appDailyInspection/protectionManage/deployStartupRecordManage/startProtection");
let startProtectionPic = require("../appDailyInspection/protectionManage/deployStartupRecordManage/startProtectionPic");
// 运行图管线增删改查
let yxtPipeAdd = require("../executionMap/yxtPipeOperate/yxtPipeAdd");
let yxtPipeConnect = require("../executionMap/yxtPipeOperate/yxtPipeConnect");
let yxtPipeConnectSewage = require("../executionMap/yxtPipeOperate/yxtPipeConnectSewage");
let yxtPipeDelete = require("../executionMap/yxtPipeOperate/yxtPipeDelete");
let yxtPipeRecycleBin = require("../executionMap/yxtPipeOperate/yxtPipeRecycleBin");
let yxtPipeEdit = require("../executionMap/yxtPipeOperate/yxtPipeEdit");
let yxtPipeConnectSlice = require("../executionMap/yxtPipeOperate/yxtPipeConnectSlice");
var yxtPipeOperateService = require("services/yxtPipeOperateService");

// 排口管理->地图展示
// let outletMapList = require('../appDailyInspection/outletManage/outletMapList');
// 河涌排口地图展示点击显示片区
// let riverOutletMapList = require('../appDailyInspection/outletManage/riverOutletMapList');

//问题工单浮窗
let workOrderDetailPlugin = require("../appDailyInspection/monitorManage/userPatrolTrack/workOrderDetailPlugin");
//轨迹播放控制
let playHandlerPlugin = require("../appDailyInspection/monitorManage/userPatrolTrack/playHandlerPlugin");

//泵站管理新增、编辑
let mapAddEdit = require("../pumpInspectNew/pumpManage/mapAddEdit");
let pumpInspectionPlanDrawLine = require("../pumpInspectNew/pumpInspectionPlan/pumpInspectionPlanDrawLine");
let pumpInspectionPlanDrawLineBack = require("../pumpInspectNew/pumpInspectionPlan/pumpInspectionPlanDrawLineBack");

/*** 排口管理->地图展示（迁移仲恺） - start ***/
let outletMapList = require("../outletManage/outletMapList");
// 河涌排口地图展示点击显示片区
let riverOutletMapListPic = require("../outletManage/riverOutletMapListPic");
// 河涌排口地图展示点击显示片区
let riverOutletMapList = require("../outletManage/riverOutletMapList");
/*** 排口管理->地图展示（迁移仲恺） - end ***/

//隐患点管理
var appRiskPointManager = require("./plugin/appRiskPointManager");
const arcgisUtils = require("utils/arcgisUtils");
//通用图层
var generalLayer = require("./plugin/generalLayer");
//地图调试信息
var mapDebug = require("modules/executionMap/mapDebug");

//网格管理模块start
//网格划分
let gridDefined = require("../gridManagement/gridDefined");
//新增计划
let newPlan = require("../gridManagement/projectPlan/newPlan");
//计划详情
let planAccount = require("../gridManagement/planAccount");
//天气组件
let weather = require("../arcgisPlugin/plugin/weather");

//网格管理模块end

//一张图模块
let riskOneMap = require("../oneMapLayer/riskOneMap");
let clearOneMap = require("../oneMapLayer/clearOneMap");
let allDetail = require("../oneMapLayer/allDetail");
//一张图模块结束

// 定义组件
var comm = Vue.extend({
    template: template,
    mixins: [layerControlHelper],
    data: function () {
        window.a = this;
        return {
            allRegionLoad: false, //所有切片数据的加载
            dialogVisible: false, //自相交
            showFacilityList: false, //显示或隐藏设备列表
            captureState: "",
            locationGraphic: {},
            pointData: [],
            uploadURL: window.basicURL + "/uploadFile/batchUploadFile",
            fileList: [],
            uploadParm: {},
            bizId: "",
            caseImg: "",
            remark: "",
            questionForm: {
                problemType: "",
                description: "",
                wkt: "",
                problemReporter: "",
                reportTime: "",
                editable: true,
            },
            questionDialogVisible: false,
            cancelDeployVisible: false,
            saveStatus: false,
            showBigImg: false,
            currentPic: "",
            sewerageClickX: "",
            sewerageClickY: "",
            upload: false,
            sewerageCode: "",
            sewerageEntry: false,
            searchX: "",
            searchY: "",
            latSearchX: "",
            latSearchY: "",
            mapPointX: "",
            mapPointY: "",
            map_: "q",
            isloading: false,
            searchLoadingVisible: false,
            searchWord: "",
            searchResource: [],
            searchResourceVisible: false,
            mapSearchShow: true, //地图搜索
            message: "",
            roleName: "",
            userName: "",
            loginSuccess: false,
            detailOpen: false,
            facility: "",
            showtools: false,
            leftMap: {},
            iscreateSymbol: false,
            mapLoading: true,
            iscreateSymbols: false,
            isCreatePolygon: false,
            isCreateLine: false,
            layer: "",
            layers: [],
            lineLayers: [],
            drawGraphics: [],
            drawPointGraphics: [],
            drawLineGraphics: [],
            polygonId: 1,
            baseMap: {},
            baseView: {},
            graLayer: {},
            graphics: [],
            setCenterClick: false,
            highLightFacility: {
                item: {},
            },
            currentStatus: "",
            xyStates: "",
            mapSearch: true,
            searchAddXY: false,
            searchXY: false,
            latLongLocationXY: false,
            pickupXY: false,
            wgs84ToDg2000XY: false,
            command: 1,
            distanceArr: [],
            pointClickState: false,
            isQueryMark: false,
            searchDisabledVisible: false,
            allLabelRecords: [],
            mapSearchArr: [], //地图查询数据，
            pipeStatistics: {}, //地图查询管线长度统计，
            searchGraphic: [],
            detailDataArr: [],
            isSearchLoading: true,
            isDetailLoading: true,
            isComplete: false,
            mapSearchWkt: "",
            isDetail: false,
            isAttributes: false,
            isAttributesLoading: false,
            pipeLayers: [],
            fieldListsAttr: [],
            isPointContent: false,
            fieldItemName: "",
            isPointLoading: false,
            pointClickDataArr: [],
            isShowPointAttr: false,
            selectLayer: "",
            pointAttrValue: {},
            pointParams: {},
            listPageCount: 0,
            listCurrentPage: 1,
            completeEvt: {},
            toDetailParams: {},
            isAliasLi: false,
            questionCommand: -1,
            toolsVisible: false,
            pointGraphicArr: [],
            allRegionData: [], //所有片区数据,
            geometryArray: [], //片区geometry,
            isPipeExecute: false,
            isTaskCheck: false, //如果是任务审查模块，分屏幕展示地形图
            moveType: "",
            //污水专题图专用对象
            showSewageWaterThematicMap: false, //组件状态控制
            dataCheckOrderBtnVisible: false, // 核查按钮是否可见
            // 权限
            permission: {
                dataCheckOrderReport: false, // 核查工单权限
                createAndRead: false, // 核查工单查看和发起权限
            },
            isMapPointXY: false, // 判断坐标拾取时是否选中坐标
            setInEditPipeService: "",
            yxtPipeIconList: {
                addVisible: false,
            },
            workOrderDetailList: [],
            initArgs: [],
            wgsTodg: {
                x: "",
                y: "",
            },
            dgTowgs: {
                x: "", //平面坐标定位
                y: "", //平面坐标定位
            },
            connipType: "chart", //查询的数据库列席
            isFocus: false, //搜索框是否需要聚焦
        };
    },
    computed: {
        getDropDownName() {
            var nameDict = {
                search: "设施查询",
                location: "坐标定位",
                pickupToDg2000: "坐标拾取",
                pickup: "经纬度拾取",
                latLongLocation: "经纬度定位",
            };
            if (nameDict[this.command]) return nameDict[this.command];
            return "设施查询";
        },
    },
    watch: {
        showFacilityList: {
            handler() {
                if (this.showFacilityList) {
                    this.$nextTick(() => {
                        $(".map-tools").css({
                            left: "320px",
                        });
                    });
                } else {
                    this.$nextTick(() => {
                        $(".map-tools").css({
                            left: "10px",
                        });
                    });
                }
            },
            immediate: true,
        },
    },
    methods: {
        //勾选 某个图层
        checkLayer: function (boolean, name) {
            if (!name) {
                name = "排水单元";
            }
            if (boolean == undefined) {
                boolean = true;
            }
            //以上 设置name和boolean默认值
            if (!this.$refs.baseMap.$refs.layerControl) return;
            var layerTree = this.$refs.baseMap.$refs.layerControl.$refs.layerTree;
            var nodes = layerTree.store._getAllNodes();
            for (var i = 0, len = nodes.length; i < len; i++) {
                nodes[i].expanded = boolean; //关闭时无需收缩，改成true既可
            }
            var s = setTimeout(
                function () {
                    layerTree.setChecked(name, boolean);
                    for (var i = 0, len = nodes.length; i < len; i++) {
                        nodes[i].expanded = false; //关闭时无需收缩，改成true既可
                    }
                }.bind(this),
                10
            );
        },
        getQuestionArea: function () {
            if (!!this.currentQuestionLayer) {
                this.currentQuestionLayer.removeAll();
            }
            var style = {
                color: [64, 158, 255, 0.35],
                outline: {
                    color: [64, 158, 255, 0.6],
                    width: 2,
                },
            };
            pipeService.getQuestionArea(
                function (result) {
                    if (!!result.success) {
                        result.data.forEach(
                            function (area) {
                                if (!!area.wkt) {
                                    area.type = "questionArea";
                                    var polygon = this.apiInstance.wktToPolygon(
                                        area.wkt,
                                        this.baseView.spatialReference
                                    );
                                    this.apiInstance.createPolygon(
                                        this.currentQuestionLayer,
                                        polygon.rings,
                                        style,
                                        area
                                    );
                                }
                            }.bind(this)
                        );
                    }
                }.bind(this)
            );
        },
        closeQuestionArea: function () {
            this.questionCommand = -1;
            this.newQuestionLayer.removeAll();
            this.currentQuestionLayer.visible = false;
        },
        queryMarkByPoint: function (event) {
            var point = event.mapPoint;
            var form = {
                x: point.x,
                y: point.y,
                bufferDistance: this.apiInstance.screenLengthToMapLength(
                    this.baseView,
                    10
                ),
            };
            eventHelper.emit("loading-start");
            pipeService.queryMarkInChart(
                form,
                function (result) {
                    eventHelper.emit("loading-end");
                    if (!!result) {
                        if (result.length == 0) {
                            this.$message.error("你未选中任何标注，请重新选择");
                            return;
                        } else if (result.length > 1) {
                            this.$message.error("你选取了两个以上的标注，请重新选择");
                            return;
                        } else {
                            this.currentMarker = result.data[0];
                        }
                    }
                }.bind(this)
            );
        },
        queryMarker: function () {
            this.$message.info("功能开发中，敬请期待");
            return;
            this.$message.info("已打开运行图标注功能，请点击地图上的标注点进行查询");
            this.isQueryMark = true;
            this.checkLayer(true, "运行图标注");
        },
        showQuestionArea: function () {
            this.questionCommand = 0;
            this.currentQuestionLayer.visible = true;
            this.getQuestionArea();
        },
        createQuestionArea: function () {
            eventHelper.emit("loading-start");
            pipeService.createQuestionArea(
                this.questionForm.problemType,
                this.questionForm.description,
                this.questionForm.wkt,
                function (result) {
                    this.$message.success("您的工单已下发成功");
                    this.getQuestionArea();
                    this.handleClose();
                    eventHelper.emit("loading-end");
                }.bind(this)
            );
        },
        deleteQuestionArea: function () {
            this.$confirm("此操作将永久删除该记录, 是否继续?", "删除问题记录", {
                confirmButtonText: "确定删除",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                pipeService.deleteQuestionArea(
                    this.questionForm.id,
                    function (result) {
                        if (result.success) {
                            this.$message.success("已删除成功");
                            this.getQuestionArea();
                            this.handleClose();
                        }
                    }.bind(this)
                );
            });
        },
        redraw: function () {
            if (!this.pen) {
                this.pen = this.apiInstance.initDrawPen(
                    this.baseView,
                    this.newQuestionLayer,
                    function (area) {
                        this.questionForm.wkt = this.apiInstance.polygonToWKT(area);
                        this.questionDialogVisible = true;
                    }.bind(this)
                );
            } else {
                this.newQuestionLayer.removeAll();
                this.pen.endDraw();
            }
            this.pen.startDraw();
            this.questionCommand = 1;
        },
        handleClose: function () {
            for (var key in this.questionForm) {
                this.questionForm[key] = "";
            }
            this.questionForm.editable = true;
            this.newQuestionLayer.removeAll();
            this.questionDialogVisible = false;
            this.questionCommand = 0;
            if (!!this.pen) {
                this.pen.endDraw();
            }
        },
        showQuestionMark: function () {
            this.questionCommand = 1;
            this.$message("请画出问题区域");
            if (!this.pen) {
                this.pen = this.apiInstance.initDrawPen(
                    this.baseView,
                    this.newQuestionLayer,
                    function (area) {
                        this.questionForm.wkt = this.apiInstance.polygonToWKT(area);
                        this.questionDialogVisible = true;
                    }.bind(this)
                );
            }
            this.pen.startDraw();
        },
        //所有片区接口
        getDistrictData: function () {
            this.allRegionLoad = false;
            regionCutController.getAllManualDistrict(
                function (res) {
                    eventHelper.emit("loading-end");
                    this.allRegionLoad = true;
                    if (res.success) {
                        this.allRegionData = res.data;
                        if (this.geometryArray.length == 0) {
                            this.geometryArray = mapHelper.createGraphicAndText(
                                this.allRegionData,
                                this.baseView
                            );
                        }
                    }
                }.bind(this),
                function (error) {
                    console.log("获取所有片区信息错误" + error);
                }
            );
        },
        closeBigImg: function () {
            this.showBigImg = false;
            return;
        },
        getBufferRadius: function () {
            var zoom = this.baseView.zoom,
                bufferRadius;
            switch (zoom) {
                case 1:
                    bufferRadius = 1.5;
                    break;
                case 2:
                    bufferRadius = 1.6;
                    break;
                case 3:
                    bufferRadius = 1.7;
                    break;
                case 4:
                    bufferRadius = 1.8;
                    break;
                case 5:
                    bufferRadius = 1.9;
                    break;
                case 6:
                    bufferRadius = 2;
                    break;
                case 7:
                    bufferRadius = 2.1;
                    break;
                case 8:
                    bufferRadius = 2.2;
                    break;
                case 9:
                    bufferRadius = 2.3;
                    break;
                case 10:
                    bufferRadius = 2.4;
                    break;
                case 11:
                    bufferRadius = 2.5;
                    break;
                case 12:
                    bufferRadius = 2.6;
                    break;
                case 13:
                    bufferRadius = 2.7;
                    break;
                case 14:
                    bufferRadius = 2.8;
                    break;
                case 15:
                    bufferRadius = 2.9;
                    break;
                default:
                    bufferRadius = 3;
            }
            return bufferRadius;
        },
        showPointContent: function () {
            //点选功能
            // var bufferRadius = this.getBufferRadius();
            var bufferRadius = this.apiInstance.screenLengthToMapLength(
                this.baseView,
                8
            );
            this.pointParams["bufferDistance"] = bufferRadius;
            if (this.isPipeExecute) {
                this.pointParams["connip"] = "chart";
            } else {
                this.pointParams["connip"] = null;
            }
            this.isComplete = false;
            this.isPointContent = true;
            this.isPointLoading = true;
            this.isShowPointAttr = false;
            // this.isPipeCheck = false;
            this.isAliasLi = false;

            arcgisMapController.queryPipeByGeometry(
                this.pointParams,
                function (res) {
                    if (res.success === true) {
                        var data = res.data;
                        data.forEach(function (layer) {
                            layer.features.forEach(function (feature) {
                                if (!feature.primaryField) {
                                    feature.primaryField = feature.fieldDetails[0].value;
                                }
                            });
                        });
                        this.pointClickDataArr = data;
                        this.isPointLoading = false;
                        this.isAliasLi = true;
                        eventHelper.emit("querySelectData", data);
                        if (data.length > 0) {
                            // this.isShowPointAttr = true;
                        }
                    } else {
                        this.isPointLoading = false;
                        this.pointClickDataArr = [];
                    }
                }.bind(this),
                function (err) {
                    console.log("queryPipeByGeometry--请求错误：" + err.msg);
                }.bind(this)
            );
        },
        clickPointLayer: function (item) {
            this.pointAttrValue = item;
            this.isShowPointAttr = true;
            var wkt = item.wkt;
            if (wkt.indexOf("LINESTRING") >= 0) {
                this.addMapLine(wkt);
            } else if (wkt.indexOf("POINT") >= 0) {
                var imgObj = {
                    width: "16px",
                    height: "20px",
                    url: "./css/images/choucha.png",
                    xoffset: 0,
                    yoffset: "10px",
                };
                var attributes = {
                    facilityTypeName: item.primaryField,
                };
                this.clearGraLayer();
                mapHelper.createPictureMarkSymbol2(
                    this.graLayer,
                    wkt,
                    imgObj,
                    attributes
                );
            }
        },
        toAttributes: function (item) {
            this.isAttributes = true;
            // this.isAttributesLoading = true;
            this.fieldListsAttr = item.fieldDetails;
            this.fieldItemName = item.primaryField;
            // var t = setTimeout(function () {
            // this.isAttributesLoading = false;
            // window.clearTimeout(t);
            // }.bind(this), 100);
        },
        fieldItem: function (item) {
            if (!item.wkt) {
                console.log("当前列表定位未找到wkt");
                return;
            }
            var wkt = item.wkt;
            if (wkt.indexOf("POINT") >= 0) {
                this.graLayer.removeMany(this.pointGraphicArr);
                var XY = item.wkt
                    .replace("POINT", "")
                    .replace("(", "")
                    .replace(")", "")
                    .split(" ")
                    .filter(function (item) {
                        return !!item;
                    });
                var imgObj = {
                    width: "16px",
                    height: "20px",
                    url: "./css/images/choucha.png",
                    xoffset: 0,
                    yoffset: "10px", //坐标点需要向上偏移，因为坐标点的中心位置对应 x,y坐标
                };
                var attributes = {
                    facilityTypeName: item.primaryField,
                };
                var pointGraphic = mapHelper.createPictureMarkSymbol(
                    this.graLayer,
                    XY[0],
                    XY[1],
                    imgObj,
                    attributes
                );
                this.pointGraphicArr.push(pointGraphic);
                // this.baseView.goTo(pointGraphic.geometry.extent.expand(2));
                setTimeout(
                    function () {
                        //延迟触发，挪动中点，解决一开始点显示偏移的bug，
                        this.$refs.baseMap.setCenter(XY[0], XY[1]);
                    }.bind(this),
                    500
                );
            } else if (wkt.indexOf("LINESTRING") >= 0) {
                // var params = {
                //     state: "noLine"
                // };
                this.addMapLine(wkt);
            } else {
                console.log("找到fieldItem方法 ---类型漏判");
            }
        },
        mapSearchData: function (x, y, pointBufferDistance) {
            var params = {
                x: x,
                y: y,
                pointBufferDistance: pointBufferDistance,
            };
            arcgisMapController.queryPipeByGeometry(
                params,
                function (data) {
                    console.log("queryPipeByGeometry-请求成功:");
                    console.log(data);
                    return data;
                },
                function (error) {
                    console.log(
                        "请求错误  ：arcgisMapController/queryPipeByGeometry  : " + error
                    );
                    return "";
                }
            );
        },
        //测试打开排水户信息表
        openTestTABLE: function (evt) {
            eventHelper.emit("openTable", evt);
        },
        sewerageAddInfo: function (evt) {
            if (!!evt && this.xyStates == "add") {
                this.sewerageClickX = Math.floor(evt.mapPoint.x * 1000) / 1000;
                this.sewerageClickY = Math.floor(evt.mapPoint.y * 1000) / 1000;
                this.sewerageEntry = true;
            }
        },
        sewerageAdd: function () {
            this.mapSearch = false;
            this.searchXY = false;
            this.searchAddXY = true;
            this.pickupXY = false;
            this.wgs84ToDg2000XY = false;
        },
        addSewerageInfo: function () {
            var id = new Date().getTime(); //时间戳的唯一性
            var params = {
                drainHouseHoldCode: this.sewerageCode,
                x: this.sewerageClickX,
                y: this.sewerageClickY,
                remark: this.remark, //备注信息
                id: id,
            };
            arcgisMapController.drainHouseHoldFileSave(
                params,
                function (data) {
                    if (!!data) {
                        this.bizId = data.id;
                        var bizType = "sewerageUser";
                        this.upload = true;
                    } else {
                        this.upload = false;
                        $(".numberform .el-input__inner").css("border-color", "red");
                        $(".numberform .el-input__inner");
                    }
                }.bind(this),
                function (error) {
                    $(".numberform .el-input__inner").css("border-color", "red");
                }.bind(this)
            );
        },
        cancelSewerageInfo: function () {
            this.sewerageCode = "";
            this.remark = "";
            this.closeSewerage();
        },
        submitUpload: function (e) {
            this.uploadParm.bizId = this.bizId;
            this.uploadParm.bizType = "sewerageUser";
            this.uploadParm.token = serviceHelper.getToken();
            this.$refs.upload.submit();
        },
        handleSuccess: function (response, f, farr) {
            if (!!response.success) {
                var i = farr.indexOf(f);
                farr.splice(i, 1);
                if (farr.length == 0) {
                    this.$message.success("上传完成");
                }
            } else {
                this.$message.error("上传失败,原因:" + response.msg);
                // return false;
            }
            // this.fileList.length=0;
        },
        handleError: function (e, file, filearr) {
            this.$message.error("文件上传失败");
        },
        searchXy: function () {
            this.mapSearch = false;
            this.searchXY = true;
            this.searchAddXY = false;
            this.pickupXY = false;
            this.wgs84ToDg2000XY = false;
        },
        latLongLocationXy: function () {
            this.mapSearch = false;
            this.searchXY = false;
            this.searchAddXY = false;
            this.pickupXY = false;
            this.latLongLocationXY = true;
            this.wgs84ToDg2000XY = false;
        },
        pickupXy: function () {
            this.mapSearch = false;
            this.searchXY = false;
            this.searchAddXY = false;
            this.pickupXY = true;
            this.wgs84ToDg2000XY = false;
        },
        wgs84ToDg2000() {
            this.mapSearch = false;
            this.searchXY = false;
            this.searchAddXY = false;
            this.pickupXY = false;
            this.wgs84ToDg2000XY = true;
        },
        //坐标定位
        clickLocation: function (x, y, state, myZoom) {
            serviceHelper.getJson(
                serviceHelper.getPath("coordTransDg2000ToWgs84"),
                {
                    x,
                    y,
                },
                (res) => {
                    var params = {
                        x: res.x,
                        y: res.y,
                        state: state,
                        icon: "icon03",
                        myZoom: myZoom,
                    };
                    this.locationGraphic = this.goLocation(params);
                },
                (err) => {
                    this.$message.error("坐标转换失败");
                }
            );
        },
        // 经纬度定位
        wgs84ToMercator: function (lonX, latY) {
            // 东莞的就是wgs84，不需要转墨卡托
            // let xy = {x: '', y: ''};
            // let x = +lonX * 2.0035508342789E7 / 180.0;
            // let y = Math.log(Math.tan((90.0 + +latY) * 3.141592653589793 / 360.0)) / 0.017453292519943295;
            // y = y * 2.003460834789E7 / 180.0;
            // xy.x = x;
            // xy.y = y;
            let x = lonX;
            let y = latY;
            // this.clickLocation(x, y, 'location', 15);

            // 添加点
            if (this.latLocationLayer) {
            } else {
                this.latLocationLayer = this.$refs.baseMap.addGraphicLayer(
                    "lat-location-layer",
                    null,
                    null,
                    999
                );
            }
            this.latLocationLayer.removeAll();
            let icon = {
                url: "../../../img/arcgisPlugin/concernManage/local.png",
                width: "24px",
                height: "24px",
            };
            let attributes = {
                item: "123",
                id: 123,
                type: "markLoc",
            };
            let markPointLayer = this.apiInstance.createPictureMarkSymbol(
                this.latLocationLayer,
                x,
                y,
                icon,
                attributes
            );
            mapHelper.setCenter(this.baseView, x, y, 18);
        },
        mapDropdownClick: function (command) {
            //显示排水户录入
            this.command = command;
            this.latLongLocationXY = false;
            if (this.latLocationLayer) {
                this.latLocationLayer.removeAll();
            }
            this.clearGraLayer();
            if (command == "search") {
                //搜索
                this.mapSearch = true;
                this.searchXY = false;
                this.searchAddXY = false;
                this.pickupXY = false;
                this.wgs84ToDg2000XY = false;
            } else if (command == "sewerage") {
                //排水户录入
                this.sewerageAdd();
            } else if (command == "location") {
                //坐标定位
                this.searchXy();
            } else if (command == "pickup") {
                //经纬度坐标拾取
                this.$message.info("点击地图上的某一处可复制坐标到粘贴板");
                this.pickupXy();
            } else if (command == "pickupToDg2000") {
                //经纬坐标转平面坐标
                this.wgs84ToDg2000();
            } else if (command == "latLongLocation") {
                //坐标拾取
                this.latLongLocationXy();
            } else if (command == "distance") {
                //测距
                this.clearGraLayer();
                this.distanceArr.splice(0);
                this.setStyle();
            } else if (command == "point") {
                //点选
                this.setStyle();
            } else if (command == "border") {
                //画框
                this.setStyle();
            } else if (command == "moreBorder") {
                //多边形
                this.setStyle();
            }
        },
        setStyle: function () {
            this.mapSearch = false;
            this.searchXY = false;
            this.searchAddXY = false;
            $(".tool-row").css("width", "85px");
            var longitude = "",
                latitude = "";
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function (position) {
                        longitude = position.coords.longitude;
                        latitude = position.coords.latitude;
                    },
                    function (e) {
                        var msg = e.code;
                        var dd = e.message;
                        console.log("msg:" + msg);
                        console.log("dd:" + dd);
                    }
                );
            }
            mapHelper.setCenter(this.baseView, longitude, latitude, 12);
        },
        clearGraLayer: function () {
            this.graLayer.removeAll();
        },
        closeSewerage: function () {
            // this.setCenterClick = false;
            this.sewerageEntry = false;
            this.graLayer.removeMany(this.locationGraphic);
        },
        closeSearchXY: function () {
            this.clearGraLayer();
            this.searchXY = false;
            if (this.xyStates == "add") {
                this.sewerageEntry = false;
            }
        },
        goLocation: function (params) {
            //定位查询
            var icon = params.icon;
            var name = !!params.name ? params.name : "";
            var x = params.x;
            var y = params.y;
            var myZoom = params.myZoom ? params.myZoom : 17;
            var state = params.state;
            if (!!x && !!y) {
                if (state != "distance") {
                    this.clearGraLayer();
                }
                var spatialReference = this.baseView.spatialReference;
                var subFacilities = [{x: x, y: y, name: name}];
                this.xyStates = state;
                var legend = {
                    icon: icon,
                    facilityTypeName: !!name ? name : "",
                    type: "gif",
                };
                var graphicArr = this.createPoint(legend, subFacilities);
                mapHelper.setCenter(
                    this.baseView,
                    subFacilities[0].x,
                    subFacilities[0].y,
                    myZoom
                );
                this.setCenterClick = true;
                return graphicArr;
            }
        },
        findWord: function (e) {
            if (!!$.trim(e.key)) {
                this.searchResource = mapOneWord.filter(
                    function (item, index) {
                        return item["value"].indexOf(this.searchWord) >= 0;
                    }.bind(this)
                );
            }
        },
        handleInput(data) {
            this.setInputeValue(data);
            this.isFocus = true;
        },
        //搜索道路
        setInputeValue: function (e) {
            this.isFocus = false;
            var self = this;
            if (!!$.trim(e)) {
                self.searchLoadingVisible = true;
                self.searchResourceVisible = true;
                self.searchDisabledVisible = true;
                this.searchResource = [];
                let zkSearch = new Promise((resolve, reject) => {
                    arcgisMapController.getFuzzyNames(
                        e,
                        function (data) {
                            this.xyStates = "";
                            resolve(data["records"]);
                            /*self.searchDisabledVisible = false;
                        self.searchLoadingVisible = false;*/
                        }.bind(this),
                        function (err) {
                            resolve();
                            // self.searchResource = [];
                            /*self.searchDisabledVisible = false;
                        this.searchLoadingVisible = false;*/
                        }.bind(this)
                    );
                });
                let gdSearch = new Promise((resolve, reject) => {
                    AMap.service(["AMap.PlaceSearch"], () => {
                        //构造地点查询类
                        var placeSearch = new AMap.PlaceSearch({
                            pageSize: 5, // 单页显示结果条数
                            pageIndex: 1, // 页码
                            city: "东莞市", // 兴趣点城市
                            citylimit: true, //是否强制限制在设置的城市内搜索
                        });
                        //关键字查询
                        placeSearch.search(e, (status, res) => {
                            if (status == "complete") {
                                res.poiList.pois.forEach((item, index) => {
                                    item.belong1 = item.type.split(";")[1];
                                    item.type = "点";
                                    item.value1 = item.name;
                                    var tempPoint = arcgisUtils.gcj02towgs84(
                                        parseFloat(item.location.lng),
                                        parseFloat(item.location.lat)
                                    );
                                    item.wkt = this.apiInstance.pointToWKT({
                                        x: tempPoint[0],
                                        y: tempPoint[1],
                                    });
                                    item.x = tempPoint[0];
                                    item.y = tempPoint[1];
                                });
                                resolve(res.poiList.pois);
                            } else {
                                resolve([]);
                            }
                        });
                    });
                });
                Promise.all([zkSearch, gdSearch]).then(
                    (res) => {
                        this.searchResource.push(...res[0]);
                        this.searchResource.push(...res[1]);
                        this.searchDisabledVisible = false;
                        this.searchLoadingVisible = false;
                        if (this.isFocus) {
                            this.$nextTick(() => {
                                this.$refs.searchInput.focus();
                            });
                        }
                    },
                    (rej) => {
                        this.searchDisabledVisible = false;
                        this.searchLoadingVisible = false;
                    }
                );
            } else {
                self.searchResource = [];
            }
            var st = setTimeout(
                function () {
                    if (this.searchResource.length == 0) {
                        this.searchLoadingVisible = false;
                        self.searchDisabledVisible = false;
                        if (this.isFocus) {
                            this.$nextTick(() => {
                                this.$refs.searchInput.focus();
                            });
                        }
                    }
                    clearTimeout(st);
                }.bind(this),
                3500
            );
        },
        mapSearchChange: function (e) {
            if ($.trim(e) == "") {
                var graphics = this.searchGraphic;
                if ($.type(graphics) == "array") {
                    this.graLayer.removeMany(graphics);
                } else {
                    this.graLayer.remove(graphics);
                }
            }
        },
        addMapLine: function (wkt, params) {
            var polyline = {};
            var style = {
                color: [255, 0, 0, 0.8], //红色
                // color: [64, 158, 255, 0.7], //蓝色
                width: 3,
            };
            var paramsArr = [];

            function setLine() {
                self.graLayer.removeMany(self.searchGraphic);
                self.searchGraphic = [];
                if ($.type(wkt) == "array") {
                    for (var i in wkt) {
                        paramsArr = [self.graLayer, wkt[i], style];
                        polyline = mapHelper.wkbToPolyline(...paramsArr);
                        self.searchGraphic.push(polyline);
                    }
                } else {
                    paramsArr = [self.graLayer, wkt, style];
                    polyline = mapHelper.wkbToPolyline(...paramsArr);
                    self.searchGraphic.push(polyline);
                }
            }

            var index = 2;
            var self = this;
            var b = true;
            var setIn = window.setInterval(function () {
                // self.clearGraLayer();
                if (b) {
                    setLine();
                    self.baseView.goTo(polyline.geometry.extent.expand(2));
                    b = false;
                }
                if (index % 2 == 0) {
                    style.width = 6;
                } else {
                    style.width = 2;
                }
                if (index == 5) {
                    if (params && params.state && params.state == "noLine") {
                        style.width = 0;
                    } else {
                        style.width = 3;
                    }
                    window.clearInterval(setIn);
                }
                setLine();
                index++;
            }, 1000);
        },
        addMapSurface: function (wkt, expandNum = 1, left = 0) {
            //搜索添加区域
            var layer = this.graLayer;
            var style = {
                color: [64, 158, 255, 0.35],
                outline: {
                    color: [64, 158, 255, 0.6],
                    width: 2,
                },
            };
            var polygon = mapHelper.wktToPolygon(wkt, layer, style);
            let expand = polygon.geometry.extent.expand(expandNum);
            this.baseView.goTo(expand);
            setTimeout(() => {
                mapHelper.setCenter(
                    this.baseView,
                    expand.center.x + left,
                    expand.center.y
                );
            }, 0);
            return polygon;
        },
        //点击搜索结果定位
        searchLocation: function (e) {
            var self = this;
            this.clearGraLayer();
            if (!!e.oid) {
                var wkt = e.wkt;
                self.searchWord = e.value1;
                if (e.type == "点") {
                    //标注点
                    var re = /\([^\)]+\)/g;
                    var s = wkt;
                    s = s.match(re)[0];
                    s = s.substring(1, s.length - 1);
                    var arr = s.split(" ").filter(function (item) {
                        return item != "";
                    });
                    var params = {
                        x: arr[0],
                        y: arr[1],
                        state: "add",
                        icon: "icon01",
                    };
                    this.searchGraphic = this.goLocation(params);
                } else if (e.type == "面") {
                    //面
                    this.searchGraphic = this.addMapSurface(wkt);
                } else if ((e.type = "线")) {
                    //线
                    this.addMapLine(wkt);
                }
                self.searchResourceVisible = false;
            } else {
                var params = {
                    x: e.x,
                    y: e.y,
                    state: "add",
                    icon: "icon01",
                    address: e.value1,
                };
                this.goLocation(params);
                self.searchResourceVisible = false;
            }
        },
        distanceLine: function (params) {
            //地图测距
            var distanceArr = this.distanceArr;
            var len = distanceArr.length;
            if (len > 0) {
                var last = distanceArr[len - 1].split(" ");
                var _x = params.x - last[0];
                var _y = params.y - last[1];
                var _distance = Math.sqrt(Math.pow(_x, 2) + Math.pow(_y, 2));
                params.name = _distance.toFixed(2) + "m";
            }
            this.goLocation(params);
            var str = params.x + " " + params.y;
            distanceArr.push(str);
            if (distanceArr.length >= 2) {
                var line = "LINESTRING(" + distanceArr.toString() + ")";
                var style = {
                    // color: [64, 158, 255, 0.5], //蓝色
                    color: [224, 52, 52, 0.7], //红色
                    width: 3,
                };
                var polyline = mapHelper.wkbToPolyline(this.graLayer, line, style);
            }
        },
        getBaseMapData: function (params) {
            this.isPointContent = false;
            this.isSearchLoading = true;
            this.isDetail = false;
            this.isAttributes = false;
            if (this.isPipeExecute) {
                params["connip"] = "chart";
            } else {
                params["connip"] = null;
            }
            arcgisMapController.getStatPipeLineByWkt(
                params,
                function (res) {
                    var data = res.data;
                    for (let i = 0; i < data.length; i++) {
                        if (!this.pipeStatistics[data[i].layerName])
                            this.pipeStatistics[data[i].layerName] = {};
                        let sort = !!data[i].sort ? data[i].sort : data[i].subtype;
                        this.pipeStatistics[data[i].layerName][sort] = {
                            label: formatLayer(data[i].layerName) + "_" + sort,
                            value: !!data[i].length
                                ? data[i].length.toFixed(2)
                                : data[i].count,
                        };
                    }
                    arcgisMapController.queryPipeLayerByWkt(
                        params,
                        function (res) {
                            var data = res.data;
                            this.mapSearchArr = data;
                            this.isSearchLoading = false;
                        }.bind(this),
                        function (err) {
                            console.log("queryPipeLayerByWkt--请求错误：" + err.msg);
                        }.bind(this)
                    );
                }.bind(this),
                function (err) {
                    console.log("queryPipeLayerByWkt--请求错误：" + err.msg);
                }.bind(this)
            );
        },
        clickLayers: function (item) {
            if (!!item) {
                this.selectLayer = item.text;
                this.pointParams.layerId = item.value;
                this.isAliasLi = false;
                this.showPointContent();
            } else {
                console.log("command错误-----");
            }
        },
        setPointParams: function (evt) {
            var x = evt.coordinates[0];
            var y = evt.coordinates[1];
            var pipeLayers = this.pipeLayers;
            if (pipeLayers.length > 0) {
                //每次点击初始化图层下拉单的数据
                this.selectLayer = pipeLayers[0].text;
            } else {
                this.selectLayer = "全部图层";
            }
            this.pointParams = {
                x: x,
                y: y,
                layerId: "",
            };
            this.showPointContent();
        },
        toDetail: function (item) {
            //查询数据列表
            this.listCurrentPage = 1;
            this.toDetailParams = {
                wkt: this.mapSearchWkt,
                layerId: item.layerId,
                pageNumber: this.listCurrentPage,
                pageSize: this.InspectionPageSize,
            };
            this.queryPipeLayer();
        },
        queryPipeLayer: function () {
            this.isDetail = true;
            this.isDetailLoading = true;
            arcgisMapController.queryPipeLayerFeatureByWkt(
                this.toDetailParams,
                function (res) {
                    var data = res.data;
                    this.isDetailLoading = false;
                    if (res.success == true && data.records) {
                        this.listCurrentPage = data.pageNumber;
                        this.listPageCount = data.totalPage;
                        data.records.forEach(function (feature) {
                            if (!feature.primaryField) {
                                feature.primaryField = feature.fieldDetails[0].value;
                            }
                        });
                        this.detailDataArr = data.records;
                    }
                }.bind(this),
                function (error) {
                    console.log("queryPipeLayerFeatureByWkt错误：" + error);
                }
            );
        },
        mapListCurrentChange: function (val) {
            this.toDetailParams["pageNumber"] = val;
            this.queryPipeLayer();
        },
        initEditPipeLayer: function () {
            let url = "";
            if (["dev", "test"].indexOf(RUNNING_ENV) > -1) {
                url = serviceHelper.getArcgisServer() + "/arcgis251/arcgis/rest/services/DGPS/chart_2_web_test/MapServer";
            } else {
                url = serviceHelper.getArcgisServer() + "/arcgis251/arcgis/rest/services/DGPS/chart_2_web/MapServer";
            }
            let layer = new this.apiInstance.MapImageLayer({
                id: "editPipeLayer",
                url: url,
                /*sublayers: [
                 {
                 id: 2,
                 visible: false
                 }, {
                 id: 5,
                 visible: false
                 }, {
                 id: 20,
                 visible: false
                 }, {
                 id: 37,
                 visible: false
                 }
                 ],*/
                spatialReference: this.baseView.spatialReference,
            });
            return layer;
        },
        moveMap(evt) {
            //监听地图拖动事件等，实现分屏操作
            if (!!this.$refs.baseMap && this.moveType === 1) {
                this.$refs.baseMap.view.extent = evt.extent;
            }
            if (!!this.$refs.dxtMap && this.moveType === 2) {
                this.$refs.dxtMap.view.extent = evt.extent;
            }
        },
        //初始化地图
        initBaseMap: function () {
            var self = this;
            let arcgisLoginConf = {
                serverString: serviceHelper.getArcgisServer() + "/arcgis251/arcgis/rest/services",
            };
            this.$refs.baseMap.init(
                "mapDiv",
                {
                    fromServer: true,
                    serverURL: serviceHelper.getEwaterUrl(),
                    iotURL: serviceHelper.getNormalPath(),
                    token: serviceHelper.getToken(),
                    mapType: 1,
                    layerControl: 1,
                    arcgisLoginConf: arcgisLoginConf,
                },
                function (api, currentMap, baseView, initedLayers) {
                    this.apiInstance = api;
                    self.baseMap = currentMap;
                    self.baseView = baseView;
                    this.initArgs = arguments;
                    //将api传给排水户组件
                    this.$refs.drainageMapList.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.drainageConstructList.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.userMonitor.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.carMonitor.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.carPatrolTrack.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.carPatrolTrackEmer.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.patrolTrackPlugin.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.problemReportMapList.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.problemReportMapListShow.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.planClearHydMapList.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.editTraceTool.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.traceMainPipe.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.outletMapList.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.addDeployPoint.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.startProtection.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.riverMapList.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.riverMapList2.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.riverOutletMapList.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.riverOutletMapListPic.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.startProtectionPic.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.editPipeInfo.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    //网格管理
                    this.$refs.gridDefined.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    //计划管理
                    this.$refs.newPlan.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    //计划详情台账
                    this.$refs.planAccount.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    // 检测一张图
                    this.$refs.riskOneMap.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    // 清疏一张图
                    this.$refs.clearOneMap.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    initJOmap.init(api, currentMap, baseView, initedLayers);
                    this.$refs.yxtPipeAdd.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.yxtPipeConnect.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.yxtPipeConnectSewage.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.yxtPipeDelete.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.yxtPipeRecycleBin.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.yxtPipeEdit.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.yxtPipeConnectSlice.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.mapDebug.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers,
                        this.$refs.baseMap
                    );
                    this.$refs.playHandlerPlugin.initPlugin(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    // 泵站管理
                    this.$refs.mapAddEdit.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.pumpInspectionPlanDrawLine.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.appRiskPointManager.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.generalLayer.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers,
                        this.$refs.baseMap
                    );

                    // 排水户新的
                    this.$refs.drainageMapListNew.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );
                    this.$refs.drainageConstructListNew.getMapApi(
                        api,
                        currentMap,
                        baseView,
                        initedLayers
                    );

                    let pipelineLayer = this.$refs.baseMap.getLayerById(
                        "main-pipeline-layer"
                    );
                    this.pipe_sewage = this.$refs.baseMap.getLayerById("pipe_sewage");
                    var baseMaps = initedLayers.baseMaps;
                    var currentDisplayBaseMap = "";
                    //  baseView.constraints.rotationEnabled = false;//禁止地图旋转
                    this.$refs.baseMap.$on("base-map-change", (layerID) => {
                        currentDisplayBaseMap = layerID;
                        let zoom = Math.round(baseView.zoom);
                        for (var key in baseMaps) {
                            if (key == currentDisplayBaseMap) {
                                if (baseMaps[key][0].id === "internet-tdt-yxt") {
                                    baseMaps[key].forEach((baseMapConfig) => {
                                        baseMapConfig.visible = false;
                                    });
                                    this.WFSLayer.visible = false;
                                    this.WFSZjLayer.visible = false;
                                    this.WFSLayerYxt.visible = true;
                                    this.WFSZjLayerYxt.visible = true;
                                } else {
                                    if (zoom > 16) {
                                        baseMaps[key].forEach((baseMapConfig) => {
                                            baseMapConfig.visible = false;
                                        });
                                        this.WFSLayer.visible = true;
                                        this.WFSZjLayer.visible = true;
                                    } else {
                                        baseMaps[key].forEach((baseMapConfig) => {
                                            baseMapConfig.visible = true;
                                        });
                                        this.WFSLayer.visible = false;
                                        this.WFSZjLayer.visible = false;
                                    }
                                    this.WFSLayerYxt.visible = false;
                                    this.WFSZjLayerYxt.visible = false;
                                }
                            }
                        }
                        this.$refs.mapDebug.layerIDChange(layerID);
                    });
                    this.WFSLayer = initJOmap.createWebTileLayer(
                        currentMap,
                        {
                            tileExtent:
                                "111.4398193359,21.6458129883,116.4111328125,24.0820312500",
                            tileFormat: "png",
                            tileLevel: "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20",
                            tileResolution:
                                "0.703125000000,0.351562500000,0.175781250000,0.087890625000,0.043945312500,0.021972656250,0.010986328125,0.005493164062,0.002746582031,0.001373291016,0.000686645508,0.000343322754,0.000171661377,0.000085830688,0.000042915344,0.000021457672,0.000010728836,0.000005364418,0.000002682209,0.0000013411045",
                            tileScale:
                                "295828763.797038,147914381.898519,73957190.949259,36978595.474630,18489297.737315,9244648.868657,4622324.434329,2311162.217164,1155581.108582,577790.554291,288895.277146,144447.638573,72223.819286,36111.909643,18055.954822,9027.977411,4513.988705,2256.994353,1128.4971765,564.24858825",
                            tileSizeCols: 256,
                            tileSizeRows: 256,
                            tileZeroX: -180,
                            tileZeroY: 90,
                            transparency: null,
                            type: "TomcatTile",
                            url: serviceHelper.getStaticUrl() + "/自然资源瓦片底图/国土地图/矢量底图/",
                            wkid: "4490",
                            compressionQuality: "0",
                        },
                        "JOBasemap"
                    )[0];
                    this.WFSZjLayer = initJOmap.createWebTileLayer(
                        currentMap,
                        {
                            tileExtent:
                                "111.4398193359,21.6458129883,116.4111328125,24.0820312500",
                            tileFormat: "png",
                            tileLevel: "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20",
                            tileResolution:
                                "0.703125000000,0.351562500000,0.175781250000,0.087890625000,0.043945312500,0.021972656250,0.010986328125,0.005493164062,0.002746582031,0.001373291016,0.000686645508,0.000343322754,0.000171661377,0.000085830688,0.000042915344,0.000021457672,0.000010728836,0.000005364418,0.000002682209,0.0000013411045",
                            tileScale:
                                "295828763.797038,147914381.898519,73957190.949259,36978595.474630,18489297.737315,9244648.868657,4622324.434329,2311162.217164,1155581.108582,577790.554291,288895.277146,144447.638573,72223.819286,36111.909643,18055.954822,9027.977411,4513.988705,2256.994353,1128.4971765,564.24858825",
                            tileSizeCols: 256,
                            tileSizeRows: 256,
                            tileZeroX: -180,
                            tileZeroY: 90,
                            transparency: null,
                            type: "TomcatTile",
                            url: serviceHelper.getStaticUrl()+ "/自然资源瓦片底图/国土地图/矢量注记/",
                            wkid: "4490",
                            compressionQuality: "0",
                        },
                        "JOBzmap"
                    )[0];
                    this.WFSLayer.visible = false;
                    this.WFSZjLayer.visible = false;
                    currentMap.add(this.WFSLayer, 0);
                    currentMap.add(this.WFSZjLayer, 1);
                    //加载吉奥影像底图切片
                    this.WFSLayerYxt = initJOmap.createWebTileLayer(
                        currentMap,
                        {
                            tileExtent:
                                "111.4398193359,21.6458129883,116.4111328125,24.0820312500",
                            tileFormat: "jpg",
                            tileLevel: "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20",
                            tileResolution:
                                "0.703125000000,0.351562500000,0.175781250000,0.087890625000,0.043945312500,0.021972656250,0.010986328125,0.005493164062,0.002746582031,0.001373291016,0.000686645508,0.000343322754,0.000171661377,0.000085830688,0.000042915344,0.000021457672,0.000010728836,0.000005364418,0.000002682209,0.0000013411045",
                            tileScale:
                                "295828763.797038,147914381.898519,73957190.949259,36978595.474630,18489297.737315,9244648.868657,4622324.434329,2311162.217164,1155581.108582,577790.554291,288895.277146,144447.638573,72223.819286,36111.909643,18055.954822,9027.977411,4513.988705,2256.994353,1128.4971765,564.24858825",
                            tileSizeCols: 256,
                            tileSizeRows: 256,
                            tileZeroX: -180,
                            tileZeroY: 90,
                            transparency: null,
                            type: "TomcatTile",
                            url: serviceHelper.getStaticUrl()+ "/自然资源瓦片底图/国土地图/影像底图/",
                            wkid: "4490",
                            compressionQuality: "0",
                        },
                        "JOWxtmap"
                    )[0];
                    this.WFSZjLayerYxt = initJOmap.createWebTileLayer(
                        currentMap,
                        {
                            tileExtent:
                                "111.4398193359,21.6458129883,116.4111328125,24.0820312500",
                            tileFormat: "png",
                            tileLevel: "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20",
                            tileResolution:
                                "0.703125000000,0.351562500000,0.175781250000,0.087890625000,0.043945312500,0.021972656250,0.010986328125,0.005493164062,0.002746582031,0.001373291016,0.000686645508,0.000343322754,0.000171661377,0.000085830688,0.000042915344,0.000021457672,0.000010728836,0.000005364418,0.000002682209,0.0000013411045",
                            tileScale:
                                "295828763.797038,147914381.898519,73957190.949259,36978595.474630,18489297.737315,9244648.868657,4622324.434329,2311162.217164,1155581.108582,577790.554291,288895.277146,144447.638573,72223.819286,36111.909643,18055.954822,9027.977411,4513.988705,2256.994353,1128.4971765,564.24858825",
                            tileSizeCols: 256,
                            tileSizeRows: 256,
                            tileZeroX: -180,
                            tileZeroY: 90,
                            transparency: null,
                            type: "TomcatTile",
                            url: serviceHelper.getStaticUrl() + "/自然资源瓦片底图/国土地图/影像注记/",
                            wkid: "4490",
                            compressionQuality: "0",
                        },
                        "JOWxtBzmap"
                    )[0];
                    this.WFSLayerYxt.visible = false;
                    this.WFSZjLayerYxt.visible = false;
                    currentMap.add(this.WFSLayerYxt, 0);
                    currentMap.add(this.WFSZjLayerYxt, 1);

                    //按数据角色控制地图遮罩
                    var zone = "";
                    let allMapPower = false;
                    userModel.getDataRoles().forEach((item, index) => {
                        if (index === 0) {
                            zone += "zone <> '" + item.name + "'";
                        } else {
                            zone += " and zone <> '" + item.name + "'";
                        }
                        if (item.name === "全市地图权限") {
                            allMapPower = true;
                        }
                    });
                    var translucentLayer = this.$refs.baseMap.getLayerById("translucent");
                    baseView.map.reorder(translucentLayer, 100);
                    if (!!translucentLayer) {
                        baseView.whenLayerView(translucentLayer).then(() => {
                            translucentLayer.allSublayers.items.forEach(function (subLayer) {
                                if (subLayer.setDefinitionExpression)
                                    subLayer.setDefinitionExpression(zone);
                                else subLayer.definitionExpression = zone;
                            });
                        });
                    }
                    if (allMapPower) {
                        translucentLayer.visible = false;
                    }
                    //设置切片图层格式给png8
                    var reginCutLayer;
                    if (initedLayers.customerLayers) {
                        reginCutLayer = initedLayers.customerLayers["regionCut"];
                    }
                    if (!!reginCutLayer) {
                        reginCutLayer.layer.imageFormat = "png8";
                    }
                    var timmer;
                    var currentZoom = baseView.zoom;
                    var compassWidget = new api.Compass({view: baseView});
                    baseView.ui.view.ui.add(compassWidget, "top-left");
                    self.mapLoading = false;
                    var layerTree =
                        this.$refs.baseMap.getSubComponent("layerControl").$refs.layerTree;
                    layerTree.$on(
                        "treeChange",
                        function (res) {
                            if (res.name === "排水管网_污" && !this.pipe_sewage) {
                                this.pipe_sewage =
                                    this.$refs.baseMap.getLayerById("pipe_sewage");
                            }
                        }.bind(this)
                    );

                    //手动控制关闭
                    this.checkLayer(false, "区划范围-镇街");
                    this.checkLayer(false, "区划范围-分公司");

                    baseView.watch(
                        "extent",
                        function (evt) {
                            if (!!this.dxtMap) {
                                this.moveMap(evt);
                            }
                            // this.switchZoomToDisplayLayer(baseView.zoom);
                        }.bind(this)
                    );
                    baseView.on(
                        "mouse-wheel",
                        function (evt) {
                            this.moveType = 2;
                        }.bind(this)
                    );
                    baseView.watch("focused", (isFocused) => {
                        if (isFocused) this.moveType = 2;
                    });
                    baseView.watch(
                        "zoom",
                        function () {
                            if (timmer) {
                                clearTimeout(timmer);
                            }
                            let layer = null;
                            var zoom = Math.round(baseView.zoom);
                            for (var key in baseMaps) {
                                if (key == currentDisplayBaseMap) {
                                    layer = baseMaps[key];
                                    baseMaps[key].forEach((baseMapConfig) => {
                                        baseMapConfig.visible = true;
                                    });
                                }
                            }
                            if (layer && layer[0].id === "internet-tdt") {
                                if (zoom > 16) {
                                    layer.forEach((baseMapConfig) => {
                                        baseMapConfig.visible = false;
                                    });
                                    this.WFSLayer.visible = true;
                                    this.WFSZjLayer.visible = true;
                                    this.WFSLayerYxt.visible = false;
                                    this.WFSZjLayerYxt.visible = false;
                                }else{
                                    layer.forEach((baseMapConfig) => {
                                        baseMapConfig.visible = true;
                                    });
                                    this.WFSLayer.visible = false;
                                    this.WFSZjLayer.visible = false;
                                    this.WFSLayerYxt.visible = false;
                                    this.WFSZjLayerYxt.visible = false;
                                }
                            } else {
                                layer.forEach((baseMapConfig) => {
                                    baseMapConfig.visible = false;
                                });
                                this.WFSLayerYxt.visible = true;
                                this.WFSZjLayerYxt.visible = true;
                            }
                            if (zoom > 15) {
                                if (pipelineLayer) {
                                    pipelineLayer.visible = false;
                                }
                                // if (this.pipe_sewage) {
                                //     this.pipe_sewage.visible = false;
                                // }
                            } else {
                                if (pipelineLayer) {
                                    pipelineLayer.visible = true;
                                }
                                // if (this.pipe_sewage) {
                                //     this.pipe_sewage.visible = true;
                                // }
                            }

                            if (
                                cacheFacilities.length == 0 &&
                                !!this.$refs.baseMap.getFacilityLayer()
                            ) {
                                var facilityLayer = this.$refs.baseMap.getFacilityLayer();
                                var graphics = facilityLayer.graphics;
                                if (!!graphics && graphics.items.length > 0) {
                                    cacheFacilities = graphics.items.slice(0);
                                }
                                console.log(cacheFacilities);
                            }

                            console.log(currentZoom, zoom);
                            currentZoom = zoom;
                        }.bind(this)
                    );
                    arcgisMapController.setUpArcgis(
                        "baseMap",
                        api,
                        currentMap,
                        baseView,
                        self.$refs.baseMap
                    );
                    var currentModule = appMenuModel.getCurrentModel();
                    mapHelper.getMapToolsAndRes(
                        currentMap,
                        baseView,
                        function (boolean, evt) {
                            //点选pointClick
                            if (boolean === true) {
                                // self.pointClickState = "pop-point-base";
                                self.setPointParams(evt);
                            } else {
                                self.pointClickState = "";
                            }
                        },
                        function (evt) {
                            //cbData
                            self.completeEvt = evt;
                        }
                    );
                    //    self.$refs.mapInfoWindow.loadData(api, baseView);
                    self.toolsVisible = true;
                    // baseView.zoom = 3;
                    self.apiInstance = api;
                    self.graLayer = self.$refs.baseMap.addGraphicLayer(
                        "graphicLayer",
                        null,
                        null,
                        999
                    );
                    self.editLogLayer = self.$refs.baseMap.addGraphicLayer(
                        "editLogLayer",
                        null,
                        null,
                        999
                    );
                    self.newQuestionLayer =
                        self.$refs.baseMap.addGraphicLayer("new-question-layer");
                    self.currentQuestionLayer = self.$refs.baseMap.addGraphicLayer(
                        "current-question-layer"
                    );
                    self.currentQuestionLayer.visible = false;
                    if (
                        !!initedLayers.facilityLayer &&
                        initedLayers.facilityLayer.graphics
                    ) {
                        var showDeviceList = initedLayers.facilityLayer.graphics.length > 0; //初始化设备列表
                        if (showDeviceList) {
                            eventHelper.emit("open-facility-list", true);
                        }
                    }
                    mapHelper.stopEdit(self.graLayer, baseView); //停止编辑

                    // self.$refs.infoWindow.registerToView(baseView);
                    self.graLayer.on(
                        "layerview-create",
                        function (evt) {
                            var graView = evt.view;
                            var getMap = {};
                            getMap.map = currentMap;
                            getMap.view = baseView;
                            this.$refs.serviceAttribute.initArcgis(getMap);
                            eventHelper.emit("get-map", getMap);
                            self.$refs.retrospectDetail.initRetrospectDetail(api, getMap);
                            self.$refs.editTraceTool.initRetrospectDetail(getMap);
                            self.$refs.traceMainPipe.initRetrospectDetail(getMap);
                            graView.on("click", function (event) {
                                if (!!self.isQueryMark) {
                                    //查询运行图标记
                                    self.queryMarkByPoint(event);
                                    return;
                                }
                                graView.hitTest(event).then(
                                    function (response) {
                                        // 如果当前模块为 排水单元-关注点管理 则不操作
                                        if (self.pointClickState == "concernManage") return;
                                        if (response.results.length == 0) return;
                                        var graphic = response.results[0].graphic;
                                        var attributes = graphic.attributes;
                                        if (!attributes) return;
                                        if (attributes.facilityType == "IP") {
                                            eventHelper.emit("open-facilityInfo-dialog", attributes);
                                            return;
                                        } else if (attributes.facilityType == "query-line") {
                                            self.getWKT("line");
                                            return;
                                        } else if (attributes.facilityType == "query-polygon") {
                                            self.getWKT("polygon");
                                            return;
                                        } else if (attributes.facilityType == "distanceClose") {
                                            var distanceGraphics =
                                                self.$refs.pipeCheck.distanceGraphics;
                                            self.graLayer.removeMany(distanceGraphics);
                                            return;
                                        } else if (attributes.facilityType == "bzClose") {
                                            self.$refs.pipeCheck.bzPointClose(graphic);
                                            return;
                                        } else if (attributes.facilityType == "drainageClose") {
                                            self.openTestTABLE(graphic.attributes.item);
                                            return;
                                        } else if (attributes.facilityType == "analysis") {
                                            //雨污混接弹窗
                                            eventHelper.emit("add-pollutionAnalysisDialog", graphic);
                                            return;
                                        } else if (attributes.facilityType == "traceability") {
                                            self.$refs.traceabilityDetail.openTraceabilityPopu(
                                                graphic
                                            );
                                            return;
                                        } else if (
                                            !!attributes.facilityTypeName ||
                                            (!!attributes &&
                                                !!attributes.item &&
                                                !!attributes.item.fid)
                                        ) {
                                            //点中了测站
                                            self.showFacilityList = true;
                                            eventHelper.emit("openDevicePanel", attributes.item);
                                        }
                                    }.bind(this)
                                );
                            });
                            self.mapLoading = false;
                        }.bind(this)
                    );
                    /*mapHelper.createMapImageLayer(currentMap, serviceHelper.getPipeUrl() + url, 'pipeLineLayer');*/
                    //单击
                    baseView.on(
                        "click",
                        function (evt) {
                            // mapHelper.setCenter(baseView, evt.mapPoint.x, evt.mapPoint.y);
                            this.searchX = evt.mapPoint.x;
                            this.searchY = evt.mapPoint.y;
                            // 若此时为左边拾取，并且用户点击了某个坐标，则为true
                            if (this.pickupXY === true) {
                                this.isMapPointXY = true;
                            } else if (this.wgs84ToDg2000XY) {
                                this.getDg2000XY();
                                var imgObj = {
                                    width: "16px",
                                    height: "20px",
                                    url: "./css/images/choucha.png",
                                    xoffset: 0,
                                    yoffset: "10px",
                                };
                                this.clearGraLayer();
                                mapHelper.createPictureMarkSymbol(
                                    this.graLayer,
                                    evt.mapPoint.x,evt.mapPoint.y,
                                    imgObj,
                                    {}
                                );
                            }
                            if (this.command == "pickup") {
                                this.$message.success("坐标已成功复制到剪贴板");
                                commonUtil.copyText(evt.mapPoint.x + "," + evt.mapPoint.y);
                            }

                            var state = self.pointClickState;
                            if (window.drawStatus != "regionComplete") {
                                state = "";
                            }
                            switch (state) {
                                case "po-point-bzInfo":
                                    self.$refs.pipeCheck.addPointBZinfo(evt);
                                    break;
                                case "startPipeline": //管网抽查点击
                                    self.$refs.pipeCheck.startPipeData(evt);
                                    break;
                                case "editRegion": //切片
                                    var params = {
                                        x: evt.mapPoint.x,
                                        y: evt.mapPoint.y,
                                    };
                                    regionCutController.getPolygonByPoint(
                                        params,
                                        function (res) {
                                            if (res.success && JSON.stringify(res.data) != "{}") {
                                                eventHelper.emit("showRegionCut", [
                                                    self.graLayer,
                                                    res.data,
                                                    self.baseView,
                                                    evt,
                                                ]);
                                                window.drawStatus = "";
                                            } else {
                                                console.log("点击区域没有创建片区");
                                            }
                                        },
                                        function (error) {
                                            console.log(error);
                                        }
                                    );
                                    break;
                                case "editAnalysisBlock": // 分析区块
                                    analysisBlockService.getBlockByPoint(
                                        {
                                            x: evt.mapPoint.x,
                                            y: evt.mapPoint.y,
                                        },
                                        (res) => {
                                            if (JSON.stringify(res) != "{}") {
                                                eventHelper.emit("showAnalysisBlockCut", [
                                                    self.graLayer,
                                                    res,
                                                    self.baseView,
                                                    evt,
                                                ]);
                                                window.drawStatus = "";
                                            } else {
                                                console.log("点击区域没有创建区块");
                                            }
                                        },
                                        (err) => {
                                            console.error(err);
                                        }
                                    );
                                    break;
                                /* case 'addRegion':
                             mapHelper.mapRegionTool(self.graLayer, self.baseView);*/
                                case "concernManage": // 关注点管理
                                    this.$refs.concernManage.fetchMapViewClick(evt);
                                    break;
                                // case "problemMap":
                                //     this.$refs.problemReportMapListShow.fetchMapViewClick(evt)
                                //     break;
                                case "riverOutletMap":
                                    this.$refs.riverOutletMapListPic.fetchMapViewClick(evt);
                                    break;
                                case "protectionPic":
                                    this.$refs.startProtectionPic.fetchMapViewClick(evt);
                                    break;
                                case "unitAnalyze": // 排水单元去向分析
                                    this.$refs.unitAnalyze.fetchMapViewClick(evt);
                                    break;
                                case "unitStatistics": // 排水单元情况统计
                                    this.$refs.unitStatistics.fetchMapViewClick(evt);
                                    break;
                                case "thematicMap": // 专题图
                                    this.$refs.thematicMap.fetchMapViewClick(evt);
                                    break;
                                case "addAnalysis": // 新增水质分析点
                                    this.$refs.addAnalysis.fetchMapViewClick(evt);
                                    break;
                                default:
                                    baseView.hitTest(evt).then(
                                        function (response) {
                                            if (response.results.length > 0) {
                                                if (!!response.results[0].graphic.attributes) {
                                                    //选中问题区域
                                                    var question = response.results[0].graphic.attributes;
                                                    if (question.type == "questionArea") {
                                                        //数据问题上报
                                                        self.questionDialogVisible = true;
                                                        for (var key in question) {
                                                            self.questionForm[key] = question[key];
                                                        }
                                                        self.questionForm.editable = false;
                                                    }
                                                }
                                            } else {
                                                //什么都没选中
                                            }
                                        }.bind(this)
                                    );
                            }
                            if (self.xyStates == "add") {
                                self.sewerageAddInfo(evt);
                            }
                        }.bind(this)
                    );
                    //baseView.toMap
                    baseView.on(
                        "pointer-move",
                        function (evt) {
                            // 判断用户清空了其中一个坐标的值则重新通过移动鼠标获取坐标
                            if (this.mapPointX === "" || this.mapPointY === "") {
                                this.isMapPointXY = false;
                            }
                            // 若当前为坐标拾取，并且用户没有选中某个坐标的时候，坐标随鼠标移动
                            if (this.pickupXY === true && !this.isMapPointXY) {
                                var mapPoint = baseView.toMap(evt);
                                this.mapPointX = Math.round(mapPoint.x * 1000) / 1000;
                                this.mapPointY = Math.round(mapPoint.y * 1000) / 1000;
                            }

                            baseView.hitTest(evt).then(
                                function (response) {
                                    if (response.results.length > 0) {
                                        var station = response.results[0].graphic.attributes;
                                        if (!!station && !!station.facilityTypeName) {
                                            if (!station.item.onlineState) {
                                                this.currentStatus = "offlineStatus-bg";
                                            } else if (station.facilityTypeName == "RF") {
                                                if (station.item.state == 1) {
                                                    this.currentStatus = "yellow-bg";
                                                } else if (station.item.state == 2) {
                                                    this.currentStatus = "warningStatus-bg";
                                                } else if (station.item.state == 3) {
                                                    this.currentStatus = "dangerStatus-bg";
                                                } else {
                                                    this.currentStatus = "";
                                                }
                                            } else {
                                                if (station.item.state == 1) {
                                                    this.currentStatus = "warningStatus-bg";
                                                } else if (station.item.state == 2) {
                                                    this.currentStatus = "dangerStatus-bg";
                                                } else {
                                                    this.currentStatus = "";
                                                }
                                            }
                                            if (
                                                station.facilityTypeName == "WD" &&
                                                station.id.indexOf("K") == 0
                                            ) {
                                                station.unknowPoint = true; //待接入点
                                                this.currentStatus = "successStatus-bg";
                                            }
                                            if (station.item.checkPoint == "1") {
                                                station.item.cod =
                                                    this.facilityListObj[station.item.imei].cod.dValue;
                                                station.item.ammonia =
                                                    this.facilityListObj[
                                                        station.item.imei
                                                        ].ammonia.dValue;
                                                station.item.codUpdateTime =
                                                    this.facilityListObj[
                                                        station.item.imei
                                                        ].cod.deviceUpdateTime.split(" ")[0];
                                                if (station.item.imei == "8699690321610254") {
                                                    station.item.cod2 =
                                                        this.facilityListObj[station.item.imei].cod2.dValue;
                                                    station.item.ammonia2 =
                                                        this.facilityListObj[
                                                            station.item.imei
                                                            ].ammonia2.dValue;
                                                    station.item.cod2UpdateTime =
                                                        this.facilityListObj[
                                                            station.item.imei
                                                            ].cod2.deviceUpdateTime.split(" ")[0];
                                                }
                                            }
                                            this.highLightFacility = station;
                                            var screenPoint = response.screenPoint;
                                            // $('.facilityContainer').show().css({
                                            //     'left': screenPoint.x + 10,
                                            //     'top': screenPoint.y - 10
                                            // })
                                        }
                                    } else {
                                        $(".facilityContainer").hide();
                                    }
                                }.bind(this)
                            );
                        }.bind(this)
                    );
                    // this.customLayer();
                    // 添加地图插件监听，方法在layerControlHelper
                    this.layerControlInit();
                    // 默认显示污水管
                    // this.defaultPipe = 'pipe-line-sort-dirty';

                    // this.showStanderSortLayer('标准库-雨水管', false);
                    // this.showStanderSortLayer('标准库-污水管', true);
                    // this.showStanderSortLayer('标准库-合流管', false);
                    this.layerTreeManage();

                    // this.$refs.baseMap.$on('map:show-layer', function (layerId) {
                    //     if (layerId == 'pipe-line-sort-mix' || layerId == 'pipe-line-sort-dirty' || layerId == 'pipe-line-sort-rain') {
                    //         //假如是全选状态下，隐藏其他，只用雨水管图层全部显示
                    //         if (!!this.$refs.baseMap.$refs.layerControl.getLayerVisible('标准库-雨水管') && !!this.$refs.baseMap.$refs.layerControl.getLayerVisible('标准库-合流管') && !!this.$refs.baseMap.$refs.layerControl.getLayerVisible('标准库-污水管')) {
                    //             this.showAllStanderSortLayer(true);
                    //             this.$refs.baseMap.hideLayerById('pipe-line-sort-mix');
                    //             this.$refs.baseMap.hideLayerById('pipe-line-sort-dirty');
                    //         }
                    //     }
                    // }.bind(this));
                    // this.$refs.baseMap.$on('map:hide-layer', function (layerId) {
                    //     if (layerId == 'pipe-line-sort-mix' || layerId == 'pipe-line-sort-dirty' || layerId == 'pipe-line-sort-rain') {
                    //         if (layerId == 'pipe-line-sort-mix' || layerId == 'pipe-line-sort-dirty') {
                    //             if (!!this.$refs.baseMap.$refs.layerControl.getLayerVisible('标准库-雨水管')) {
                    //                 this.showRainStanderSortyLayer(true)
                    //             }
                    //         }
                    //         if (!!this.$refs.baseMap.$refs.layerControl.getLayerVisible('标准库-污水管')) {
                    //             this.$refs.baseMap.showLayerById('pipe-line-sort-dirty');
                    //         }
                    //         if (!!this.$refs.baseMap.$refs.layerControl.getLayerVisible('标准库-合流管')) {
                    //             this.$refs.baseMap.showLayerById('pipe-line-sort-mix')
                    //         }
                    //     }
                    // }.bind(this));
                    this.$refs.baseMap.$on(
                        "openMapLegend",
                        function (legend) {
                        }.bind(this)
                    );

                    var layer = this.$refs.baseMap.getLayerById("road_17");
                    baseView.map.reorder(layer, 2);
                }.bind(this)
            );
        },
        //标准库
        initDxtMap(cb) {
            //初始化地图
            eventHelper.emit("loading-start");
            let arcgisLoginConf = {
                serverString: serviceHelper.getArcgisServer() + "/arcgis251/arcgis/rest/services",
            };
            this.$refs.dxtMap.init(
                "mapDiv_dxt",
                {
                    fromServer: true,
                    arcgisLoginConf: arcgisLoginConf,
                    serverURL: serviceHelper.getEwaterUrl(),
                    iotURL: serviceHelper.getNormalPath(),
                    token: serviceHelper.getToken(),
                    mapType: 1,
                    layerControl: 0,
                },
                function (api, currentMap, baseView, initedLayers) {
                    this.instance_dxt = api;
                    this.dxtMap = currentMap;
                    this.baseView_dxt = baseView;
                    this.mapLoading = false;
                    let pipelineLayer = this.$refs.dxtMap.getLayerById(
                        "main-pipeline-layer"
                    );
                    this.pipe_sewage = this.$refs.dxtMap.getLayerById("pipe_sewage");

                    this.WFSLayer_bzk = initJOmap.createWebTileLayer(
                        currentMap,
                        {
                            tileExtent:
                                "111.4398193359,21.6458129883,116.4111328125,24.0820312500",
                            tileFormat: "png",
                            tileLevel: "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20",
                            tileResolution:
                                "0.703125000000,0.351562500000,0.175781250000,0.087890625000,0.043945312500,0.021972656250,0.010986328125,0.005493164062,0.002746582031,0.001373291016,0.000686645508,0.000343322754,0.000171661377,0.000085830688,0.000042915344,0.000021457672,0.000010728836,0.000005364418,0.000002682209,0.0000013411045",
                            tileScale:
                                "295828763.797038,147914381.898519,73957190.949259,36978595.474630,18489297.737315,9244648.868657,4622324.434329,2311162.217164,1155581.108582,577790.554291,288895.277146,144447.638573,72223.819286,36111.909643,18055.954822,9027.977411,4513.988705,2256.994353,1128.4971765,564.24858825",
                            tileSizeCols: 256,
                            tileSizeRows: 256,
                            tileZeroX: -180,
                            tileZeroY: 90,
                            transparency: null,
                            type: "TomcatTile",
                            url: serviceHelper.getStaticUrl() + "/自然资源瓦片底图/国土地图/矢量底图/",
                            wkid: "4490",
                            compressionQuality: "0",
                        },
                        "JOBasemap"
                    )[0];
                    this.WFSZjLayer_bzk = initJOmap.createWebTileLayer(
                        currentMap,
                        {
                            tileExtent:
                                "111.4398193359,21.6458129883,116.4111328125,24.0820312500",
                            tileFormat: "png",
                            tileLevel: "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20",
                            tileResolution:
                                "0.703125000000,0.351562500000,0.175781250000,0.087890625000,0.043945312500,0.021972656250,0.010986328125,0.005493164062,0.002746582031,0.001373291016,0.000686645508,0.000343322754,0.000171661377,0.000085830688,0.000042915344,0.000021457672,0.000010728836,0.000005364418,0.000002682209,0.0000013411045",
                            tileScale:
                                "295828763.797038,147914381.898519,73957190.949259,36978595.474630,18489297.737315,9244648.868657,4622324.434329,2311162.217164,1155581.108582,577790.554291,288895.277146,144447.638573,72223.819286,36111.909643,18055.954822,9027.977411,4513.988705,2256.994353,1128.4971765,564.24858825",
                            tileSizeCols: 256,
                            tileSizeRows: 256,
                            tileZeroX: -180,
                            tileZeroY: 90,
                            transparency: null,
                            type: "TomcatTile",
                            url: serviceHelper.getStaticUrl() + "/自然资源瓦片底图/国土地图/矢量注记/",
                            wkid: "4490",
                            compressionQuality: "0",
                        },
                        "JOBzmap"
                    )[0];
                    this.WFSLayer_bzk.visible = false;
                    this.WFSZjLayer_bzk.visible = false;
                    currentMap.add(this.WFSLayer_bzk, 0);
                    currentMap.add(this.WFSZjLayer_bzk, 1);

                    let timer;
                    this.baseView_dxt.watch(
                        "extent",
                        function (evt) {
                            if (!!this.baseMap) {
                                this.moveMap(evt);
                            }
                            // this.switchZoomToDisplayLayer(baseView.zoom);
                        }.bind(this)
                    );
                    var baseMaps = initedLayers.baseMaps;
                    baseView.on(
                        "mouse-wheel",
                        function (evt) {
                            this.moveType = 1;
                        }.bind(this)
                    );
                    this.baseView_dxt.watch("focused", (isFocused) => {
                        if (isFocused) this.moveType = 1;
                    });
                    //按数据角色控制地图遮罩
                    var zone = "";
                    let allMapPower = false;
                    userModel.getDataRoles().forEach((item, index) => {
                        if (index === 0) {
                            zone += "zone <> '" + item.name + "'";
                        } else {
                            zone += " and zone <> '" + item.name + "'";
                        }
                        if (item.name === "全市地图权限") {
                            allMapPower = true;
                        }
                    });
                    var translucentLayer = this.$refs.dxtMap.getLayerById("translucent");
                    baseView.map.reorder(translucentLayer, 100);
                    if (!!translucentLayer) {
                        baseView.whenLayerView(translucentLayer).then(() => {
                            translucentLayer.allSublayers.items.forEach(function (subLayer) {
                                if (subLayer.setDefinitionExpression)
                                    subLayer.setDefinitionExpression(zone);
                                else subLayer.definitionExpression = zone;
                            });
                        });
                    }
                    if (allMapPower) {
                        translucentLayer.visible = false;
                    }
                    // var layer = this.$refs.dxtMap.getLayerById('pipe-line-sort-rain');//用雨水图层来当做全类别图层
                    // if (!layer) {
                    //     this.$message.error('加载管线图层失败')
                    //     return;
                    // }
                    // layer.imageFormat = 'png8';
                    // layer.sublayers.items[3].visible = true;
                    var currentDisplayBaseMap = "";
                    //  baseView.constraints.rotationEnabled = false;//禁止地图旋转
                    this.$refs.dxtMap.$on("base-map-change", function (layerID) {
                        currentDisplayBaseMap = layerID;
                    });
                    baseView.watch(
                        "zoom",
                        function () {
                            if (timer) {
                                clearTimeout(timer);
                            }
                            var zoom = baseView.zoom;
                            for (var key in baseMaps) {
                                if (key == currentDisplayBaseMap) {
                                    baseMaps[key].forEach((baseMapConfig) => {
                                        baseMapConfig.visible = true;
                                    });
                                }
                            }
                            if (zoom > 16) {
                                for (var key in baseMaps) {
                                    baseMaps[key].forEach((baseMapConfig) => {
                                        baseMapConfig.visible = false;
                                    });
                                }
                                this.WFSLayer_bzk.visible = true;
                                this.WFSZjLayer_bzk.visible = true;
                            } else {
                                for (var key in baseMaps) {
                                    baseMaps[key].forEach((baseMapConfig) => {
                                        baseMapConfig.visible = true;
                                    });
                                }
                                this.WFSLayer_bzk.visible = false;
                                this.WFSZjLayer_bzk.visible = false;
                            }
                            // if (zoom > 8) {
                            //     for (var key in baseMaps) {
                            //         if (key == currentDisplayBaseMap) {
                            //             baseMaps[key].forEach((baseMapConfig) => {
                            //                 if (baseMapConfig.id == "osm-layer") {
                            //                     baseMapConfig.visible = false;
                            //                 }
                            //             })
                            //         }
                            //     }
                            // }

                            if (zoom > 15) {
                                if (pipelineLayer) {
                                    pipelineLayer.visible = false;
                                    timer = setTimeout(() => {
                                        eventHelper.emit("refresh-map-dxt");
                                    }, 200);
                                }
                                // if (this.pipe_sewage) {
                                //     this.pipe_sewage.visible = false;
                                // }
                            } else {
                                if (pipelineLayer) {
                                    pipelineLayer.visible = true;
                                }
                                // if (this.pipe_sewage) {
                                //     this.pipe_sewage.visible = true;
                                // }
                            }
                        }.bind(this)
                    );
                    this.baseView_dxt.when(() => {
                        this.$refs.dxtMap.hideLayerById("gz_wxt");
                        baseView.ui.remove("zoom"); //隐藏放大缩小按键
                        this.$refs.dxtMap.view.extent = this.$refs.baseMap.view.extent;
                    });
                    this.$refs.dxtMap.$on(
                        "base-map-change",
                        function (layer) {
                            //监听地图切换，同步切换运行图
                            if (!!this.$refs.baseMap) this.$refs.baseMap.changeBaseMap(layer);
                        }.bind(this)
                    );
                    cb();
                    eventHelper.emit("loading-end");
                }.bind(this)
            );
        },

        revertMapToolBox: function () {
            $(".esri-ui-corner").css({
                left: "0",
            }); /*
                $('.legendContainer').css({
                    right: 'auto',
                    left: '28px',
                });
                $('.legendControl').css({
                    right: 'auto',
                    left: '28px',
                });*/

            $(".map-tools").css({
                left: "10px",
            });
            $(".layerBox").css({
                left: "65px",
            });
        },
        getWKT: function (type, visible) {
            var polygonWkt = "";
            if (type == "polygon") {
                polygonWkt = this.completeEvt;
            } else if (type == "line") {
                var evt = this.completeEvt;
                var vertices = evt.vertices;
                var str = [];
                var firstWkt = "";
                vertices.forEach(function (item, index) {
                    if (index == 0) {
                        firstWkt = item.toString().replace(",", " ");
                    }
                    var xy = item.toString().replace(",", " ");
                    str.push(xy);
                });

                polygonWkt = "LINESTRING( " + str.toString() + ")";
            }
            if (!visible) {
                this.isComplete = true;
            } else {
                this.isComplete = false;
            }
            // this.isPipeCheck = false;
            this.mapSearchWkt = polygonWkt;
            this.getBaseMapData({
                wkt: polygonWkt,
            });
        },
        createPoint: function (legend, subFacilities) {
            var graphicsArr = [];
            subFacilities.forEach(
                function (item) {
                    var icon = !!legend.icon ? legend.icon : legend.facilityTypeName;
                    var newIcon = "";
                    if (legend.type == "gif") {
                        newIcon = "./img/toolbar/" + icon + ".png";
                    } else {
                        newIcon = "./img/toolbar/huawei-" + icon + ".png";
                    }
                    if (!legend.id) {
                        item.fid = Math.random().toString(36).substr(3);
                    }
                    var imgObj = {};
                    if (this.command == "distance") {
                        imgObj = {
                            width: "18px",
                            url: newIcon,
                            height: "18px",
                        };
                    } else {
                        imgObj = {
                            width: "28px",
                            url: newIcon,
                            height: "30px",
                            yoffset: "15px",
                            // xoffset:'-14px'
                        };
                    }
                    var textObj = {
                        color: "red",
                        // text: item.name,
                        yoffset: -18,
                        verticalAlignment: "top",
                        font: {
                            size: 12,
                        },
                    };
                    if (!!item.name) {
                        // textObj.text = item.name;
                    }
                    var attributes = {
                        item: item,
                        facilityTypeName: legend.facilityTypeName,
                        id: item.fid,
                    };
                    var graphic = mapHelper.createPictureMarkSymbol(
                        this.graLayer,
                        item.x,
                        item.y,
                        imgObj,
                        attributes
                    );
                    graphicsArr.push(graphic);
                    this.graphics.push(graphic);
                    this.graphics.push(
                        mapHelper.createTextSymbol(this.graLayer, item.x, item.y, textObj)
                    );
                }.bind(this)
            );
            this.facilityArr[legend.facilityTypeName] = {
                graphics: this.graphics,
                data: subFacilities,
                layer: this.graLayer,
            };
            return graphicsArr;
        },
        /***
         *  canvas图层展示-geometry转换markLines
         */
        geometryToMarkLine(geometry, sort) {
            let markLines = [];
            if (geometry.paths) {
                geometry.paths.forEach((line) => {
                    let lineLength = line.length;
                    for (let i = 0; i < lineLength - 1; i++) {
                        let markLine = {
                            startX: line[i][0],
                            startY: line[i][1],
                            endX: line[i + 1][0],
                            endY: line[i + 1][1],
                            sort: sort,
                        };
                        markLines.push(markLine);
                    }
                });
            }

            return markLines;
        },
        /***
         * canvas图层展示-格式化arcgis获取的空间数据
         * @param wspsgwPromise
         * @param wspsgqPromise
         * @returns {Promise<Array | never>}
         */
        formatExecuteQueryLineData(wspsgwPromise, wspsgqPromise) {
            return Promise.all([wspsgwPromise, wspsgqPromise]).then((results) => {
                let lineData = [];
                results.forEach((item) => {
                    item.features.forEach((graphic) => {
                        let markLines = this.geometryToMarkLine(
                            graphic.geometry,
                            graphic.attributes.sort
                        );
                        lineData.push(...markLines);
                    });
                });
                return lineData;
            });
        },
        closeSearchLoading() {
            this.searchResourceVisible = false; //当查询没有数据的时候，再次点击可以隐藏
        },
        hideDeviceList: function () {
            this.showFacilityList = false;
            if (this.$refs.devicePanel) {
                this.$refs.devicePanel.closePanel();
            }
            if (!this.isPipeExecute) {
                //不是运行图模式下才重置按钮
                this.revertMapToolBox();
            }
            this.checkLayer(false, "运行监测");
        },
        taskCheckVisable() {
            //如果是任务审核模式，分屏幕展示
            this.isTaskCheck = true;
            document.all("mapType")[1].style.display = "none"; //隐藏地形图的底图切换图标
        },
        taskCheckNoVisable() {
            //如果是任务审核模式，只展示一个地图
            this.isTaskCheck = false;
            document.all("mapType")[1].style.display = "flex"; //隐藏地形图的底图切换图标
        },
        dataCheckWorkOrderVisable() {
            //如果是数据审核工单，分屏幕展示
            // this.isDataCheckWorkOrder = true
            document.all("mapType")[1].style.display = "none"; //隐藏地形图的底图切换图标
        },
        dataCheckWorkOrderNoVisable() {
            //如果是数据审核工单，只展示一个地图
            // this.isDataCheckWorkOrder = false
            document.all("mapType")[1].style.display = "flex"; //隐藏地形图的底图切换图标
        },
        // 数据审核工单：查看用户是否有发起核查工单和查看的权限
        checkUserHasCreateAndReadRole() {
            appSplitScreenController.checkUserHasCreateAndReadRole((res) => {
                if (res) this.permission.createAndRead = true;
                else this.permission.createAndRead = false;
            });
        },
        // 加载问题上报图层
        initProblemMap: function () {
            if (!!this.problemMap) {
                this.$refs.baseMap.removeLayerById("problemMap");
                this.problemMap = null;
            }
            var url;
            if (RUNNING_ENV === "dev") {
                url =
                    "http://58.67.157.136:6080/arcgis/rest/services/test/work_order_test/MapServer";
            } else if (RUNNING_ENV === "hwServer") {
                url =
                    "http://58.67.157.136:6080/arcgis/rest/services/test/work_order_test/MapServer";
            } else if (RUNNING_ENV === "prd") {
                url =
                    "http://58.67.157.136:6080/arcgis/rest/services/kfq/work_order/MapServer";
            }
            this.problemMapLayer = new this.apiInstance.MapImageLayer({
                id: "problemMap",
                url: url,
                imageFormat: "png8",
                sublayers: [
                    {
                        id: 0,
                        visible: true,
                    },
                    {
                        id: 1,
                        visible: false,
                    },
                ],
                spatialReference: this.baseView.spatialReference,
            });
            this.problemMapLayer.visible = false;
            this.baseMap.add(this.problemMapLayer, 30);
            this.initproblemMapSubLayer([true, false]);
        },
        initproblemMapSubLayer(visible) {
            if (!!this.problemMapLayer) {
                this.problemMapLayer.findSublayerById(0).visible = visible[0];
                this.problemMapLayer.findSublayerById(1).visible = visible[1];
            }
            this.problemMapLayer.visible = true;
        },

        // 获取数据角色
        getUserRoles() {
            /*
       * 2020-04-08
       * 只是用功能角色，而没有使用数据角色的原因
       * userModel的数据角色需要在appNavigator的userModel.setUserRoles(result.data.roles)设置
       * 比初始化地图的时候慢了，所以暂时只用了功能角色，然后修改了login登录模块的初始化地图的顺序
       *
       * 2020-08-03
       * 改为按功能角色做权限：外委工程管理员可以看到地图、外委不可以
       */
            var isWaiWei = false;
            (userModel.getUserDetail().roles || []).forEach((item) => {
                if (item.name === "外委") {
                    isWaiWei = true;
                }
            });

            return isWaiWei;
        },
        // 取消布防
        cancelDeploy() {
            this.cancelDeployVisible = false;
            eventHelper.emit("submitCancelDeploy", 1);
        },
        lookYxtPipeOperate(moduleName) {
            var moduleList = [
                "yxtPipeAdd",
                "yxtPipeConnect",
                "yxtPipeConnectSewage",
                "yxtPipeDelete",
                "yxtPipeRecycleBin",
                "yxtPipeEdit",
                "yxtPipeConnectSlice",
            ];
            if (moduleName === "yxtPipeAdd") {
                this.yxtPipeIconList.addVisible = true;
            } else {
                this.yxtPipeIconList.addVisible = false;
            }
            moduleList.forEach((item) => {
                if (item === moduleName) {
                    this.$refs[moduleName].initPage();
                } else {
                    this.$refs[item].closePage();
                }
            });
        },
        getDg2000XY() {
            serviceHelper.getJson(
                serviceHelper.getPath("coordTransWgs84ToDg2000"),
                {
                    x: this.searchX,
                    y: this.searchY,
                },
                (res) => {
                    // 科学计数转数字
                    this.wgsTodg.x = +new Number(res.x);
                    this.wgsTodg.y = +new Number(res.y);
                },
                (err) => {
                    this.$message.error("坐标转换失败");
                }
            );
        },

        // 控制标准库管线
        /*initPipeLineSort: function(){
            if (!!this.pipeLineSortService) {
                this.$refs.baseMap.removeLayerById("pipe-line-sort");
                this.pipeLineSortService = null;
            }
            var url = "";
            var sublayers = [];
            if (['dev', 'test'].indexOf(RUNNING_ENV) > -1) {// 雨水-171, 污水-114, 合流-57, 全部-0
                url = "http://*************:9000/arcgis251/arcgis/rest/services/test/pipe_group_by_sort_test/MapServer";
                sublayers = [
                    {
                        id: 171,
                        visible: false
                    },
                    {
                        id: 114,
                        visible: true
                    },
                    {
                        id: 57,
                        visible: false
                    },
                    {
                        id: 0,
                        visible: false
                    }
                ];
            } else if (RUNNING_ENV === 'prd') {// 雨水-135, 污水-90, 合流-45, 全部-0
                url = 'http://*************:9000/arcgis251/arcgis/rest/services/DGPS/pipe_group_by_sort/MapServer';
                sublayers = [
                    {
                        id: 135,
                        visible: false
                    },
                    {
                        id: 90,
                        visible: true
                    },
                    {
                        id: 45,
                        visible: false
                    },
                    {
                        id: 0,
                        visible: false
                    }
                ];
            }
            this.pipeLineSortService = new this.apiInstance.MapImageLayer({
                id: 'pipe-line-sort',
                url: url,
                imageFormat: 'png8',
                sublayers: sublayers,
                spatialReference: this.baseView.spatialReference
            });
            this.pipeLineSortService.visible = true;
            this.baseMap.add(this.pipeLineSortService, 30);
            mapHelper.setCenter(this.baseView, this.baseView.center.x + 0.0001, this.baseView.center.y, this.baseView.zoom);
        },*/
        /*initPipeLineSortSubMap: function(zoom){
            if (this.pipeLineSortService && this.pipeLineSortService.allSublayers) {
                let intZoom = Math.round(zoom)
                if (intZoom > 8) {
                    this.showSubLayer([58, 76], this.pipeLineSortService.allSublayers)
                } else if (intZoom == 8) {
                    this.showSubLayer([40], this.pipeLineSortService.allSublayers)
                }else if (intZoom == 7) {
                    this.showSubLayer([33], this.pipeLineSortService.allSublayers)
                }else if (intZoom == 6) {
                    this.showSubLayer([33], this.pipeLineSortService.allSublayers)
                }else if (intZoom == 5) {
                    this.pipeLineSortService.findSublayerById(28).visible = true;
                    this.pipeLineSortService.findSublayerById(33).visible = true;
                    this.pipeLineSortService.visible = true
                }
            }
        },*/
        /*showSubLayer(ids, layerGroup){
            layerGroup.items.forEach((subLayer, index) => {
                if (ids.includes(subLayer.id)) {
                    subLayer.visible = true;
                } else {
                    subLayer.visible = false;
                }
            })
            this.pipeLineSortService.visible = true
        },*/
        /*customLayer: function () {
            this.showAllStanderSortLayer(true);
            // this.showStanderSortLayer('pipe-line-sort-mix');
            // this.showStanderSortLayer('pipe-line-sort-dirty');
            // this.$refs.baseMap.hideLayerById('pipe-line-sort-mix');
            // this.$refs.baseMap.hideLayerById('pipe-line-sort-dirty');
        },*/
        showAllStanderSortLayer: function (layerId, arr) {
            var layer = this.$refs.baseMap.getLayerById(layerId);
            layer.when(() => {
                this.baseView.map.reorder(layer, 20);
                if (!layer) {
                    return;
                }
                layer.imageFormat = "png8";
                // 雨水-0, 污水-1, 合流-2, 全部-3
                if (layer.sublayers) {
                    layer.sublayers.items[0].visible = arr[0] || false;
                    layer.sublayers.items[1].visible = arr[1] || false;
                    layer.sublayers.items[2].visible = arr[2] || false;
                    layer.sublayers.items[3].visible = arr[3] || false;
                }

                this.$refs.baseMap.showLayerById(layerId);
            });
        },
        layerTreeManage() {
            var layerTree =
                this.$refs.baseMap.getSubComponent("layerControl").$refs.layerTree;
            layerTree.$on(
                "treeChange",
                function (res) {
                    if (!!res.children) {
                        return;
                    } else if (
                        ["标准库-雨水管", "标准库-污水管", "标准库-合流管"].includes(
                            res.name
                        )
                    ) {
                        if (res.showIcon) {
                            var layerIds = {
                                "标准库-雨水管": "pipe-line-sort-rain",
                                "标准库-污水管": "pipe-line-sort-dirty",
                                "标准库-合流管": "pipe-line-sort-mix",
                            };
                            var visibles = {
                                "标准库-雨水管": [true, false, false, false],
                                "标准库-污水管": [false, true, false, false],
                                "标准库-合流管": [false, false, true, false],
                            };
                            let layerId = layerIds[res.name] || "";
                            let subVisible = visibles[res.name] || [];
                            this.showAllStanderSortLayer(layerId, subVisible);
                        }
                    }
                }.bind(this)
            );
        },
        //修改查询数据库类型
        changeConnipType() {
            userModel.setConnipType(this.connipType);
        },
        /*showRainStanderSortyLayer: function (flag) {
            var layer = this.$refs.baseMap.getLayerById('pipe-line-sort-rain');//单独显示雨水图层
            this.baseView.map.reorder(layer, 20);
            if (!layer) {
                this.$message.error('加载管线图层失败')
                return;
            }
            layer.imageFormat = 'png8';
            layer.sublayers.items[0].visible = flag;
            layer.sublayers.items[1].visible = false;
            layer.sublayers.items[2].visible = false;
            layer.sublayers.items[3].visible = false;
        },*/
        /*showStanderSortLayer: function (layerId, visible = false) {
            var layer = this.$refs.baseMap.getLayerById(this.defaultPipe);
            this.baseView.map.reorder(layer, 20);
            if (!layer) {
                return;
            }
            layer.imageFormat = 'png8';
            if (layerId == '标准库-雨水管') {
                layer.sublayers.items[0].visible = visible;
            } else if (layerId == '标准库-污水管') {
                layer.sublayers.items[1].visible = visible;
            } else if (layerId == '标准库-合流管') {
                layer.sublayers.items[2].visible = visible;
            }

            this.checkStanderSortLayer();
        },*/
        /*checkStanderSortLayer() {
            let rain = false;
            let dirty = false;
            let mix = false;
            let layerControl = this.$refs.baseMap.$refs.layerControl;
            if (layerControl && !!layerControl.getLayerVisible) {
                try {
                    dirty = layerControl.getLayerVisible('标准库-污水管');
                    rain = layerControl.getLayerVisible('标准库-雨水管');
                    mix = layerControl.getLayerVisible('标准库-合流管');
                }catch (e) {
                    console.log(e)
                }
            }
            if (rain || dirty || mix) {
                var layer = this.$refs.baseMap.getLayerById(this.defaultPipe);
                if (!layer) {
                    return;
                }
                this.$refs.baseMap.showLayerById(this.defaultPipe);
            }
        }*/
        /**
         * 监听地图移动
         * 移动到监测点图标上显示信息框
         */
        watchMoveEvent() {
            if (this.moveEvent) {
                return;
            }
            this.moveEvent = this.baseView.on(
                "pointer-move",
                commonUtil.throttle(
                    function (evt) {
                        if (this.pickupXY === true) {
                            var mapPoint = this.baseView.toMap(evt);
                            this.mapPointX = Math.round(mapPoint.x * 1000) / 1000;
                            this.mapPointY = Math.round(mapPoint.y * 1000) / 1000;
                        }
                        this.baseView.hitTest(evt).then(
                            function (response) {
                                if (response.results.length > 0) {
                                    var station = response.results[0].graphic.attributes;
                                    if (!!station && !!station.item) {
                                        if (station.item.state == 0 && station.item.status == 1) {
                                            this.currentStatus = "offlineStatus-bg";
                                        } else if (station.item.type == "rainFall") {
                                            if (station.item.state == 1) {
                                                this.currentStatus = "yellow-bg";
                                            } else if (station.item.state == 2) {
                                                this.currentStatus = "warningStatus-bg";
                                            } else if (station.item.state == 3) {
                                                this.currentStatus = "dangerStatus-bg";
                                            } else {
                                                this.currentStatus = "";
                                            }
                                        } else {
                                            if (station.item.state == 3) {
                                                this.currentStatus = "yellow-bg";
                                            } else if (station.item.state == 5) {
                                                this.currentStatus = "dangerStatus-bg";
                                            } else {
                                                this.currentStatus = "";
                                            }
                                        }
                                        this.highLightFacility = station;
                                        var screenPoint = response.screenPoint;
                                        $(".facilityContainer")
                                            .show()
                                            .css({
                                                left: screenPoint.x + 10,
                                                top: screenPoint.y - 10,
                                            });
                                    }
                                } else {
                                    $(".facilityContainer").hide();
                                }
                            }.bind(this)
                        );
                    }.bind(this)
                )
            );
        },
        /**
         * 移除监听地图移动事件
         */
        removeMoveEvent() {
            this.moveEvent && this.moveEvent.remove();
            this.moveEvent = null;
        },
    },
    created: function () {
        //加载所有切片信息
        // this.getDistrictData();
        arcgisMapController.getPipeLayers(
            function (res) {
                if (res.success == true) {
                    var data = res.data;
                    this.pipeLayers = data;
                    this.selectLayer = data[0].text;
                }
            }.bind(this),
            function (error) {
                console.log("arcgisMapController__错误：" + error);
            }
        );
        // 获取权限
        this.checkUserHasCreateAndReadRole();
        permission.getYxtPermission("data-check-order-report", (res) => {
            this.permission.dataCheckOrderReport = !!res;
        });
        // 设置pointClickState
        eventHelper.on("setPointClickState", (state) => {
            this.pointClickState = state;
        });
        this.dataCheckOrderBtnVisible = appMenuModel.getCurrentModel() == "gis";
        eventHelper.on("currentModelChange", (key) => {
            if (key == "gis") this.dataCheckOrderBtnVisible = true;
            else this.dataCheckOrderBtnVisible = false;
        });
    },
    mounted: function () {
        var isWaiWei = this.getUserRoles();
        if (isWaiWei) {
            eventHelper.emit("showEntrustUserPic", "");
            this.mapLoading = false;
            return;
        }
        //加载设备
        this.facilityArr = {};
        this.map_ = this.initBaseMap();
        $(".rightPanelControl").hide(); //刚开始先隐藏

        //菜单关闭设备列表
        eventHelper.on(
            "close-facility-list",
            function (flag) {
                this.hideDeviceList();
                this.checkLayer(false, "运行监测");
            }.bind(this)
        );

        //打开设备列表
        eventHelper.on(
            "open-facility-list",
            function (flag) {
                this.watchMoveEvent(); //监听鼠标移动到测站图标上
                $("#statusToolsBox").css("top", "60px");
                if (!flag) {
                    //来自菜单点击
                    this.checkLayer(true, "运行监测");
                    /* setTimeout(()=>{
                 this.resetFacilityIcon()
                 },300)*/
                }
                if (!this.isPipeExecute) {
                    // //不是运行图模式下才重置按钮
                    // $('.legendContainer').css({
                    //     left: '277px'
                    // });
                    // $('.legendControl').css({
                    //     left: '277px'
                    // });
                }
                this.showFacilityList = true;
                $(".esri-ui-corner").css({
                    left: "250px",
                });
                // $('.map-tools').css({
                //     left: '320px'
                // })
                $(".layerBox").css({
                    left: "315px",
                });
                if (cacheFacilities.length == 0) {
                    var graphics = this.$refs.baseMap.getFacilityLayer().graphics;
                    if (!!graphics && graphics.items.length > 0) {
                        cacheFacilities = graphics.items.slice(0);
                    }
                    console.log(cacheFacilities);
                }
                var center = this.baseView.extent.center;

                this.facilityListObj = {};
                facilityController.getCurrentUserFacilitysMonitor(
                    function (facilities) {
                        facilities.forEach((facility) => {
                            this.facilityListObj[facility.imei] =
                                facilityModel.getFacility(facility);
                        });
                        setTimeout(() => {
                            this.$refs.baseMap.setCenter(center.x + 0.00001, center.y);
                        }, 100);
                    }.bind(this)
                );
            }.bind(this)
        );
        //测站筛选
        this.$on(
            "update-display-station",
            function (stations) {
                if (cacheFacilities.length == 0) {
                    var graphics = this.$refs.baseMap.getFacilityLayer().graphics;
                    if (!!graphics && graphics.items.length > 0) {
                        cacheFacilities = graphics.items.slice(0);
                    }
                    console.log(cacheFacilities);
                }
                cacheFacilities.forEach((cacheFacility) => {
                    var display = false;
                    stations.forEach((station) => {
                        if (
                            !!cacheFacility.attributes &&
                            station.fid == cacheFacility.attributes.item.fid
                        ) {
                            display = true;
                            return;
                        }
                    });
                    cacheFacility.visible = display;
                });
                var center = this.baseView.extent.center;
                setTimeout(
                    function () {
                        this.$refs.baseMap.setCenter(center.x + 0.000001, center.y);
                    }.bind(this),
                    100
                );
            }.bind(this)
        );
        //关闭所有选中菜单
        eventHelper.on(
            "close-all-menu",
            function () {
                // 运行图管线增删改查
                if (!!this.$refs.yxtPipeAdd && this.$refs.yxtPipeAdd.pageVisible) {
                    this.$refs.yxtPipeAdd.closePage();
                }
                if (
                    !!this.$refs.yxtPipeConnect &&
                    this.$refs.yxtPipeConnect.pageVisible
                ) {
                    this.$refs.yxtPipeConnect.closePage();
                }
                if (
                    !!this.$refs.yxtPipeConnectSewage &&
                    this.$refs.yxtPipeConnectSewage.pageVisible
                ) {
                    this.$refs.yxtPipeConnectSewage.closePage();
                }
                if (
                    !!this.$refs.yxtPipeDelete &&
                    this.$refs.yxtPipeDelete.pageVisible
                ) {
                    this.$refs.yxtPipeDelete.closePage();
                }
                if (
                    !!this.$refs.yxtPipeRecycleBin &&
                    this.$refs.yxtPipeRecycleBin.pageVisible
                ) {
                    this.$refs.yxtPipeRecycleBin.closePage();
                }
                if (!!this.$refs.yxtPipeEdit && this.$refs.yxtPipeEdit.pageVisible) {
                    this.$refs.yxtPipeEdit.closePage();
                }
                if (
                    !!this.$refs.yxtPipeConnectSlice &&
                    this.$refs.yxtPipeConnectSlice.pageVisible
                ) {
                    this.$refs.yxtPipeConnectSlice.closePage();
                }
                // 标注的新增和修改
                if (!!this.$refs.markAdd && this.$refs.markAdd.pageVisible) {
                    this.$refs.markAdd.closePage();
                }
                // 标注的新增和修改
                if (
                    !!this.$refs.concernManage &&
                    this.$refs.concernManage.pageVisible
                ) {
                    this.$refs.concernManage.close(true);
                }
                // 管线追溯工具
                if (
                    !!this.$refs.editTraceTool &&
                    this.$refs.editTraceTool.pageVisible
                ) {
                    this.$refs.editTraceTool.close();
                }
                if (
                    !!this.$refs.traceMainPipe &&
                    this.$refs.traceMainPipe.pageVisible
                ) {
                    this.$refs.traceMainPipe.close();
                }
                //标注点
                if (
                    !!this.$refs.markPointList &&
                    this.$refs.markPointList.markPointListVisible
                ) {
                    this.$refs.markPointList.close();
                }
                //混淆点
                if (
                    !!this.$refs.mixConnectPoint &&
                    this.$refs.mixConnectPoint.mixConnectPointVisible
                ) {
                    this.$refs.mixConnectPoint.close();
                }
                //进展展示
                if (
                    !!this.$refs.sewageWaterThematicMap &&
                    this.$refs.sewageWaterThematicMap.pageVisible
                ) {
                    this.$refs.sewageWaterThematicMap.close();
                }
                //运行图审核
                if (
                    !!this.$refs.taskCheck &&
                    this.$refs.taskCheck.taskCheckListVisible
                ) {
                    this.$refs.taskCheck.close();
                }
                //运行图审批日志
                if (!!this.$refs.executeLog && this.$refs.executeLog.logVisible) {
                    this.$refs.executeLog.close();
                }
                //数据审核工单
                if (
                    !!this.$refs.dataCheckWorkOrder &&
                    this.$refs.dataCheckWorkOrder.visible
                ) {
                    this.$refs.dataCheckWorkOrder.close();
                }
            }.bind(this)
        );

        //关闭不是运行图的选中菜单
        eventHelper.on(
            "close-normal-menu",
            function () {

                console.log('close-normal-menu newPlan plan close');
                //编辑排水单元
                if (
                    this.pointClickState === "editRegion" ||
                    this.pointClickState === "addRegion"
                ) {
                    this.editRegionOpen = false;
                    this.$refs.regionForm.closeDialog();
                }
                //专题图和关注点
                if (!!this.$refs.thematicMap && this.$refs.thematicMap.pageVisible) {
                    this.$refs.thematicMap.close();
                }
                // 水质关注点
                if (
                    !!this.$refs.concernManage &&
                    this.$refs.concernManage.pageVisible
                ) {
                    this.$refs.concernManage.close(true);
                }
                // //关闭编辑分析区块
                // if(!!this.$refs.analysisBlockCutForm && this.pointClickState === 'editAnalysisBlock'){
                //     this.$refs.analysisBlockCutForm.closeDialog()
                // }
                //关闭网格划分区块
                if (
                    !!this.$refs.gridDefined &&
                    this.$refs.gridDefined.gridDefinedShowFlag
                ) {
                    this.$refs.gridDefined.close();
                }
                if (!!this.$refs.newPlan && this.$refs.newPlan.newPlanShow) {
                    this.$refs.newPlan.close();
                }
                if (!!this.$refs.planAccount && this.$refs.planAccount.planAccountShow) {
                    this.$refs.planAccount.close();
                }
                if (!!this.$refs.riskOneMap && this.$refs.riskOneMap.riskOneMapShow) {
                    this.$refs.riskOneMap.close();
                }
                if (!!this.$refs.clearOneMap && this.$refs.clearOneMap.clearOneMapShow) {
                    this.$refs.clearOneMap.close();
                }
                if(!!this.$refs.appRiskPointManager && this.$refs.appRiskPointManager.appRiskPointManagerVisible){
                    this.$refs.appRiskPointManager.close();
                }
            }.bind(this)
        );

        eventHelper.on(
            "carDetail-clicked",
            function (point) {
                this.$refs.carDetail.open(point.item);
            }.bind(this)
        );

        eventHelper.on(
            "openMapSearch",
            function () {
                this.mapSearchShow = !this.mapSearchShow;
            }.bind(this)
        );
        //坐标定位
        eventHelper.on(
            "searchXY",
            function () {
                this.searchXy();
                if (this.xyStates != "search") {
                    this.clearGraLayer();
                    this.sewerageEntry = false;
                }
                this.xyStates = "search";
            }.bind(this)
        );

        eventHelper.on(
            "sewerageAdd",
            function () {
                this.sewerageAdd();
                if (this.xyStates != "add") {
                    this.clearGraLayer();
                }
                this.xyStates = "add";
            }.bind(this)
        );
        var initCapturePlugin = false;
        /***
         * 污水专题图展示
         */
        eventHelper.on("sewage-water-thematic-map1", async () => {
            //wait 先暂时+1屏蔽叼这个功能
            if (interval != null) {
                this.editLogLayer.visible = false;
                clearInterval(interval);
                interval = null;
            } else {
                this.editLogLayer.visible = true;
                interval = setInterval(
                    function () {
                        if (!!this.isPipeExecute) {
                            this.editLogLayer.visible = !this.editLogLayer.visible;
                        }
                    }.bind(this),
                    1500
                );
            }

            /*this.showSewageWaterThematicMap = !this.showSewageWaterThematicMap;
             let dirtyPipeZoomLayer = this.baseView.map.findLayerById('dirty-pipe-zoom');
             if (!!dirtyPipeZoomLayer) {
             dirtyPipeZoomLayer.visible = this.showSewageWaterThematicMap;
             }
             if (this.showSewageWaterThematicMap) {

             let arrowCanvasLayer = new CanvasLayer.createCanvasLayer({id: "arrowCanvas"});
             this.baseView.map.add(arrowCanvasLayer);

             let queryParam = {
             where: "length>=15",
             returnGeometry: true,
             outFields: ["*"]
             };
             let wspsgwPromise = mapHelper.executeQueryTask(dirtyPipeZoomLayer.url + "/3", queryParam);
             let wspsgqPromise = mapHelper.executeQueryTask(dirtyPipeZoomLayer.url + "/4", queryParam);

             let lineData = await this.formatExecuteQueryLineData(wspsgwPromise, wspsgqPromise);

             arrowCanvasLayer.getCanvas().then((canvasDom) => {
             new CanvasArrow(this.baseView, canvasDom, {
             data: lineData,
             //箭头移动速度
             arrowSpeed: 5,
             //箭头移动间隔
             arrowStepSpan: 0.1,
             //箭头间距
             stepSize: 25,
             });
             });
             } else {
             let arrowCanvasLayer = this.baseView.map.findLayerById("arrowCanvas");
             this.baseView.map.remove(arrowCanvasLayer);
             }*/

            //let rainPipeZoomLayer = this.$refs.baseMap.showLayerById('rain-pipe-zoom');
        });
        eventHelper.on(
            "pipeInspection",
            function () {
                if (!initCapturePlugin) {
                    //初始化截图功能
                    Init(
                        function (type, x, y, width, height, info, content, localpath) {
                            initPlugin = true;
                            if (this.captureState == "pipeCheck") {
                                this.$refs.pipeCheck.setImg(content);
                            } else if (this.captureState == "infoWindow") {
                                this.$refs.infoWindow.setImg(content);
                            }
                        }.bind(this)
                    );
                }
            }.bind(this)
        );
        //放大图片
        eventHelper.on(
            "showBigImg",
            function (url) {
                this.currentPic = url;
                this.showBigImg = true;
            }.bind(this)
        );
        eventHelper.on(
            "pollution-analysis",
            function () {
                if (!initCapturePlugin) {
                    //初始化截图功能
                    Init(
                        function (type, x, y, width, height, info, content, localpath) {
                            console.log(localpath);
                            initPlugin = true;
                            if (this.captureState == "pipeCheck") {
                                this.$refs.pipeCheck.setImg(content);
                            } else if (this.captureState == "infoWindow") {
                                this.$refs.infoWindow.setImg(content);
                            }
                        }.bind(this)
                    );
                }
            }.bind(this)
        );
        eventHelper.on(
            "openDevicePanel",
            function (selectedItem) {
                this.$refs.baseMap.setCenter(selectedItem.x, selectedItem.y, 19);
            }.bind(this)
        );
        eventHelper.on(
            "addMapLine",
            function (wkt) {
                this.addMapLine(wkt);
            }.bind(this)
        );
        eventHelper.on(
            "pipe-handover",
            function () {
                this.$refs.pipeHandover.init();
                this.command = 0;
            }.bind(this)
        );
        eventHelper.on(
            "pipe-road-edit",
            function () {
                this.$refs.pipeRoadEdit.init();
            }.bind(this)
        );
        eventHelper.on(
            "mark-point-list",
            function () {
                this.$refs.markPointList.open();
                this.command = 0;
            }.bind(this)
        );
        eventHelper.on(
            "sewage-water-thematic-map",
            function () {
                this.$refs.sewageWaterThematicMap.init();
                this.command = 0;
            }.bind(this)
        );
        eventHelper.on(
            "edit-pipe-info",
            function () {
                this.$refs.editPipeInfo.init();
                this.command = 0;
            }.bind(this)
        );
        eventHelper.on(
            "edit-trace-tool",
            function () {
                this.$refs.editTraceTool.init();
                this.$refs.traceMainPipe.init();
                // 流向工具和追溯分析冲突的代码(已注释暂时别删)
                // eventHelper.emit('closeRetrospectDetailTrace', "");
                this.command = 0;
            }.bind(this)
        );
        eventHelper.on(
            "maintain-detail",
            function () {
                this.$refs.maintainDetail.open();
                this.command = 0;
            }.bind(this)
        );
        eventHelper.on(
            "task-check-show",
            function (isShow) {
                isShow ? this.taskCheckVisable() : this.taskCheckNoVisable(); //如果是任务审查功能则分屏幕展示
            }.bind(this)
        );
        eventHelper.on(
            "data-check-work-order",
            function () {
                if (!taskMapInit) {
                    taskMapInit = true;
                    this.initDxtMap(
                        function () {
                            this.$refs.dxtMap.view.extent = this.$refs.baseMap.view.extent;
                        }.bind(this)
                    );
                }
                this.$refs.dataCheckWorkOrder.open();
                // this.command = 0;
            }.bind(this)
        );
        eventHelper.on(
            "data-check-work-order-show",
            function (isShow) {
                isShow
                    ? this.dataCheckWorkOrderVisable()
                    : this.dataCheckWorkOrderNoVisable(); //如果是数据审核工单功能则分屏幕展示
            }.bind(this)
        );

        eventHelper.on(
            "right-panel-close",
            function (id) {
                eventHelper.emit("appExecutionMenuClose", id);
                if (id == "task-check") this.taskCheckNoVisable(); //如果是任务审查功能则关闭分屏显示
                if (id == "data-check-work-order") this.dataCheckWorkOrderNoVisable(); //如果是任务审查功能则关闭分屏显示
            }.bind(this)
        );
        eventHelper.on(
            "mix-connect-point",
            function () {
                this.$refs.mixConnectPoint.open();
            }.bind(this)
        );
        eventHelper.on(
            "unit-analyze",
            function () {
                this.$refs.unitAnalyze.open();
            }.bind(this)
        );
        eventHelper.on(
            "unit-statistics",
            function () {
                this.$refs.unitStatistics.open();
            }.bind(this)
        );
        eventHelper.on(
            "concern-manage",
            function () {
                this.$refs.concernManage.open();
            }.bind(this)
        );
        eventHelper.on(
            "execute-log",
            function () {
                this.$refs.executeLog.open();
            }.bind(this)
        );
        eventHelper.on(
            "cut-region-info-edit",
            function () {
                this.$refs.regionInfoEdit.open();
            }.bind(this)
        );
        eventHelper.on(
            "sewage-system",
            function () {
                this.$refs.systemSelector.open();
            }.bind(this)
        );
        eventHelper.on(
            "clearMapLine",
            function (params) {
                this.graLayer.removeMany(this.searchGraphic);
            }.bind(this)
        );
        eventHelper.on(
            "refresh-map",
            function (center) {
                this.$refs.baseMap.setCenter(center.x + 0.1, center.y + 0.1);
            }.bind(this)
        );
        eventHelper.on(
            "general-layer-open",
            function () {
                this.$refs.generalLayer.open();
            }.bind(this)
        );
        //打开隐患点功能
        eventHelper.on(
            "app-risk-point-manager",
            function () {
                this.$refs.appRiskPointManager.open();
            }.bind(this)
        );
        //打开检测一张图
        eventHelper.on(
            "risk-one-map",
            function () {
                this.$refs.riskOneMap.open();
            }.bind(this)
        );
        //打开清疏一张图
        eventHelper.on(
            "clear-one-map",
            function () {
                this.$refs.clearOneMap.open();
            }.bind(this)
        );
        //片区管理
        var self = this;

        function isShowGraphic() {
            if (self.allRegionData.length > 0 && self.geometryArray.length == 0) {
                self.geometryArray = mapHelper.createGraphicAndText(
                    self.allRegionData,
                    self.baseView
                );
            }
        }

        window.drawStatus = "regionComplete";
        //片区编辑
        eventHelper.on(
            "editRegion",
            function () {
                this.$notify({
                    title: "温馨提示",
                    message: "请在地图中点击要编辑的排水单元",
                });
                var regionThis = this.$refs.regionForm;
                this.checkLayer();
                mapHelper.stopEditing();
                /*if (this.allRegionLoad == false) {
             eventHelper.emit('loading-start');
             }*/
                if (regionThis.containerVisible == true) {
                    regionThis.closeDialog();
                } else {
                    this.pointClickState = "editRegion";
                }
            }.bind(this)
        );

        //片区添加
        eventHelper.on(
            "addRegion",
            function () {
                this.$notify({
                    title: "温馨提示",
                    message: "请在地图中绘画新的排水单元",
                });
                //工具的调用必须放在后面执行;
                this.pointClickState = "addRegion";
                this.checkLayer();
                // this.getDistrictData();
                mapHelper.mapRegionTool(this.graLayer, this.baseView);
            }.bind(this)
        );
        //分析区块编辑
        eventHelper.on(
            "editAnalysisBlock",
            function () {
                this.$notify({
                    title: "温馨提示",
                    message: "请在地图中点击要编辑的分析区",
                });
                var regionThis = this.$refs.regionForm;
                this.checkLayer(true, "污水系统");
                this.checkLayer(true, "片区");
                this.checkLayer(true, "自定义区块");
                mapHelper.stopEditing();
                if (regionThis.containerVisible == true) {
                    regionThis.closeDialog();
                } else {
                    this.pointClickState = "editAnalysisBlock";
                }
            }.bind(this)
        );
        //分析区块添加
        eventHelper.on(
            "addAnalysisBlock",
            function () {
                this.$notify({
                    title: "温馨提示",
                    message: "请在地图中绘画新的分析区",
                });
                //工具的调用必须放在后面执行;
                this.pointClickState = "addAnalysisBlock";
                this.checkLayer(true, "污水系统");
                this.checkLayer(true, "片区");
                this.checkLayer(true, "自定义区块");
                // this.getDistrictData();
                mapHelper.mapAnalysisBlockTool(this.graLayer, this.baseView);
            }.bind(this)
        );

        eventHelper.on(
            "locateToExtent",
            function (extent) {
                this.$refs.baseMap.locateToExtent(extent, 2);
            }.bind(this)
        );
        //自相交提示
        eventHelper.on(
            "dialogShow",
            function () {
                this.dialogVisible = true;
            }.bind(this)
        );
        //取消布防提示
        eventHelper.on(
            "showCancelDeployVisible",
            function () {
                this.cancelDeployVisible = true;
            }.bind(this)
        );
        //区块统计跳转过来并画出对应的面
        eventHelper.on(
            "regionPolygon",
            function (date) {
                if (!date.state) this.addMapSurface(date.wkt);
            }.bind(this)
        );
        //关闭区块统计的时候清除图层
        eventHelper.on(
            "closeRegionPolygon",
            function () {
                this.clearGraLayer();
            }.bind(this)
        );
        var lineStyle = {
            color: [255, 0, 0],
            width: 4,
        };

        interval = setInterval(
            function () {
                if (!!this.isPipeExecute) {
                    this.editLogLayer.visible = !this.editLogLayer.visible;
                }
            }.bind(this),
            1500
        );
        //点击分片区时候跳转过来
        eventHelper.on(
            "getEditLog",
            function (date) {
                this.editLogLayer.removeAll();
                pipeService.getEditLog(
                    date.district,
                    date.zone,
                    function (result) {
                        let lineWkt = result.details;
                        if (lineWkt.length > 0) {
                            lineWkt.forEach((editLog) => {
                                mapHelper.wkbToPolyline(
                                    this.editLogLayer,
                                    editLog.wkt,
                                    lineStyle
                                );
                            });
                        }
                        this.clearGraLayer();
                        this.addMapSurface(result.areaWkt, 1.8, 2000);
                    }.bind(this),
                    function (error) {
                        console.error(error);
                    }
                );
            }.bind(this)
        );
        //关闭污水分片区
        eventHelper.on(
            "close-sewage-water-thematic-map",
            function () {
                this.editLogLayer.removeAll();
                this.clearGraLayer();
            }.bind(this)
        );
        //运行图模式 true-打开   false-关闭
        eventHelper.on(
            "pipe-execute-mode",
            function (boolean) {
                //菜单点击发起
                if (boolean == undefined || boolean) {
                    boolean = true;
                    setTimeout(
                        function () {
                            // this.checkLayer(false, '标准库-雨水管');
                            // this.checkLayer(false, '标准库-合流管');
                            // this.checkLayer(false, '标准库-污水管');
                            // this.$refs.baseMap.hideLayerById('pipe-line-sort-rain');
                            // this.$refs.baseMap.hideLayerById('pipe-line-sort-dirty');
                            // this.$refs.baseMap.hideLayerById('pipe-line-sort-mix');
                            // this.checkLayer(true, '管网运行图');
                            // var layer = this.$refs.baseMap.getLayerById('edit-pipe-web');//用雨水图层来当做全类别图层
                            // this.baseView.map.reorder(layer, 20);
                        }.bind(this),
                        1000
                    );
                    $(".esri-compass").css({
                        top: "190px",
                        position: "fixed",
                        right: "25px",
                    });
                    $(".rightPanelControl").show();
                    $(".rightPanelControl").css({
                        right: "auto",
                        left: "calc(100% - 56px)",
                        display: "block",
                    });
                    $("#topbar").css({
                        left: "auto",
                        right: "110px",
                        position: "fixed",
                        marginTop: "119px",
                        top: "0px !important",
                    });

                    $(".tertiaryMenu").css({
                        //不是运行图模式隐藏
                        display: "none",
                    });
                } else {
                    // this.checkLayer(false, '管网运行图');
                    // this.checkLayer(true, '标准库-雨水管');
                    // this.checkLayer(true, '标准库-合流管');
                    // this.checkLayer(true, '标准库-污水管');
                    // this.$refs.baseMap.showLayerById('pipe-line-sort-rain')
                    // this.$refs.baseMap.showLayerById('pipe-line-sort-dirty')
                    // this.$refs.baseMap.showLayerById('pipe-line-sort-mix')
                    /*$(".esri-zoom").css({
                    'position': 'unset',
                });*/
                    $(".esri-compass").css({
                        position: "unset",
                    });
                    if (this.showFacilityList) {
                        $(".legendContainer").css({
                            left: "277px",
                        });
                        $(".legendControl").css({
                            left: "277px",
                        });
                    } else {
                        /*$(".legendContainer").css({
                        right: 'auto',
                        left: '28px',
                    });
                    $(".legendControl").css({
                        right: 'auto',
                        left: '28px',
                    });*/
                    }
                    $(".rightPanelControl").hide();
                    $("#topbar").css({
                        left: 0,
                        marginTop: "100px",
                        right: "100px",
                        position: "absolute",
                        top: "5px !important",
                    });
                }
                this.isPipeExecute = boolean;
                this.toolsVisible = !boolean;
                this.$refs.dataCheckWorkOrder &&
                this.$refs.dataCheckWorkOrder.close(true);
            }.bind(this)
        );
        var taskMapInit = false; //是否已经加载了标准库地图
        //运行图审核打开标准库地图
        eventHelper.on(
            "task-check",
            function () {
                if (!taskMapInit) {
                    // 初始话标准化地图
                    taskMapInit = true;
                    this.initDxtMap(
                        function () {
                            if (!!this.$refs.dxtMap.view && !!this.$refs.baseMap.view) {
                                this.$refs.dxtMap.view.extent = this.$refs.baseMap.view.extent;
                            }
                        }.bind(this)
                    );
                }
                this.$refs.taskCheck.open();
                this.command = 0;
            }.bind(this)
        );
        //刷新标准库地图
        eventHelper.on(
            "refresh-map-dxt",
            function () {
                if (!!this.$refs.dxtMap.view && !!this.$refs.baseMap.view) {
                    this.$refs.dxtMap.view.extent = this.$refs.baseMap.view.extent;
                }
            }.bind(this)
        );
        eventHelper.on(
            "add-analysis",
            function () {
                this.$refs.addAnalysis.open();
            }.bind(this)
        );
        eventHelper.on(
            "thematic-map",
            function () {
                this.$refs.thematicMap.open();
            }.bind(this)
        );

        eventHelper.on(
            "init-problem-map",
            function () {
                eventHelper.emit("setPointClickState", "problemMap");
                // this.initProblemMap();
            }.bind(this)
        );
        eventHelper.on(
            "control-problem-map",
            function (visible) {
                this.initproblemMapSubLayer(visible);
            }.bind(this)
        );
        eventHelper.on(
            "close-init-problem-map",
            function () {
                this.$refs.baseMap.removeLayerById("problemMap");
                eventHelper.emit("setPointClickState", "");
            }.bind(this)
        );
        eventHelper.on(
            "layerControlCheckLayer",
            function (data) {
                this.checkLayer(data.isCheck, data.name);
            }.bind(this)
        );

        eventHelper.on(
            "lookYxtPipeAdd",
            function () {
                this.lookYxtPipeOperate("yxtPipeAdd");
            }.bind(this)
        );
        eventHelper.on(
            "lookYxtPipeEdit",
            function () {
                this.lookYxtPipeOperate("yxtPipeEdit");
            }.bind(this)
        );
        eventHelper.on(
            "lookYxtPipeConnect",
            function () {
                this.lookYxtPipeOperate("yxtPipeConnect");
            }.bind(this)
        );
        eventHelper.on(
            "lookYxtPipeDelete",
            function () {
                this.lookYxtPipeOperate("yxtPipeDelete");
            }.bind(this)
        );
        eventHelper.on(
            "loadWorkOrderDetail",
            function (data) {
                this.workOrderDetailList = data;
                this.$nextTick(async function () {
                    for (let i in this.workOrderDetailList) {
                        console.log(this.$refs.workOrderDetailPlugin[i]);
                        this.$refs.workOrderDetailPlugin[i].initPlugin(
                            ...this.initArgs,
                            this.workOrderDetailList[i].workOrderId
                        );
                        eventHelper.emit(
                            "initWorkOrderRepDetail" +
                            this.workOrderDetailList[i].workOrderId,
                            this.workOrderDetailList[i]
                        );
                    }
                });
            }.bind(this)
        );
        eventHelper.on(
            "data-check-draw",
            function () {
                this.$refs.dataCheckDraw.init();
            }.bind(this)
        );
        eventHelper.on("open-jiandaoyun-single-login", () => {
            window.open(
                "https://www.jiandaoyun.com/sso/custom/ding1ee049cec4d560de24f2f5cc6abecb85/iss"
            );
        });
        eventHelper.on("jiandaoyun", (url) => {
            let jdyUrl = window.open(
                "https://www.jiandaoyun.com/sso/custom/ding1ee049cec4d560de24f2f5cc6abecb85/iss"
            );
            setTimeout(() => {
                if (!jdyUrl.closed) {
                    jdyUrl.close();
                }
                window.open(url);
            }, 500);
        });
        //通用属性表地图定位
        eventHelper.on(
            "property-sheet-open",
            function (param) {
                var style = {
                    color: [255, 0, 0],
                    size: 8,
                    outline: {
                        // autocasts as new SimpleLineSymbol()
                        width: 0.5,
                        color: [255, 0, 0],
                    },
                };
                switch (param.type) {
                    case "POINT":
                        this.clearGraLayer();
                        var points = mapHelper.readWKT(param.wkt);
                        var icon = {
                            url: "./img/icon/label/flicker.gif",
                            width: "80px",
                            height: "80px",
                            // xoffset: 1
                            // yoffset: 1
                        };
                        mapHelper.createPictureMarkSymbol(
                            this.graLayer,
                            points[0],
                            points[1],
                            icon
                        );
                        mapHelper.setCenter(this.baseView, points[0], points[1], 17);
                        break;
                    case "POLYGON":
                        this.addMapSurface(param.wkt);
                        break;
                    case "LINE":
                        this.clearGraLayer();
                        this.addMapLine(param.wkt);
                        break;
                }
            }.bind(this)
        );
    },
    components: {
        // 'flow-analyze': flowAnalyze,
        "water-mark": waterMark,
        //'info-window': infoWindow,
        "pipe-handover": pipeHandover,
        "pipe-road-edit": pipeRoadEdit,
        "mark-point-list": markPointList,
        "sewage-water-thematic-map": sewageWaterThematicMap,
        "edit-pipe-info": editPipeInfo,
        "task-check": taskCheck,
        "maintain-detail": maintainDetail,
        "retrospect-detail": retrospectDetail,
        "risk-point-panel": appRiskPoint,
        "risk-point-statistics": appRiskPointStatistic,
        "drainage-list": drainageList,
        "pollution-analysis": pollutionAnalysis,
        "mix-connect-point": mixConnectPoint,
        "pipe-check": pipeCheck,
        //    "info-window-custom": infoWindowCustom,
        "sy-analysis": syAnalysis,
        "traceability-analysis": traceabilityAnalysis,
        "traceability-dialog": traceabilityDialog,
        "analysis-dialog": analysisDialog,
        "status-tools": statusTools,
        "region-cut-form": regionCutForm,
        "analysis-block-cut-form": analysisBlockCutForm,
        "legend-view": legendView,
        "right-panel-control": rightPanelCtrl,
        "user-info-update": userInfoUpdate,
        "update-password": updatePassword,
        "service-attribute": serviceAttribute,
        "legend-control": legendControl,
        "system-selector": systemSelector,
        "unit-analyze": unitAnalyze,
        "unit-statistics": unitStatistics,
        "concern-manage": concernManage,
        "execute-log": executeLog,
        "region-info-edit": regionInfoEdit,
        "data-check-work-order": dataCheckWorkOrder,
        "drainage-mapList": drainageMapList,
        "drainage-construct-list": drainageConstructList,
        "user-monitor": userMonitor,
        "grid-defined": gridDefined,
        "new-plan": newPlan,
        "plan-account": planAccount,
        "car-monitor": carMonitor,
        "add-analysis": addAnalysis,
        "thematic-map": thematicMap,
        "car-monitor": carMonitor,
        "car-patrol-track": carPatrolTrack,
        "patrol-track-plugin": patrolTrackPlugin,
        "problem-report-map-list": problemReportMapList,
        "problem-report-map-list-show": problemReportMapListShow,
        "plan-clear-hyd-map-list": planClearHydMapList,
        "outlet-map-list": outletMapList,
        "river-map-list": riverMapList,
        "river-outlet-map-list": riverOutletMapList,
        "river-outlet-map-list-pic": riverOutletMapListPic,
        "edit-trace-tool": editTraceTool,
        "trace-main-pipe": traceMainPipe,
        "entrust-user-pic": entrustUserPic,
        "river-map-list2": riverMapList2,
        "add-deploy-point": addDeployPoint,
        "start-protection": startProtection,
        "start-protection-pic": startProtectionPic,
        "car-patrol-track-emer": carPatrolTrackEmer,
        "yxt-pipe-add": yxtPipeAdd,
        "yxt-pipe-connect": yxtPipeConnect,
        "yxt-pipe-connect-sewage": yxtPipeConnectSewage,
        "yxt-pipe-delete": yxtPipeDelete,
        "yxt-pipe-recycle-bin": yxtPipeRecycleBin,
        "yxt-pipe-edit": yxtPipeEdit,
        "yxt-pipe-connect-slice": yxtPipeConnectSlice,
        "play-handler-plugin": playHandlerPlugin,
        "work-order-detail-plugin": workOrderDetailPlugin,
        "data-check-draw": dataCheckDraw,
        "map-add-edit": mapAddEdit,
        "pump-inspection-plan-draw-line": pumpInspectionPlanDrawLine,
        "pump-inspection-plan-draw-line-back": pumpInspectionPlanDrawLineBack,
        "app-risk-point-manager": appRiskPointManager,
        "drainage-map-list-new": drainageMapListNew,
        "drainage-construct-list-new": drainageConstructListNew,
        "app-risk-point-manager": appRiskPointManager,
        deviceList: deviceList,
        "device-panel": devicePanel,
        "device-detail": deviceDetail,
        "general-layer": generalLayer,
        "map-debug": mapDebug,
        "weather": weather,
        "risk-one-map":riskOneMap,
        "clear-one-map":clearOneMap,
        "all-detail": allDetail,
    },
});
module.exports = comm;

function formatLayer(layerId) {
    let aliasName =
        layerId === "PS_PIPE_ZY"
            ? "排水管道"
            : layerId === "PS_CANAL_ZY"
                ? "沟渠"
                : layerId === "PS_WELL_ZY"
                    ? "窨井"
                    : layerId === "PS_COMB_ZY"
                        ? "雨水口"
                        : layerId === "PS_SPOUT_ZY"
                            ? "排放口"
                            : "管线点";
    return aliasName;
}
