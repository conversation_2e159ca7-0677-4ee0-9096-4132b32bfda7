<div class="riskPointMain">
    <div class="header">
        <div slot="header" class="userMonitorTitle">
            <span>{{ title }}</span>
        </div>
        <i v-if="mode==='riskCount'" class="el-icon-close" @click="close"></i>
        <i v-if="mode === 'riskList'" class="el-icon-back" @click="goBack"></i>
        <i v-if="mode === 'riskDetail'" class="el-icon-back" @click="goBack"></i>
    </div>
    <div class="content">
        <div class="topBox">
            <el-popover placement="left" width="250" ref="popover" trigger="click">
                <form class="selectForm">
                    <!--<div class="form-group"  v-show="detailShow">
                        <label>隐患点编号</label><br>
                        <el-input v-model="toolbarQueryParam.riskFacilityId" placeholder="请输入查询的编号"></el-input>
                    </div>-->
                    <!--
                    <div class="form-group" v-if="mode === 'riskDetail'">
                        <label>行政区</label><br>
                        <select class="form-control" v-model="toolbarQueryParam.distinct">
                            <option v-for="option in listDistinct"
                                    :value="option.value">
                                {{ option.label }}
                            </option>
                        </select>
                    </div>
                    -->
                    <!--
                    <div class="form-group" v-if="mode !== 'riskDetail'">
                        <label>分公司</label><br>
                        <select class="form-control" v-model="toolbarQueryParam.FGS">
                            <option
                                    v-for="option in listCompany"
                                    :value="option.value">
                                {{ option.fgs }}
                            </option>
                        </select>
                    </div>
                    -->
                    <div class="form-group" v-if="mode !== 'riskDetail'">
                        <label>镇街</label><br>
                        <el-cascader
                                show-all-levels
                                clearable
                                collapse-tags
                                :props="{ multiple:true,emitPath: false }"
                                v-model="toolbarQueryParam.district"
                                :options="townStreetList"
                                style="width: 100%;"
                        ></el-cascader>
                        <!--
                        <select class="form-control" v-model="toolbarQueryParam.district">
                            <option v-for="option in roadList"
                                    :value="option.value">
                                {{ option.label }}
                            </option>
                        </select>
                        -->
                    </div>
                    <div class="form-group" v-if="mode !== 'riskDetail'">
                        <label>处理情况</label><br>
                        <select class="form-control" v-show="mode !== 'riskDetail'"
                                v-model='toolbarQueryParam.handleState'>
                            <option value="">全部</option>
                            <option value="未处理">未处理</option>
                            <option value="进行中">进行中</option>
                            <option value="已完成">已完成</option>
                        </select>
                    </div>
                    <div class="form-group" v-if="mode === 'riskDetail'">
                        <label>处理情况</label><br>
                        <select class="form-control" v-model='toolbarQueryParam.fixState'>
                            <option value="">全部</option>
                            <option value="未处理">未处理</option>
                            <option value="进行中">进行中</option>
                            <option value="已完成">已完成</option>
                        </select>
                    </div>
                    <div class="form-group" v-if="mode !== 'riskDetail'">
                        <label>项目类别</label><br>
                        <select class="form-control" v-model='toolbarQueryParam.category'>
                            <option v-for="item in dirtyDefinitionExpression" :value="item.value">{{ item.label }}
                            </option>
                        </select>
                    </div>
                    <div class="form-group" v-if="mode !== 'riskDetail'">
                        <label>检测方法</label><br>
                        <el-select v-model="toolbarQueryParam.checkMethod" placeholder="请选择">
                            <el-option value="" label="全部"></el-option>
                            <el-option value="QV检测" label="QV检测"></el-option>
                            <el-option value="CCTV检测" label="CCTV检测"></el-option>
                            <el-option value="声纳检测" label="声纳检测"></el-option>
                            <!-- <el-option value="两栖机器人" label="两栖机器人"></el-option> -->
                            <el-option value="空洞检测" label="空洞检测"></el-option>
                            <el-option value="其他" label="其他"></el-option>
                        </el-select>
                    </div>
                    <div class="form-group" v-if="mode !== 'riskDetail'">
                        <label>管网类别</label><br>
                        <select class="form-control" v-model='toolbarQueryParam.sort'>
                            <option v-for="item in pipeType" :value="item">{{ item}}
                            </option>
                        </select>
                    </div>
                    <div class="form-group" v-if="mode !== 'riskDetail'">
                        <label>是否委外</label><br/>
                        <el-select v-model="toolbarQueryParam.isOutsoutced" multiple placeholder="请选择" >
                           <el-option value="" label="全部"></el-option>
                           <el-option value="自主" label="自主"></el-option>
                           <el-option value="委外(正源)" label="委外(正源)"></el-option>
                           <el-option value="委外(其他单位)" label="委外(其他单位)"></el-option>
                        </el-select>
                    </div>
                    <div class="form-group" v-if="mode !== 'riskDetail'">
                        <label>是否有备注</label><br/>
                        <el-radio-group v-model="toolbarQueryParam.isRemark" size="small">
                            <el-radio-button :label="''">全部</el-radio-button>
                            <el-radio-button :label="'true'">是</el-radio-button>
                            <el-radio-button :label="'false'">否</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div class="form-group" v-if="mode !== 'riskDetail' && mode !== 'riskList'">
                        <label>是否有隐患点</label><br/>
                        <el-radio-group v-model="toolbarQueryParam.isRiskPoint" size="small">
                            <el-radio-button :label="''">全部</el-radio-button>
                            <el-radio-button :label="'true'">是</el-radio-button>
                            <el-radio-button :label="'false'">否</el-radio-button>
                        </el-radio-group>
                    </div>

                    <div class="form-group" v-if="mode !== 'riskDetail'">
                        <label>检测时间</label><br>
                        <el-date-picker style="width: 225px" value-format="yyyy-MM-dd" v-model="toolbarQueryParam.date"
                                        type="daterange"
                                        range-separator="~" start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </div>
                    <div class="form-group" v-if="mode !== 'riskDetail'">
                        <label>修复时间</label><br>
                        <el-date-picker style="width: 225px" value-format="yyyy-MM-dd"
                                        v-model="toolbarQueryParam.fixDate" type="daterange"
                                        range-separator="~" start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </div>
                    <div class="form-group" v-if="mode === 'riskDetail'">
                        <label>缺陷类型</label><br>
                        <select class="form-control" v-model="toolbarQueryParam.flawName">
                            <option v-for="option in listFlawName"
                                    :value="option.value">
                                {{ option.text }}
                            </option>
                        </select>
                    </div>
                    <div class="form-group" v-if="mode === 'riskDetail'">
                        <label>缺陷等级</label><br>
                        <select class="form-control" v-model="toolbarQueryParam.grade">
                            <option value="">全部</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                        </select>
                    </div>
                </form>
                <form class="form-inline pull-right">
                    <!--清空查询条件-->
                    <button type="button"
                            style="width: 70px;height: 24px;background-color: #006FD1B3;color: #fff;border: 1px solid #298EE8;"
                            class="selectBtn"
                            @click="initToolbarQueryParam">
                        清空
                    </button>
                    <!--查询按钮-->
                    <button type="button"
                            style="width: 70px;height: 24px;background-color: #006FD1B3;color: #fff;border: 1px solid #298EE8;"
                            class="reserBtn"
                            @click="search">
                        查询
                    </button>
                </form>
                <el-button style="margin:10px 0" class="selectBtn" type="text" slot="append"
                           slot="reference" icon="el-icon-search">查询
                </el-button>
            </el-popover>
            <el-button type="primary" class="leftBtn" icon="el-icon-plus" @click="addFacilitiey">新增</el-button>
            <el-button type="primary" class="outputBtn" :icon="outputBtnIcon" @click="output(0)">{{ outputBtnText }}
            </el-button>
            <el-button type="primary" class="outputBtnNoImg" :icon="outputBtnNoImgIcon" @click="outputNoImg">{{ outputBtnNoImgText }}</el-button>

        </div>
        <!-- 主表格 -->
        <div class="tableContainer" v-if="mode === 'riskCount'">
            <el-table
                    v-loading="mainLoading"
                    :data="tableData"
                    :header-cell-style="{background: '#F2F2F2',height: '50px'}"
                    :cell-style="{textAlight:'center'}"
                    height="450px"
                    @row-click="rowMainClick"
                    :row-style="{height: '50px'}"
                    style="width: 100%;">
                <el-table-column
                        align="center"
                        width="45"
                        type="index"
                        :index="indexMethod"
                        label="序号"
                >
                </el-table-column>
                <el-table-column
                        align="center"
                        label="设施编号">
                    <template slot-scope="scope">
                        {{ scope.row.work_id ? scope.row.work_id : scope.row.facilityUsid }}
                    </template>
                </el-table-column>
                <!--
                <el-table-column
                        align="center"
                        label="检测状态"
                        width="70"
                >
                    <template slot-scope="scope">
                        <div class="statusTD" :style="{color:scope.row.fixState === '未处理'?'#FF0000':scope.row.fixState === '进行中'?'orange':''}">
                            {{scope.row.fixState}}
                        </div>
                    </template>
                </el-table-column>
                -->
                <el-table-column
                        align="center"
                        prop="district"
                        label="地址"
                >
                </el-table-column>
                <el-table-column
                        align="center"
                        width="100"
                        label="检测次数">
                    <template slot-scope="scope">
                        <div class="tdBox tdBoxCenter">
                            <div class="ingBox" @click="viewRiskList(scope.row)">{{ scope.row.checkCount }}</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        width="100"
                        label="缺陷点">
                    <template slot-scope="scope">
                        <div class="tdBox">
                            <div class="errorBox noCursor">{{ scope.row.undone }}</div>
                            <div class="ingBox noCursor">{{ scope.row.inProgress }}</div>
                            <div class="finishBox noCursor">{{ scope.row.done }}</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                        width="55"
                        align="center"
                        label="操作"
                >
                    <template slot-scope="scope">
                        <div class="tdImgBox" @click="viewRiskList(scope.row)">
                            <img src="../../../../../img/pipeProblemSpecial/tdView.png" alt="">
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 主表格的分页 -->
        <div class="footer" v-if="mode === 'riskCount'">
            <el-pagination
                    background
                    :current-page="page.pageNumber"
                    @current-change="pageChange"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="page.pageSize"
                    layout="prev, pager, next,total,jumper"
                    :total="page.totalRecord">
            </el-pagination>
        </div>
        <!-- 检测表格 -->
        <div class="tableContainer" v-if="mode === 'riskList'">
            <el-table
                    v-loading="subMainLoading"
                    :data="subTableData"
                    :header-cell-style="{background: '#F2F2F2',height: '50px'}"
                    :cell-style="{textAlight:'center'}"
                    height="450px"
                    @row-click="rowMainClick"
                    :row-style="{height: '50px'}"
                    style="width: 100%;">
                <el-table-column
                        align="center"
                        width="45"
                        type="index"
                        :index="indexMethod"
                        label="序号"
                >
                </el-table-column>
                <el-table-column
                        align="center"
                        label="设施编号"
                        width="140"
                >
                    <template slot-scope="scope">
                        {{ scope.row.work_id ? scope.row.work_id : scope.row.facilityUsid }}
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        prop="checkDate"
                        width="100"
                        label="检测日期"
                >
                    <template slot-scope="scope">
                        {{ scope.row.checkDate}}
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        label="检测状态"
                        width="70"
                >
                    <template slot-scope="scope">
                        <div class="statusTD"
                             :style="{color:scope.row.fixState === '未处理'?'#FF0000':scope.row.fixState === '进行中'?'orange':''}">
                            {{ scope.row.fixState }}
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                        align="center"
                        prop="district"
                        label="地址"
                >
                </el-table-column>
                <el-table-column
                        align="center"
                        width="100"
                        label="缺陷点">
                    <template slot-scope="scope">
                        <div class="tdBox">
                            <div class="errorBox" @click="toDetail(scope.row,'未处理')">{{ scope.row.undone }}</div>
                            <div class="ingBox" @click="toDetail(scope.row,'进行中')">{{ scope.row.inProgress }}</div>
                            <div class="finishBox" @click="toDetail(scope.row,'已完成')">{{ scope.row.done }}</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                        width="105"
                        align="center"
                        label="操作"
                >
                    <template slot-scope="scope">
<!--                        <el-button icon="el-icon-tickets" size="mini" circle
                                                            @click.stop="createWorkOrder(scope.row)"></el-button>-->
                        <div class="tdImgBox" @click="editFalicy(scope.row)">
                            <img src="../../../../../img/pipeProblemSpecial/tdEdit.png" alt="">
                        </div>
                        <el-button icon="el-icon-delete" circle size="mini"
                                   @click.stop="deleteFalicty(scope.row)"></el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 检测表格的分页 -->
        <div class="footer" v-if="mode === 'riskList'">
            <el-pagination
                    background
                    :current-page="subPage.pageNumber"
                    @current-change="subPageChange"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="subPage.pageSize"
                    layout="prev, pager, next,total,jumper"
                    :total="subPage.totalRecord">
            </el-pagination>
        </div>

        <!--详情表格-->
        <div class="detailRisk" v-if="mode === 'riskDetail'">
            <el-form class="formBox" label-position="right">
                <el-form-item class="column" label="检测方法">
                    <span>{{currentRow.checkMethod }}</span>
                </el-form-item>
                <el-form-item class="column" label="检测时间">
                    <span>{{ currentRow.checkDate ? currentRow.checkDate.substring(0, 10) : ''}}</span>
                </el-form-item>
                <el-form-item class="column" label="检测单位">
                    <span>{{ currentRow.checkUnit }}</span>
                </el-form-item>
                <el-form-item class="column" label="填报人">
                    <span>{{ currentRow.submitStaff }}</span>
                </el-form-item>
            </el-form>
        </div>
        <div class="tableContainer" v-if="mode === 'riskDetail'">
            <el-table
                    :data="detailTable"
                    :v-loading="detailLoading"
                    :header-cell-style="{background: '#F2F2F2',height: '50px'}"
                    :cell-style="{textAlight:'center'}"
                    height="450px"
                    :row-style="{height: '50px'}"
                    style="width: 100%;">
                <el-table-column
                        align="center"
                        width="45"
                        type="index"
                        label="序号"
                >
                </el-table-column>
                <el-table-column
                        align="center"
                        prop="yhdType"
                        label="类型">
                </el-table-column>
                <el-table-column
                        align="center"
                        prop="flawName"
                        label="缺陷名称">
                </el-table-column>
                <el-table-column
                        align="center"
                        prop="flawLevel"
                        label="等级"
                >
                </el-table-column>
                <el-table-column
                        align="center"
                        label="状态"
                >
                    <template slot-scope="scope">
                        <div class="statusTD"
                             :style="{color:scope.row.handleState === '未处理'?'#FF0000':scope.row.handleState === '进行中'?'orange':''}">
                            {{ scope.row.handleState }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        prop="district"
                        label="地址">
                </el-table-column>
                <el-table-column
                        width="85"
                        align="center"
                        label="缺陷图片">
                    <template slot-scope="scope">
                        <el-popover placement="top-start" title="" trigger="hover">
                            <img :src="scope.row.imgWithBase64" alt="" style="width: 150px;height: 150px">
                            <img slot="reference" :src="scope.row.imgWithBase64" style="width: 60px;height: 60px">
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column
                        width="105"
                        align="center"
                >
                    <template slot-scope="scope">
                         <div class="tdImgBox" @click="editRiskPoint(scope.row)">
                            <img src="../../../../../img/pipeProblemSpecial/tdEdit.png" alt="">
                        </div>
                        <el-button icon="el-icon-delete" circle size="mini"
                                   @click.stop="deletePoint(scope.row)"></el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</div>

