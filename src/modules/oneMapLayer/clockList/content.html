<div class="clearDetail">
    <div class="header">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a @click="back()">返回</a></el-breadcrumb-item>
            <el-breadcrumb-item>巡检列表</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="title">
            <span>巡检列表</span>
        </div>
    </div>

    <div class="mainContent">
        <el-divider content-position="left">搜索条件</el-divider>
        <div class="search-group">
            <el-row :gutter="20">
                <el-col :span="24">
                    <label>任务类别</label> <br/>
                    <el-select size="mini" v-model="detailQueryParam.clockType" clearable
                               @change="detailClockTypeChange($event)">
                        <el-option value="" label="全部"></el-option>
                        <el-option value="计划巡检" label="计划巡检"></el-option>
                        <el-option value="维修上报" label="维修上报"></el-option>
                        <el-option value="清疏上报" label="清疏上报"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="24">
                    <label>打卡日期</label> <br/>
                    <el-date-picker style="width:100%;" size="mini" v-model="detailQueryParam.clockTime"
                                    @change="detailClockTimeChange($event)"
                                    type="daterange" range-separator="至" value-format="yyyy-MM-dd"
                                    start-placeholder="开始日期" end-placeholder="结束日期">
                    </el-date-picker>
                </el-col>
            </el-row>
        </div>
        <el-divider content-position="left">巡检信息</el-divider>
        <template v-for="(item,index) in detailInfo.clockList">
            <el-card class="box-card">
                <el-row style="height: 100%;">
                    <el-col :span="8" class="img">
                        <img v-show="!((item.uploadFiles[0] || {}).id)" src="../../../img/pipeProblemSpecial/wlr.jpg" alt="">
                        <img v-show="!((item.uploadFiles[0] || {}).id)" :src="getPic((item.uploadFiles[0] || {}).id)"  @click.stop="showImgPic([getPic((item.uploadFiles[0] || {}).id)],0)"  alt="">
                    </el-col>
                    <el-col :span="16" style="height: 100%;">
                        <!--
                        <div class="item-detail">
                            <el-button type="text" @click="viewDetail(item)">详情</el-button>
                        </div>
                        -->
                        <el-row :gutter="10" class="card-detail">
                            <el-col :span="24">
                                <label>{{ item.address }}</label>
                            </el-col>
                            <el-col :span="24">
                                <label>打卡类型</label>
                                {{ item.clockType }}
                            </el-col>
                            <el-col :span="24">
                                <label>操作类型</label>
                                {{ item.orderOperateType }}
                            </el-col>
                            <el-col :span="24">
                                <label>打卡时间</label>
                                {{ item.clockTime }}
                            </el-col>
                            <el-col :span="24">
                                <label>打卡人</label>
                                {{ item.clockUserName }}
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
            </el-card>
        </template>
    </div>
</div>