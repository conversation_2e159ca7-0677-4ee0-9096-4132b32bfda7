<div class="clearDetail">
    <div class="header">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a @click="back()">返回</a></el-breadcrumb-item>
            <el-breadcrumb-item>维修列表</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="title">
            <span>维修列表</span>
        </div>
    </div>

    <div class="mainContent">
        <el-divider content-position="left">搜索条件</el-divider>
        <div class="search-group">
            <el-row :gutter="20">
                <el-col :span="24">
                    <label>任务类别</label> <br/>
                    <el-select size="mini" v-model="detailQueryParam.taskClassify" clearable
                               @change="detailTaskClassifyChange($event)">
                        <el-option value="" label="全部"></el-option>
                        <el-option value="计划性清疏" label="计划性清疏"></el-option>
                        <el-option value="专项清疏" label="专项清疏"></el-option>
                        <el-option value="应急清疏" label="应急清疏"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="24">
                    <label>上报日期</label> <br/>
                    <el-date-picker style="width:100%;" size="mini" v-model="detailQueryParam.reportDate"
                                    @change="detailReportDateChange($event)"
                                    type="daterange" range-separator="至" value-format="yyyy-MM-dd"
                                    start-placeholder="开始日期" end-placeholder="结束日期">
                    </el-date-picker>
                </el-col>
                <el-col :span="24">
                    <label>处理日期</label> <br/>
                    <el-date-picker style="width:100%;" size="mini" v-model="detailQueryParam.handleDate"
                                    @change="detailHandleDateChange($event)"
                                    type="daterange" range-separator="至" value-format="yyyy-MM-dd"
                                    start-placeholder="开始日期" end-placeholder="结束日期">
                    </el-date-picker>
                </el-col>
            </el-row>
        </div>
        <el-divider content-position="left">维修信息</el-divider>
        <template v-for="(item,index) in detailInfo.engmaiList">
            <el-card class="box-card">
                <el-row style="height: 100%;">
                    <el-col :span="8" class="img">
                        <img v-show="!((item.uploadFiles[0] || {}).id)" src="../../../img/pipeProblemSpecial/wlr.jpg" alt="">
                        <img v-show="((item.uploadFiles[0] || {}).id)"  :src="getPic((item.uploadFiles[0] || {}).id)" @click.stop="showImgPic([getPic((item.uploadFiles[0] || {}).id)],0)"  alt="">
                    </el-col>
                    <el-col :span="16" style="height: 100%;">
                        <div class="item-detail">
                            <el-button type="text" @click="viewDetail(item)">详情</el-button>
                        </div>
                        <el-row :gutter="10" class="card-detail">
                            <el-col :span="24">
                                <label>{{ item.workOrderNo }}</label>
                            </el-col>
                            <el-col :span="24">
                                <label>任务类型</label>
                                {{ item.engMaiType }}
                            </el-col>
                            <el-col :span="24">
                                <label>问题类型</label>
                                {{ item.facilityProblem }}
                            </el-col>
                            <el-col :span="24">
                                <label>上报时间</label>
                                {{ item.reportDate }}
                            </el-col>
                            <el-col :span="24">
                                <label>处理时间</label>
                                {{ item.handleDate }}
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
            </el-card>
        </template>
    </div>
</div>