<div>
    <div class="risk-one-map" :style="panelCollapse?'width:34px;':'width:660px;'"
         v-show="riskOneMapShow">
        <!-- 竖向排列按钮组 -->
        <div class="button-group">
            <!--
            <el-button
                    :type="activeName === 'search' ? 'primary' : ''"
                    :class="{ 'vertical-btn-active': 'search' === activeName }"
                    @click="handleButtonClick('search')"
                    class="vertical-btn">
                搜索
            </el-button>
            <el-button
                    :type="activeName === 'list' ? 'primary' : ''"
                    :class="{ 'vertical-btn-active': 'list' === activeName }"
                    @click="handleButtonClick('list')"
                    class="vertical-btn">
                列表
            </el-button>
            -->
            <!--
            <el-button
                    :type="activeName === 'detail' ? 'primary' : ''"
                    :class="{ 'vertical-btn-active': 'detail' === activeName }"
                    @click="handleButtonClick('detail')"
                    class="vertical-btn">
                详情
            </el-button>
            -->
            <el-button
                    type="default"
                    @click="toggleLeft()"
                    :icon="panelCollapse?'el-icon-d-arrow-left':'el-icon-d-arrow-right'"
                    class="vertical-btn"
                    style="height: 75px;"
            >
                收缩
            </el-button>

            <div class="layer-legend">
                <div>图例</div>
                <div>
                    <div class="legend-label">检测次数</div>
                    <div class="legend-label">管道颜色</div>
                </div>
                <div>
                    <div class="legend-label">1次</div>
                    <div class="legend-color low"></div>
                </div>
                <div>
                    <div class="legend-label">2-3次</div>
                    <div class="legend-color medium"></div>
                </div>
                <div>
                    <div class="legend-label">4-5次</div>
                    <div class="legend-color high"></div>
                </div>
                <div>
                    <div class="legend-label">5+次</div>
                    <div class="legend-color critical"></div>
                </div>
                <div class="defect-container">
                    <div class="legend-label">缺陷数量</div>
                    <div class="defect-icon">
                        <img src="../../../img/oneMapLayer/risk.svg">
                    </div>
                    <!--
                    <div class="defect-indicator"></div>
                    -->
                </div>
            </div>
        </div>
        <div class="detail-group" v-show="!panelCollapse">
            <div class="detail-header">
                <div class="title">
                    检测数据
                    <span class="title-close">
                        <i class="el-icon-close" @click="close"></i>
                    </span>
                </div>
                <div class="detail-button" v-if="activeName === 'search'">
                    <el-button type="text" size="mini" @click="handleButtonClick('list')">返回检测列表</el-button>
                    <!--
                    <el-button type="primary" size="mini" @click="search">
                        查询
                    </el-button>
                    <el-button type="default" size="mini" @click="initToolbarQueryParam">
                        清空
                    </el-button>
                    -->
                </div>
                <div class="detail-button" v-if="activeName === 'list'">
                    <el-button type="text" size="mini" @click="handleButtonClick('search')">返回查询条件</el-button>
                    <!--
                    <el-button type="default" size="mini">导出</el-button>
                    <el-button type="default" size="mini">导出(不带图片)</el-button>
                    -->
                    <div class="view-date">
                        <span class="view-date-title">时间范围:</span>
                        (
                        <span class="view-date-detail"
                              v-if="toolbarQueryParam.checkDate && toolbarQueryParam.checkDate.length > 1">
                            {{ toolbarQueryParam.checkDate[0] }}-{{ toolbarQueryParam.checkDate[1] }}
                        </span>
                        )
                    </div>
                </div>
                <div class="detail-button" v-if="activeName === 'list'">
                    <el-button type="primary" v-loading="exportLoading" icon="el-icon-download"
                               style="float: right;margin-right: 20px;"
                               size="mini"
                               @click="exportMoreToExcelNoImg">导出(不含图片)
                    </el-button>
                    <el-button type="primary" v-loading="exportLoading" icon="el-icon-download"
                               style="float: right;margin-right: 20px;"
                               size="mini"
                               @click="exportMoreToExcel">导出
                    </el-button>
                </div>
                <div class="detail-button">
                    <span class="code-title">设施编号：</span>
                    <span class="code-input">
                       <el-input size="mini" v-model="toolbarQueryParam.workIdLike" clearable
                                 @input="onWorkIdLikeChange($event)"></el-input>
                   </span>
                </div>
            </div>
            <div class="search-panel" v-if="activeName === 'search'">
                <risk-layer-search :toolbarQueryParam="toolbarQueryParam" @search="search"
                                   @resetSearch="initToolbarQueryParam"></risk-layer-search>
            </div>
            <div class="detail-panel" v-if="activeName === 'list'">
                <div class="tableContainer">
                    <el-table
                            v-loading="mainLoading"
                            :data="tableData"
                            :header-cell-style="{background: '#F2F2F2',height: '50px'}"
                            :cell-style="{textAlight:'center'}"
                            @row-click="rowMainClick"
                            :row-style="{height: '50px'}"
                            height="600px"
                            style="width: 100%;">
                        <el-table-column
                                align="center"
                                width="45"
                                type="index"
                                :index="indexMethod"
                                label="序号"
                        >
                        </el-table-column>
                        <el-table-column
                                align="center"
                                label="设施编号">
                            <template slot-scope="scope">
                                {{ scope.row.workId ? scope.row.workId : scope.row.facilityUsid }}
                            </template>
                        </el-table-column>
                        <el-table-column
                                align="center"
                                prop="pipeType"
                                label="项目类属"
                        >
                        </el-table-column>
                        <el-table-column
                                align="center"
                                prop="district"
                                label="镇街"
                        >
                        </el-table-column>
                        <el-table-column
                                align="center"
                                width="70"
                                label="检测次数">
                            <template slot-scope="scope">
                                <div class="tdBox tdBoxCenter">
                                    <div class="custom-box" :class="getBoxClass(scope.row)"
                                         @click="viewRiskList(scope.row)">{{
                                            scope.row.checkCount
                                        }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                                align="center"
                                width="70"
                                label="缺陷数量">
                            <template slot-scope="scope">
                                <div class="tdBox tdBoxCenter">
                                    <div class="custom-box noCursor flaw">{{ scope.row.checkPointCount }}</div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                                align="center"
                                width="80"
                                label="已处理缺陷">
                            <template slot-scope="scope">
                                <div class="tdBox tdBoxCenter">
                                    <div class="finishBox">{{ scope.row.done }}</div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                                width="55"
                                align="center"
                                label="详情"
                        >
                            <template slot-scope="scope">
                                <div class="tdImgBox" @click="viewRiskList(scope.row)">
                                    <img src="../../../img/pipeProblemSpecial/tdView.png" alt="">
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <!-- 主表格的分页 -->
                <div class="footer">
                    <el-pagination
                            background
                            :current-page="page.pageNumber"
                            @current-change="pageChange"
                            :page-sizes="[5, 10, 20, 30]"
                            :page-size="page.pageSize"
                            layout="prev, pager, next,total,jumper"
                            :total="page.totalRecord">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
</div>
