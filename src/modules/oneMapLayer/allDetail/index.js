var template = require("./content.html");
var mathUtils = require('utils/mathUtils');
var eventHelper = require("utils/eventHelper");
var riskList = require("../riskList");
var riskDetail = require("../riskDetail");
var riskPointDetail = require("../riskPointDetail");
var facilityDetail = require("../facilityDetail");
var clearList = require("../clearList");
var engmaiList = require("../engmaiList");
var clearDetail = require("../clearDetail");
var engmaiDetail = require("../engmaiDetail");
var clockList = require("../clockList");
var problemList = require("../problemList");
var problemDetail = require("../problemDetail");

var comm = Vue.extend({
    template: template,
    data: function () {
        return {
            allDetailShow: false,
            panelCollapse: false,
            activeName: '设施',
            mode: '',
            historyData: {},
            backCallback: null,
            queryFacilityData: {
                objectId: '',
                layerId: '',
                usid: '',
                cuuid: '',
                workId: ''
            },
            picVisible: false,
            initialIndex: 0,
            carouselPhotos: [],
        };
    },
    created: function () {
    },
    mounted: function () {
    },
    activated: function () {
    },
    methods: {
        open(activeName, mode, data, backCallback) {
            // eventHelper.emit('change-menu', {title: '主地图', funUrl: 'arcgis-plugin'});
            this.activeName = activeName;
            this.mode = mode;
            this.allDetailShow = true;
            if (backCallback) {
                this.backCallback = backCallback;
                // 第一次从外部进来则清空缓存
                this.historyData = {};
            }
            setTimeout(() => {
                if (this.activeName === '设施') {
                    this.$refs.facilityDetailRef.open(data);
                } else if (this.activeName === '检测') {
                    if (mode === 'riskList') {
                        this.queryFacilityData.workId = data.workId;
                        this.queryFacilityData.objectId = data.objectId;
                        this.queryFacilityData.layerId = data.layerId;
                        this.queryFacilityData.usid = data.usid;
                        this.queryFacilityData.cuuid = data.cuuid
                        this.$refs.riskListRef.open(data);
                        this.historyData[activeName] = {};
                        this.historyData[activeName]['riskList'] = data;
                    } else if (mode === 'risk') {
                        if(!this.historyData[activeName]){
                           this.historyData[activeName] = {};
                        }
                        this.$refs.riskDetailRef.open(data);
                        this.historyData[activeName]['risk'] = data;
                    } else if (mode === 'riskPoint') {
                        this.$refs.riskPointDetailRef.open(data);
                        this.historyData[activeName]['riskPoint'] = data;
                    }
                } else if (this.activeName === '清疏') {
                    if (mode === 'clearList') {
                        this.queryFacilityData.workId = data.workId;
                        this.queryFacilityData.objectId = data.objectId;
                        this.queryFacilityData.layerId = data.layerId;
                        this.queryFacilityData.usid = data.usid;
                        this.queryFacilityData.cuuid = data.cuuid
                        this.$refs.clearListRef.open(data);
                        this.historyData[activeName] = {};
                        this.historyData[activeName]['clearList'] = data;
                    } else if (mode === 'clear') {
                        this.$refs.clearDetailRef.open(data);
                        if(!this.historyData[activeName]){
                           this.historyData[activeName] = {};
                        }
                        this.historyData[activeName]['clear'] = data;
                    }
                } else if (this.activeName === '维修') {
                    if (mode === 'engmaiList') {
                        this.queryFacilityData.workId = data.workId;
                        this.queryFacilityData.objectId = data.objectId;
                        this.queryFacilityData.layerId = data.layerId;
                        this.queryFacilityData.usid = data.usid;
                        this.queryFacilityData.cuuid = data.cuuid
                        this.$refs.engmaiListRef.open(data);
                        this.historyData[activeName] = {};
                        this.historyData[activeName]['engmaiList'] = data;
                    } else if (mode === 'engmai') {
                        this.$refs.engmaiDetailRef.open(data);
                        this.historyData[activeName]['engmai'] = data;
                    }
                } else if (this.activeName === '巡检') {
                    if (mode === 'clockList') {
                        this.queryFacilityData.workId = data.workId;
                        this.queryFacilityData.objectId = data.objectId;
                        this.queryFacilityData.layerId = data.layerId;
                        this.queryFacilityData.usid = data.usid;
                        this.queryFacilityData.cuuid = data.cuuid
                        this.$refs.clockListRef.open(data);
                        this.historyData[activeName] = {};
                        this.historyData[activeName]['clockList'] = data;
                    } else if (mode === 'clock') {
                        // this.$refs.clockDetailRef.open(data);
                        // this.historyData[activeName]['clock'] = data;
                    }
                } else if (this.activeName === '问题上报') {
                    if (mode === 'problemList') {
                        this.queryFacilityData.workId = data.workId;
                        this.queryFacilityData.objectId = data.objectId;
                        this.queryFacilityData.layerId = data.layerId;
                        this.queryFacilityData.usid = data.usid;
                        this.queryFacilityData.cuuid = data.cuuid
                        this.$refs.problemListRef.open(data);
                        this.historyData[activeName] = {};
                        this.historyData[activeName]['problemList'] = data;
                    } else if (mode === 'problem') {
                        this.$refs.problemDetailRef.open(data);
                        this.historyData[activeName]['problem'] = data;

                    }
                }
            });
        },
        close() {
            this.allDetailShow = false;
        },
        handleButtonClick(panelName) {
            // 切换面板状态
            this.activeName = this.activeName === panelName ? '' : panelName;
            setTimeout(() => {
                if (panelName === '设施') {
                    this.open(panelName, '', this.queryFacilityData);
                } else if (panelName === '检测') {
                    if (this.historyData[panelName]) {
                        this.open(panelName, 'riskList', this.historyData[panelName]['riskList']);
                    } else {
                        this.open(panelName, 'riskList', this.queryFacilityData);
                    }
                } else if (panelName === '清疏') {
                    if (this.historyData[panelName]) {
                        this.open(panelName, 'clearList', this.historyData[panelName]['clearList'] || this.queryFacilityData);
                    } else {
                        this.open(panelName, 'clearList', this.queryFacilityData);
                    }
                } else if (panelName === '维修') {
                    if (this.historyData[panelName]) {
                        this.open(panelName, 'engmaiList', this.historyData[panelName]['engmaiList']);
                    } else {
                        this.open(panelName, 'engmaiList', this.queryFacilityData);
                    }
                } else if (panelName === '巡检') {
                    if (this.historyData[panelName]) {
                        this.open(panelName, 'clockList', this.historyData[panelName]['clockList']);
                    } else {
                        this.open(panelName, 'clockList', this.queryFacilityData);
                    }
                } else if (panelName === '问题上报') {
                    if (this.historyData[panelName]) {
                        this.open(panelName, 'problemList', this.historyData[panelName]['problemList']);
                    } else {
                        this.open(panelName, 'problemList', this.queryFacilityData);
                    }
                }
            });

        },
        toggleLeft() {
            this.panelCollapse = !this.panelCollapse;
        },
        //自定义表格索引
        indexMethod(index) {
            return index + (this.page.pageNumber - 1) * this.page.pageSize + 1;
        },
        backCallbackHandle() {
            this.allDetailShow = false;
            if (this.backCallback) {
                this.backCallback()
            }
        },
        showImgPic(imgs,index) {
            this.carouselPhotos = imgs;
            this.initialIndex = index;
            this.picVisible = true;
        }
    },
    components: {
        'new-facility-detail': facilityDetail,
        'new-risk-list': riskList,
        'new-risk-detail': riskDetail,
        'new-risk-point-detail': riskPointDetail,
        'new-clear-list': clearList,
        'new-clear-detail': clearDetail,
        'new-engmai-list': engmaiList,
        'new-engmai-detail': engmaiDetail,
        'new-clock-list': clockList,
        'new-problem-list': problemList,
        'new-problem-detail': problemDetail,
    }
});
module.exports = comm;