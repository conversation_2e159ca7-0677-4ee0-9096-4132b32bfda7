<div class="configContent" v-loading="pageLoading">
    <div class="goBackWrapper">
        <el-button class="btn" size="medium" @click="addSubject">
            <img class="icon" :src="'./img/safeProduction/onlineStudy/add-white.png'" />
            <span>新增</span>
        </el-button>
    </div>
    <ul v-if="groupList.length" class="groupList">
        <li v-for="item in groupList" class="groupItem">
            <span class="labelItem" :class="contestStatusMap[item.contestStatus].color">{{contestStatusMap[item.contestStatus].text}}</span>
            <p class="title">{{item.contestName}}</p>
            <p class="time">开放时间：{{item.openTimeScope}}</p>
            <div class="btnWrapper">
                <div v-if="item.contestStatus !== 3" class="btnItem blue" @click="editGroup(item.id)">
                    <img :src="'./img/safeProduction/onlineExam/icon-edit.png'"/>
                    <span>编辑</span>
                </div>
                <div v-if="item.contestStatus === 1" class="line"></div>
                <div v-if="item.contestStatus !== 2" class="btnItem red" @click="deleteGroup(item.id)">
                    <img :src="'./img/safeProduction/icon-delete-red.png'"/>
                    <span>删除</span>
                </div>
            </div>
        </li>
    </ul>
    <p v-else class="noMoreTips">暂无数据</p>
    <div class="paginationWrapper">
        <el-pagination
                background
                layout="prev, pager, next"
                :current-page.sync="searchParam.pageNumber"
                :page-sizes="searchParam.pageSize"
                @current-change="bindPageChange"
                :total="totalRecord">
        </el-pagination>
    </div>
</div>
