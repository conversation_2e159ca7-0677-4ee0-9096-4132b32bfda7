<div class="pumpStation" v-loading="pageLoading" :element-loading-text="pageLoadingText">
    <div class="flexBox">
        <div class="flexBoxLeft">
            <declare-timeline type="primary" :infoId="row.infoId" infoType="2"></declare-timeline>
        </div>
        <div class="flexBoxRight">
            <el-form :model="form" :rules="formRules" ref="form" class="form"
                :class="dialogType == 'view' ? 'diyFormView' : ''" size="medium" :inline="false" label-width="170px"
                :disabled="dialogType == 'view'">
                <el-row :gutter="20">
                    <el-form-item label="泵站名称" prop="pumpName">
                        <el-input v-model="form.pumpName"></el-input>
                    </el-form-item>
                    <el-form-item label="泵站地址" prop="pumpAddress">
                        <el-input type="textarea" v-model="form.pumpAddress" :rows="3"></el-input>
                    </el-form-item>
                    <el-form-item label="所属分公司" prop="companyId">
                        <el-select v-model="form.companyId" placeholder="请选择" @change="changeCompany()">
                            <el-option v-for="item in companyOption" :key="item.companyId" :label="item.orgName"
                                :value="item.companyId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                        <el-form-item label="部门">
                            <el-input v-model="form.departmentName"></el-input>
                        </el-form-item>
                    <el-form-item label="类型" prop="pumpType">
                        <el-select v-model="form.pumpType" placeholder="请选择">
                            <el-option v-for="item in pumpTypeOption" :key="item.value" :label="item.text"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="泵站负责人" prop="pumpHeadPerson">
                        <el-select v-model="form.pumpHeadPerson" placeholder="请选择">
                            <el-option-group v-for="group in ownerUnitHeadData" :label="group.label" :key="group.key">
                                <el-option v-for="item in group.children" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-option-group>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="泵站负责人联系方式" prop="pumpHeadPersonTel">
                        <el-input v-model="form.pumpHeadPersonTel"></el-input>
                    </el-form-item>
                    <el-form-item label="值班人员" prop="dutyPerson">
                        <el-select style="width: 100%" v-model="form.dutyPerson" multiple placeholder="请选择">
                            <el-option-group v-for="group in ownerUnitHeadData" :label="group.label" :key="group.key">
                                <el-option v-for="item in group.children" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-option-group>
                        </el-select>
                    </el-form-item>
                        <el-form-item label="申报人" prop="createPersonName">
                            <el-select v-model="form.createPersonName" placeholder="请选择" disabled>
                            </el-select>
                        </el-form-item>
                </el-row>
            </el-form>

            <el-form>
                <el-row>
                        <el-form-item v-if="dialogType !== 'view'" style="text-align: center">
                            <el-button type="primary" size="medium" @click="updatePumpStationInfo">提交</el-button>
                            <el-button @click="init()" size="medium">重置</el-button>
                        </el-form-item>
                        <el-form-item v-else class="form-inline pump" style="width: 500px;" label="审批意见：">
                            <el-button type="primary" size="medium" @click="projectPumpDeclareAudit(1)">审批通过</el-button>
                            <el-button type="primary" size="medium" @click="enterAuditOpinion">审批不通过</el-button>
                        </el-form-item>
                </el-row>
            </el-form>
        </div>
    </div>

</div>