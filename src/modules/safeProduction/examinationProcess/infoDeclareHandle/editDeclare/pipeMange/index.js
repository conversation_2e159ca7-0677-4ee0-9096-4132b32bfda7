let template = require('./content.html');
let userModel = require('controllers/model/userModel');
let safeProductionService = require('services/safeProductionService');
const declareTimeline = require('modules/common/declareTimeline');

const formParams = {
    workTeamName: '',
    workPlace: '',
    companyName: '',
    areaName: '',
    areaId: '',
    areaPerson: '',
    areaContact: '',
    teamLeaderName: '',
    teamLeaderContact: '',
    teamMember: [],
}

let comm = Vue.extend({
    template,
    props: {
        row: {
            type: Object,
            default: function () {
                return {}
            }
        },
        infoId: {
            type: Number,
            default: 0
        },
        infoType: {
            type: Number,
            default: 0
        },
        processName: {
            type: String,
            default: ''
        },
        projectType: {
            type: String,
            require: true
        },
        dialogType: {
            type: String,
            default: 'view'
        },
        closeDialog: {
            type: Function,
            default: () => {
            }
        },
        reload: {
            type: Function,
            default: () => {
            }
        },
        getNum: {
            type: Function,
            default: () => {}
        },
    },
    components: {
        declareTimeline
    },
    data() {
        return {
            pageLoading: false,
            pageLoadingText: '加载中',
            form: '',
            SelectDeclareUser: [],
            declareUser: '',
            //片区列表
            areaOption: undefined,
            cascaderValue: '',
            // 存储当前用户和所在公司信息
            userAndCompanyInfo: undefined,
            ownerUnitHeadData: [],
            ownerUnitHeadValue: [],
            // 表单规则
            formRules: {
                workTeamName: {required: true, message: '作业班组不能为空', trigger: 'blur'},
                workPlace: {required: true, message: '作业地址不能为空', trigger: 'blur'},
                // areaName: {required: true, message: '请选择所属片区', trigger: 'change'},
                areaPerson: {required: true, message: '请选择片长', trigger: 'blur'},
                areaContact: {required: true, message: '片长联系方式不能为空', trigger: ['blur', 'change']},
                teamLeaderName: {required: true, message: '请选择班组长', trigger: 'change'},
                teamLeaderContact: {required: true, message: '班组长联系方式不能为空', trigger: ['blur', 'change']},
                teamMember: {required: true, message: '请选择班组成员', trigger: 'change'}
            },
            // 审批意见
            auditOpinion: ''
        }
    },
    mounted() {
        // this.SelectDeclareUser.push({name: userModel.getUserInfo().name, userId: userModel.getUserInfo().userId});
        // this.declareUser = userModel.getUserInfo().userId;

    },
    created() {
        this.init(true);
    },
    methods: {
        /**
         * 初始化
         * @param flash
         */
        init(flash = false) {
            this.pageLoading = true
            this.form = Object.assign({}, this.form, formParams, {declareChildType: this.projectType})
            this.cascaderValue = [];
            this.selectTime = [];
            safeProductionService.getOnePipeMain({id: this.row.infoId},
                result => {
                    this.form = Object.assign({}, this.form, result[0])
                    console.log(this.form);
                },
                err => {
                })
            Promise.all([this.getProjectUserAndCompanyInfo(flash)]).then(res => {
                this.pageLoading = false;
            }).catch(err => {
                this.pageLoading = false
                this.$message.error(err)
            })
        },


        // },
        /**
         * 获取当前用户和所在公司信息
         * @param flash
         * @returns {Promise<any | never>}
         */
        getProjectUserAndCompanyInfo(flash = false) {
            return new Promise(resolve => {
                if (this.userAndCompanyInfo !== undefined && !flash) resolve(this.userAndCompanyInfo)
                else safeProductionService.getProjectUserAndCompanyInfo((result) => {
                    this.userAndCompanyInfo = [{companyName: result.companyName, ownerUnitId: result.companyId}];
                    this.form.companyName = result.companyName;
                    this.form.companyId = result.companyId;
                    let arr = [];
                    result.branchOrg.forEach(element => {
                        //过滤掉没有二级菜单的选项
                        if (element.option.length != 0) {
                            let obj = {};
                            obj.value = element.text;
                            obj.label = element.text;
                            obj.children = [];
                            for (let index = 0; index < element.option.length; index++) {
                                let obj2 = {
                                    label: element.option[index].userName,
                                    value: element.option[index].userId
                                }
                                obj.children.push(obj2)
                            }
                            arr.push(obj)
                        }
                    });
                    this.ownerUnitHeadData = arr;
                    this.changeWorkGroup();
                    resolve(result)
                })
            }).then(res => {
                this.form.companyId = res.companyId
                return res
            })
        },

        /**
         * 获取管网信息
         * @returns {Promise<unknown>}
         */
        changeWorkGroup() {
            return new Promise((resolve, reject) => {
                safeProductionService.getPipeRelateInfo({companyId: this.form.companyId}, (result) => {
                    this.areaOption = result.areaInfoList;
                    resolve()
                }, err => {
                    this.pageLoading = false
                    reject('获取管网信息失败')
                })
            })
        },

        /**
         * 获取管网修改变动内容
         * @param originProjectData
         * @param formDataProject
         */
        getUpdateContent(originProjectData, formDataProject) {
            let updateContent = '';
            if (originProjectData.workTeamName !== formDataProject.workTeamName) {
                updateContent += ',作业班组名';
            }
            if (originProjectData.workPlace !== formDataProject.workPlace) {
                updateContent += ',作业地点';
            }
            if (originProjectData.companyId !== formDataProject.companyId) {
                updateContent += ',所属分公司';
            }
            if (originProjectData.areaId !== formDataProject.areaId) {
                updateContent += ',所属片区';
            }
            if (originProjectData.areaPerson !== formDataProject.areaPerson) {
                updateContent += ',片长';
            }
            if (originProjectData.areaContact !== formDataProject.areaContact) {
                updateContent += ',片长联系方式';
            }
            if (originProjectData.teamLeaderId !== formDataProject.teamLeaderId) {
                updateContent += ',班组长';
            }
            if (originProjectData.teamLeaderContact !== formDataProject.teamLeaderContact) {
                updateContent += ',班组长联系方式';
            }
            if (originProjectData.teamMember.join(',') !== formDataProject.teamMember.join(',')) {
                updateContent += ',值班人员';
            }
            updateContent = updateContent.substr(1);
            return updateContent;
        },

        /**
         * 修改管网信息
         */
        updatePipeMainInfo() {
            this.form.isCompleteType = 1;
            this.form.updateContent = this.getUpdateContent(this.row, this.form);
            this.form.id = this.row.infoId;
            this.form.declareChildType = this.projectType;
            this.form.pipeMainUser = this.form.teamMember.join(',');
            safeProductionService.updatePipeMainInfo(
                this.form,
                result => {
                    this.$message.success('修改成功')
                },
                err => {
                    this.$message.success('修改失败')
                })
        },

        /**
         * 提交修改
         */
        updatePipe() {
            this.$refs.form.validate((valid) => {
                if (!valid) return false
                this.updatePipeMainInfo()
            })
        },

        /**
         * 审批不通过提示输入意见
         */
        enterAuditOpinion() {
            this.$prompt('审核说明', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPattern: /[^\s+]+/,
                inputErrorMessage: '说明不能为空'
            }).then(res => {
                this.auditOpinion = res.value
                this.projectPumpDeclareAudit(0);
            }).catch(err => {
            })
        },

        /**
         * 项目信息审核
         * @param auditResult 0不通过，1通过
         */
        projectPumpDeclareAudit(auditResult) {
            const {infoId, infoType, processName, auditOpinion} = this;
            if (auditResult === 0 && (!auditOpinion || !auditOpinion.trim())) {
                this.$message.error('请输入审核意见');
                return;
            }
            safeProductionService.infoDeclareAudit(
                {
                    infoId,
                    infoType,
                    processName,
                    auditResult,
                    auditOpinion
                },
                res => {
                    const {success} = res;
                    if (success) {
                        this.$message.success('审核成功');
                        this.closeDialog();
                        this.reload();
                        this.getNum();
                    } else {
                        this.$message.error('审核失败');
                    }
                },
                err => {
                    this.$message.error('审核失败');
                }
            );
        },
    },
})

module.exports = comm;
