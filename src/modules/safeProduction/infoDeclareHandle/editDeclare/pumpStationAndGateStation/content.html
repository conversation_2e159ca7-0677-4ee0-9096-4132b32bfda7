<div class="pumpStationAndGateStation" v-loading="pageLoading" :element-loading-text="pageLoadingText">
    <div class="flexBox">
        <div class="flexBoxLeft">
            <declare-timeline type="primary" :infoId="row.infoId" infoType="3"></declare-timeline>
        </div>
        <div class="flexBoxRight">
            <el-form :model="form" :rules="formRules" ref="form" class="form"
                :class="dialogType == 'view' ? 'diyFormView' : ''" size="medium" :inline="false" label-width="140px"
                :disabled="dialogType == 'view'">
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="闸站名称" prop="gateStationName">
                            <el-input v-model="form.gateStationName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="闸站地址" prop="gateStationAddr">
                            <el-input v-model="form.gateStationAddr"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="所属分公司" prop="companyName">
                            <el-select v-model="form.companyName" placeholder="请选择">
                                <el-option v-for="item in userAndCompanyInfo" v-if="userAndCompanyInfo"
                                    :key="item.ownerUnitId" :label="item.companyName" :value="item.companyName">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="部门">
                            <el-select v-model="form.department" placeholder="请选择">
                                <el-option v-for="item in departmentList"  :key="item.id"
                                           :label="item.name" :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="闸站负责人" prop="companyHeadName">
                            <el-select v-model="form.companyHeadName" filterable placeholder="请选择">
                                <el-option-group v-for="group in ownerUnitHeadData" :label="group.label"
                                    :key="group.key">
                                    <el-option v-for="item in group.children" :key="item.value" :label="item.label"
                                        :value="item.value"></el-option>
                                </el-option-group>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item>
                            <el-radio v-model="form.gateStationType" label="1">自营</el-radio>
                            <el-radio v-model="form.gateStationType" label="2">外委</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="闸站外运营单位" prop="gateStationOutUnit" v-if="form.gateStationType==2">
                            <el-input v-model="form.gateStationOutUnit"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="值班人员" prop="dutyPersonList">
                            <div class="tagCotainer">
                                <el-tag v-for="(item, index) in form.dutyPersonList" :closable="dialogType !== 'view'"
                                    @close="delDutyMemberTag(index)" v-model="form.dutyPersonList">
                                    {{item.dutyPersonName}} {{item.dutyPersonContact}}
                                </el-tag>
                            </div>
                            <div v-show="show">
                                <el-select v-show="form.gateStationType==1" style="width: auto;" v-model="dutyName"
                                    filterable placeholder="请选择姓名" id="dutyNameSelect">
                                    <el-option-group v-for="group in ownerUnitHeadData" :label="group.label"
                                        :key="group.value">
                                        <el-option v-for="item in group.children" :key="item.value" :label="item.label"
                                            :value="item.value"></el-option>
                                    </el-option-group>
                                </el-select>
                                <el-input v-show="form.gateStationType!=1" style="width: auto;" placeholder="姓名"
                                    v-model="dutyName" id="dutyName"></el-input>
                                <el-input placeholder="电话" style="width: auto;" v-model.number="dutyPhone"
                                    id="dutyPhone"></el-input>
                                <el-button @click="addDutyMember" class="addButton">添加</el-button>
                                <el-button @click="delDutyMember" class="delButton">删除</el-button>
                            </div>
                            <div @click="show=!show" class="addmember" v-if="dialogType !== 'view'">新增人员</div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="申报人" prop="createPersonName">
                            <el-select v-model="form.createPersonName" placeholder="请选择" disabled>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <el-form>
                <el-row>
                    <el-col :span="24">
                        <el-form-item v-if="dialogType !== 'view'" style="text-align: center">
                            <el-button type="primary" size="medium" @click="updateProject">提交</el-button>
                            <el-button @click="init()" size="medium">重置</el-button>
                        </el-form-item>
                        <el-form-item v-else class="form-inline pump" style="width: 500px;" label="审批意见：">
                            <el-button type="primary" size="medium" @click="projectPumpDeclareAudit(1)">审批通过</el-button>
                            <el-button type="primary" size="medium" @click="enterAuditOpinion">审批不通过</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </div>


</div>