<div class="mainContent safeProduction infoDeclareHandle" v-loading="pageLoading" :element-loading-text="pageLoadingText">
    <div class="diyCard">
        <div class="toolBar">
            <!-- <el-button
                    v-for="item in tabOption"
                    :type="tab == item.value ? 'primary' : 'info'"
                    @click="setTab(item.value)"
            >
                {{item.text}}
            </el-button> -->
            <el-radio-group v-model="tab" size="medium" @change="setTab">
                <el-radio v-for="item in tabOption" :label="item.value" border>
                    <span>{{item.text}}</span>
                    <span v-if="item.text === '待完善' && !!waitingUpdateNum" class="badge">{{waitingUpdateNum}}</span>
                    <span v-if="item.text === '待审批' && !!waitingAuditNum" class="badge">{{waitingAuditNum}}</span>
                </el-radio>
            </el-radio-group>
        </div>
    
        <el-table height="calc(100% - 88px)" class="el-table--scrollable-y" size="medium" stripe
                    :data="tableData" border>
            <el-table-column label="序号" align="center" type="index" width="65"/>
            <el-table-column prop="companyName" label="公司" min-width="130">
            </el-table-column>
            <el-table-column prop="declareChildTypeName" label="类型" min-width="130">
            </el-table-column>
            <el-table-column prop="infoName" label="名称" min-width="180">
            </el-table-column>
            <el-table-column prop="createTime" label="上报时间" min-width="170">
            </el-table-column>
            <el-table-column prop="createPerson" label="申报人" min-width="140">
            </el-table-column>
            <el-table-column label="操作" width="150" align="center" fixed="right">
                <template slot-scope="scope">
                    <el-button v-if="tab === 0" @click="showEditDialog(scope.row)" type="text" size="medium">修改</el-button>
                    <el-button v-else @click="showDetail(scope.row)" type="text" size="medium">查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>
    
        <div class="pageTool" v-if="tab==0">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pager.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pager.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pager.total">
            </el-pagination>
        </div>
    </div>

    <div v-if="dialogVisible">
        <edit-declare
                :row="selectRow"
                :infoId="selectRow.infoId"
                :infoType="selectRow.infoType"
                :processName="selectRow.processName"
                :dialogVisible.sync="dialogVisible"
                :dialogType="dialogType"
                :projectType="projectType"
                :closeDialog="closeDialog"
                :reload="getList"
                :tab="tab"
        ></edit-declare>
    </div>

</div>
