<!-- 履职清单 -->
<div class="mainContent workDetailPage" v-loading="pageLoading">
    <!-- 列表 -->
    <div class="detailPage">
        <div class="titleBar">
            <div class="backIconBar" @click="goBack">
                <img :src="'./img/safeProduction/onlineStudy/back.png'" class="backIcon"/>返回
            </div>
        </div>
        <div class="detailContent">
            <div class="workTable" style="margin-left: 10px">
                <div class="title">作业详情</div>
                <el-form :disabled="!canEdit || workDetail.isSupervision === 1 ? true : false">
                    <el-row :gutter="50">
                        <el-col :span="12">
                            <el-form-item label="预计作业时间">
                                <el-date-picker
                                        style="width: 100%"
                                        v-model="dateSpan"
                                        type="daterange"
                                        range-separator="-"
                                        value-format="yyyy-MM-dd"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        :clearable="false"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="所属工程或项目名">
                                <el-input v-model="workDetail.projectName"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="作业单位">
                                <el-input v-model="workDetail.workUnit"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="作业单位负责人">
                                <el-input v-model="workDetail.workUnitHead"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系电话">
                                <el-input v-model="workDetail.workUnitHeadContact"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="作业地点">
                                <el-input v-model="workDetail.workAdress"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="作业人员">
                                <el-input v-model="workDetail.workPerson"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="监护人员">
                                <el-input v-model="workDetail.supervisePerson"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-col :span="12">
                        <el-form-item label="作业审批人">
                            <el-input v-model="workDetail.auditPerson"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="安全措施">
                            <el-select v-model="safetySelect" filterable multiple placeholder="请选择">
                                <el-option v-for="item in safetyPrecautions" :key="item.value" :label="item.text" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="所属分公司">
                            <el-input v-model="workDetail.companyName" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="填报人">
                            <el-input v-model="workDetail.reportPerson" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-show="workDetail.closeStatus">
                        <el-form-item label="关闭情况说明">
                            <el-input type="textarea" v-model="workDetail.closeRemark"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-show="!workDetail.isSupervision && canEdit">
                        <el-form-item class="submitBtn">
                            <el-button type="primary" @click="submitForm">提交</el-button>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <div class="workTable" style="margin-right: 10px">
                <div class="title">督察详情</div>
                <el-form :disabled="true">
                    <el-row :gutter="50">
                        <el-col :span="12">
                            <el-form-item label="实际作业时间">
                                <el-input v-model="supervisionTime"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="安全措施是否落实">
                                <el-input v-if="supervisionDetail.isSafetyWork" value="是"></el-input>
                                <el-input v-else-if="supervisionDetail.isSafetyWork === 0" value="否"></el-input>
                                <el-input v-else></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="确认人">
                                <el-input v-model="supervisionDetail.confirmPerson"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系电话">
                                <el-input v-model="supervisionDetail.confirmPersonPhone"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="检查人员">
                                <el-input v-model="supervisionDetail.checkPerson"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="发现安全问题">
                                <el-input type="textarea" v-model="supervisionDetail.discoveryProblem"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="安全问题整改情况">
                                <el-input type="textarea" v-model="supervisionDetail.completeSituation"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="备注">
                                <el-input type="textarea" v-model="supervisionDetail.remark"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-col :span="12">
                        <el-form-item label="问题照片">
                            <ul v-if="lstProblemImg.length > 0" class="imgList">
                                <li
                                        v-for="(img, index) in lstProblemImg" class="imgWrapper"
                                        :style="'background-image: url(' + img + ');'"
                                        @click="clickPicHandler(index, lstProblemImg)">
                              </li>
                            </ul>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="整改照片">
                            <ul v-if="lstReformImg.length > 0" class="imgList">
                                <li
                                        v-for="(img, index) in lstReformImg" class="imgWrapper"
                                        :style="'background-image: url(' + img + ');'"
                                        @click="clickPicHandler(index, lstReformImg)">
                                </li>
                            </ul>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
        </div>
    </div>
</div>
