<div class="onlineStudyVideoConfig" v-loading="pageLoading" :element-loading-text="pageLoadingText">
    <div class="toolBar">
        <el-input placeholder="请输入搜索内容" v-model="queryForm.content">
            <el-button slot="append" type="primary" @click="loadVideoList"><i class="el-icon-search"></i></el-button>
        </el-input>

        <el-button type="primary" class="addVideo" size="medium" @click="addVideo">
            <img :src="'./img/safeProduction/onlineStudy/add-white.png'" class="addVideoIcon"/>新增
        </el-button>
    </div>
    <div class="videoTable">
        <div v-for="videoItem in videoData" class="videoRow">
            <div class="itemTextContent">
                <span class="itemTitle">{{videoItem.studyTitle}}</span>
                <span class="itemTime">{{videoItem.createTime}}</span>
            </div>
            <div class="videoIconGroup">
                <img :src="'./img/safeProduction/onlineStudy/edit.png'" class="videoIcon" @click="editVideo(videoItem)" />
                <img :src="'./img/safeProduction/onlineStudy/delete.png'" class="videoIcon" @click="deleteVideo(videoItem)" />
            </div>
        </div>
    </div>
    <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageNumber"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next"
            :total="totalRecord">
    </el-pagination>
    <el-dialog
            title="视频信息新增"
            class="addVideoDialog"
            :close-on-click-modal="false"
            :visible.sync="addDialogVisible">
        <el-form ref="form" :model="videoForm" label-width="80px"
                 v-loading="addDialogLoading"
                 :element-loading-text="addDialogLoadingText">
            <el-form-item label="标题">
                <el-input v-model="videoForm.studyTitle"></el-input>
            </el-form-item>
            <el-form-item label="封面图片">
                <div class="coverPicBar">
                    <el-upload
                            ref="addCoverUpload"
                            name="file_data"
                            :action="uploadCoverPicOption.uploadPicUrl"
                            :data="uploadCoverPicOption.data"
                            :on-success="addSuccessUpload"
                            list-type="picture-card"
                            :auto-upload="false"
                            limit="1">
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </div>
            </el-form-item>
            <el-form-item label="视频内容">
                <el-input v-model="videoForm.studyVideoLink" :disabled="checkAddVideoUploadFile"></el-input>
            </el-form-item>
            <el-form-item>
                <div class="addVideoUpload">
                    <el-upload
                            ref="addVideoUpload"
                            :disabled="videoForm.studyVideoLink!=''"
                            name="file_data"
                            :on-success="addSuccessUpload"
                            :action="uploadVideoOption.uploadPicUrl"
                            :on-change="onUploadChange"
                            :data="uploadVideoOption.data"
                            :auto-upload="false">
                        <el-button size="medium" type="primary">选择文件</el-button>
                    </el-upload>
                </div>
                <div class="videoContenTips">注：视频链接与视频文件只能二选一</div>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="commitAddVideo">提交发布</el-button>
        </span>
    </el-dialog>
    <el-dialog
            title="视频信息修改"
            class="editVideoDialog"
            :close-on-click-modal="false"
            :visible.sync="editDialogVisible">
        <el-form ref="form" :model="videoForm" label-width="80px"
                 v-loading="editDialogLoading"
                 :element-loading-text="editDialogLoadingText">
            <el-form-item label="标题">
                <el-input v-model="videoForm.studyTitle"></el-input>
            </el-form-item>
            <el-form-item label="封面图片">
                <div class="coverPicBar">
                    <el-upload
                            ref="editCoverUpload"
                            name="file_data"
                            :action="uploadCoverPicOption.uploadPicUrl"
                            :data="uploadCoverPicOption.data"
                            :on-remove="handleFileRemove"
                            :on-success="editSuccessUpload"
                            list-type="picture-card"
                            :auto-upload="false"
                            limit="1">
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </div>
            </el-form-item>
            <el-form-item label="视频内容">
                <el-input v-model="videoForm.studyVideoLink"  :disabled="checkEditVideoUploadFile"></el-input>
            </el-form-item>
            <el-form-item>
                <div class="addVideoUpload">
                    <el-upload
                            ref="editVideoUpload"
                            :disabled="videoForm.studyVideoLink!=''"
                            name="file_data"
                            :action="uploadVideoOption.uploadPicUrl"
                            :data="uploadVideoOption.data"
                            :on-success="editSuccessUpload"
                            :on-change="onUploadChange"
                            :on-remove="handleFileRemove"
                            :auto-upload="false">
                        <el-button size="medium" type="primary">选择文件</el-button>
                    </el-upload>
                </div>
                <div class="videoContenTips">注：视频链接与视频文件只能二选一</div>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="commitEditVideo">提交发布</el-button>
        </span>
    </el-dialog>
</div>
