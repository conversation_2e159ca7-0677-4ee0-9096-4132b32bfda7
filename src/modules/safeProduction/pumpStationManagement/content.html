<div class="mainContent safeProduction pumpStationManagement" v-loading="pageLoading"
    :element-loading-text="pageLoadingText">
    <div class="diyCard">
        <div class="toolBar">
            <div class="leftPart">
                <el-radio-group v-model="tab" size="medium">
                    <el-radio v-for="item in tabProjectOption" :label="item.value" border>{{item.text}}</el-radio>
                </el-radio-group>
            </div>
            <div class="rightPart">
                <el-popover placement="bottom-end" width="500" trigger="click">
                    <searchForm :declareChildType="tab" :searchQueryPump.sync="searchQueryPump"
                        :searchQueryGate.sync="searchQueryGate" :searchQueryPipe1.sync="searchQueryPipe1"
                        :searchQueryPipe2.sync="searchQueryPipe2"></searchForm>
                    <el-button type="primary" icon="el-icon-search" slot="reference" size="medium">查询</el-button>
                </el-popover>
            </div>
        </div>
        <pump-station-table v-show="tab == 5" :tab="tab" declareChildType="5" :searchQueryPump.sync="searchQueryPump">
        </pump-station-table>
        <gate-station-table v-show="tab == 6" :tab="tab" declareChildType="6" :searchQueryGate.sync="searchQueryGate">
        </gate-station-table>
        <pipe-search-table v-show="tab == 4" :tab="tab" declareChildType="4" :searchQueryPipe1.sync="searchQueryPipe1">
        </pipe-search-table>
        <pipe-fixed-table v-show="tab == 7" :tab="tab" declareChildType="7" :searchQueryPipe2.sync="searchQueryPipe2">
        </pipe-fixed-table>
    </div>
    

    <div v-if="dialogVisible">
        <detail-dialog :row="selectRow" :dialogVisible.sync="dialogVisible" :dialogType="dialogType"
            :getPumpStationTypeDict="getPumpStationTypeDict" :getAllBranchCompany="getAllBranchCompany"
            @reload="getList"></detail-dialog>
    </div>

</div>