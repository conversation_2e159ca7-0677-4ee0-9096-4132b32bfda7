<div class="pipeFixedTable" style="height:calc(100% - 50px);">
    <el-table height="calc(100% - 38px)"  :data="tableData" class="scrollable-y" @sort-change="sortChange" size="medium" stripe border>
        <el-table-column label="序号" align="center" type="index" width="65"/>
        <el-table-column
                prop="companyName"
                label="公司"
                align="center"
                min-width="140">
        </el-table-column>
        <el-table-column prop="departmentName" label="部门" align="center" min-width="140"></el-table-column>
        <el-table-column
                prop="workTeamName"
                label="作业班组"
                align="center"
                min-width="180">
        </el-table-column>
        <el-table-column
                prop="workPlace"
                label="作业地点"
                align="center"
                min-width="150">
        </el-table-column>
        <el-table-column
                prop="teamLeaderName"
                label="班组长"
                align="center"
                min-width="100">
        </el-table-column>
        <el-table-column
                prop="teamMemberName"
                label="班组成员"
                align="center"
                min-width="120">
        </el-table-column>
        <el-table-column
                prop="areaName"
                label="片区"
                align="center"
                min-width="150">
        </el-table-column>
        <el-table-column
                label="隐患总数"
                prop="riskReportNum" sortable
                align="center"
                min-width="130">
            <template slot-scope="scope">
                <span>{{scope.row.riskReportNum}}</span>
            </template>
        </el-table-column>
<!--        <el-table-column-->
<!--                label="履职打卡率(%)"-->
<!--                prop="checkRate" sortable-->
<!--                align="center"-->
<!--                min-width="140">-->
<!--            <template slot-scope="scope">-->
<!--                <span>{{scope.row.checkRate}}</span>-->
<!--            </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--                label="标签"-->
<!--                align="center"-->
<!--                min-width="140">-->
<!--            <template slot-scope="scope">-->
<!--                <div class="tagWrapper" style="margin-bottom: 5px;">-->
<!--                    <el-tag v-if="scope.row.riskReportNumTag !== 1 && scope.row.checkRateTag !== 1" type="success">正常</el-tag>-->
<!--                </div>-->
<!--                <div class="tagWrapper" style="margin-bottom: 5px;">-->
<!--                    <el-tag v-if="scope.row.riskReportNumTag === 1" type="danger">隐患数多</el-tag>-->
<!--                </div>-->
<!--                <div class="tagWrapper">-->
<!--                    <el-tag v-if="scope.row.checkRateTag === 1" type="danger">打卡率低</el-tag>-->
<!--                </div>-->
<!--            </template>-->
<!--        </el-table-column>-->
        <el-table-column
                prop="createTime"
                label="上报时间"
                align="center"
                min-width="140">
        </el-table-column>
        <el-table-column
                prop="createPersonName"
                label="申报人"
                align="center"
                min-width="100">
        </el-table-column>
        <el-table-column
                label="操作"
                fixed="right"
                width="120"
                align="center">
            <template slot-scope="scope">
                <el-button @click="showDetail(scope.row)" type="text" size="medium">修改</el-button>
            </template>
        </el-table-column>
    </el-table>
    <div class="pageTool" style="text-align: right">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="searchQueryPipe2.pageNumber"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="searchQueryPipe2.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pager.total">
        </el-pagination>
    </div>
    <div v-if="dialogVisible">
        <detail-dialog :tab="declareChildType" :row="selectRow" :dialogVisible.sync="dialogVisible" @reload="getList"></detail-dialog>
    </div>
</div>