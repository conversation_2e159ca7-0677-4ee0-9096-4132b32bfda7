let template = require('./content.html');
let safeProductionService = require('services/safeProductionService');

const params = {
    userId: '',
    infoId: '',
    infoType: '',
    companyIds: '',
    checkType: [],
    startTime: '',
    endTime: ''
}

let comm = Vue.extend({

    template,

    props: {
        // 与searchForm双向绑定的属性
        mode: {
            type: Object,
            require: true
        },
        // 确认执行函数
        onSubmit: {
            type: Function,
            default: function () {
                return function () {
                }
            }
        }
    },

    data() {
        return {
            formLoading: false,
            // 查询表单
            searchForm: Object.assign({}, params),
            companyList: [],
            userList: [],
            projectList: [],
            checkTypeList: [],
            infoIdAddId: '',
            date: []
        }
    },

    activated() {
        if (window.workdynamicsmanager) return;
        window.workdynamicsmanager = this;
        this.init()
    },

    computed: {
        /**
         * 当前项目类型下的项目选项
         * @returns {Array}
         */
        projectOptionInType() {
            let result = []
            this.projectOption.forEach(group => {
                result.push({
                    text: group.text,
                    option: group.option.filter(item => "" + item.type == this.searchForm.reportType)
                })
            })
            return result
        }
    },

    methods: {
        /**
         * 初始化
         */
        init() {
            this.resetForm()
            this.pageLoading = true
            Promise.all([this.getWorkDynamicDict()]).then(res => {
                this.pageLoading = false
            }).catch(err => {
                this.pageLoading = false
                this.$message.error(err)
            })
        },

        /**
         * 获取每日报告-工作动态页面字典信息
         * @returns {Promise<any | never>}
         */
        getWorkDynamicDict() {
            return new Promise((resolve, reject) => {
                safeProductionService.getWorkDynamicDict(res => {
                    resolve(res)
                }, err => {
                    reject('获取查询条件失败')
                })
            }).then(res => {
                this.companyList = res.companyList
                this.userList = res.userList
                this.checkTypeList = res.checkTypeList
                let arr = []
                for (let index = 0; index < res.projectList.length; index++) {
                    arr.push(res.projectList[index]);
                    arr[index].index = index;
                }
                this.projectList = arr;
                return res
            })
        },

        /**
         * 重置表单
         */
        resetForm() {
            this.searchForm = Object.assign({}, params);
            this.date = []
            this.infoIdAddId = '';
        },

        /**
         * 确认搜索
         */
        submitSearch() {
            let obj = Object.assign({}, this.searchForm);
            if (this.date && this.date.length != 0) {
                obj.startTime = this.date[0]
                obj.endTime = this.date[1]
                this.searchForm.startTime = this.date[0]
                this.searchForm.endTime = this.date[1]
            } else {
                obj.startTime = ''
                obj.endTime = ''
                this.searchForm.startTime = ''
                this.searchForm.endTime = ''
            }
            if (this.infoIdAddId) {
                let infoIdAddId = this.infoIdAddId.split('_')
                if (infoIdAddId && infoIdAddId.length == 2) {
                    obj.infoType = infoIdAddId[0]
                    obj.infoId = infoIdAddId[1]
                    this.searchForm.infoType = infoIdAddId[0]
                    this.searchForm.infoId = infoIdAddId[1]
                }
            } else {
                obj.infoType = ''
                obj.infoId = ''
                this.searchForm.infoType = ''
                this.searchForm.infoId = ''
            }
            if (obj.checkType.length) {
                obj.checkType = obj.checkType.join(',');
            }
            this.$emit('getSearchFrom', obj);
            this.$emit('update:mode', this.searchForm);
        },
        /**
         * 清空查询框，初始化页面
         */
        resetPageData() {
            this.resetForm();
            this.$parent.$parent.resetData();
        }
    },
});

module.exports = comm;
