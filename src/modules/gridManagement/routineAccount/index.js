var template = require("./content.html");
import girdService from "services/girdService";
var gridDownLoad = require("services/gridDownLoad");
var serviceHelper = require("services/serviceHelper.js");
var gridDownLoad = require("services/gridDownLoad");


var comm = Vue.extend({
  template: template,
  data: function () {
    return {
      searchFrom: {
        startDate: "",
        endDate: "",
        projectCategoryList: [],
        weather: "",
        companyId: "",
        townId: "",
        teamId: "",
        workType: "",
        remark: ""
      },
      tableData: [],
      companyList: [],
      streetList: [],
      total: 0,
      exportColumnSelect: [],
      exportColumn: [],
      otherDailyWorkList: [],
      batchList: [
        "主干管",
        "市直管网",
        "水生态一期",
        "水生态二期",
        "水生态一二期",
        "水生态三期",
        "水生态四期",
        "水生态五期",
        "茅洲河一期",
        "茅洲河二期",
        "东引管网完善工程",
        "东江管网完善工程",
        "存量管网",
        "其它",
        "石鼓污水厂配套管网"
      ],
      importList: ["三级", "二级", "一级", "特殊", "一般", "重要", "四级"],
      weatherList: ["晴", "雨", "阴"],
      workTypeList: ["现场检查","委外检测","水质检测","截流井排查", "协助街镇", "雨天易涝点巡查/值守","围挡架设/回收", "其他事项"],
      teamList: [],
      picVisible: false,
      initialIndex: 0,
      carouselPhotos: [],
    };
  },
  activated: function () {},
  created: function () {
    //获取地区树
    this.getAreasTrees();
  },

  methods: {
    //获取列表数据
    async getOtherDailyWork() {
      try {
        this.searchFrom["projectCategoryList"] = this.searchFrom[
          "projectCategoryList"
        ].filter((item) => item !== "全部");
        // this.searchFrom["importanceList"] = this.searchFrom[
        //   "importanceList"
        // ].filter((item) => item !== "全部");
        const res = await new Promise((resolve, reject) => {
          girdService.otherDailyWork(
            this.searchFrom,
            (res) => {
              resolve(res);
            },
            (error) => {
              reject(error);
            }
          );
        });

        this.tableData = res.data.list;
        this.total = res.data.totalRecord;
      } catch (error) {
        console.log(error);
      }
    },
    //获取地区树
    async getAreasTrees() {
      try {
        const res = await new Promise((resolve, reject) => {
          girdService.getAreasTree(
            {},
            (res) => {
              resolve(res);
            },
            (error) => {
              reject(error);
            }
          );
        });
        this.companyList = res.data[0].areaChildList;
        console.log(this.companyList, "Ssssssss");
        // this.dataInit();
        this.getTime();
        this.getOtherDailyWork();
        this.bindDictList();
      } catch (error) {
        console.log(error);
      }
    },
    // 重新绑定下拉框
    bindDictList(){
        this.exportColumn = [];
        this.exportColumnSelect = [];
        girdService.dictList(
            {
                category: "OTHER_DAILY_WORK_EXCEL_FIELD",
            },
            (res) => {
                this.otherDailyWorkList = res.data;
                this.exportColumnSelect = this.otherDailyWorkList;
                this.$nextTick(() => {
                    this.otherDailyWorkList.forEach((item) => {
                        this.exportColumn.push(item.id);
                    });
                });
            }
        );
    },
    // 获取当前时间和一周前的时间
    getTime() {
      // 格式化时间为字符串
      let now = new Date();
      let weekAgo = new Date(now.getTime() - 7 * 24 * 3600 * 1000);
      // 格式化时间为字符串
      this.searchFrom.endDate = now.toISOString().slice(0, 10);
      this.searchFrom.startDate = weekAgo.toISOString().slice(0, 10);
    },

    //分公司change改变镇街数据
    companyChange(val) {
      this.searchFrom.townId = "";
      this.streetList = [];
      this.searchFrom.teamId = "";
      this.teamList = [];
      if (val) {
        const selectedCompany = this.companyList.find(
          (item) => item.id === val
        );
        if (selectedCompany) {
          this.streetList = selectedCompany.areaChildList;
        }
        this.getCompanyTeamDictTree(val);
      }
    },

    //根据公司查询班组
    async getCompanyTeamDictTree(id) {
      try {
        const res = await new Promise((resolve, reject) => {
          girdService.getCompanyTeamDictTree(
            { id },
            (res) => {
              resolve(res);
            },
            (error) => {
              reject(error);
            }
          );
        });
        console.log(res, "班组");
        this.teamList = res.data.listDict;
        // console.log(this.companyList, "Ssssssss");
      } catch (error) {
        console.log(error);
      }
    },

    //班组分页
    pageChange(e) {
      this.searchFrom.pageNumber = e;
      // this.getTeamManagementLists();
      this.getOtherDailyWork();
    },
    handleSizeChange(e) {
      this.searchFrom.pageSize = e;
      // this.getTeamManagementLists();
      this.getOtherDailyWork();
    },
    confirm() {
      this.getOtherDailyWork();
    },
    resetExportColumn() {
      this.exportColumn = [];
    },
    sortTableFun(column) {
      this.sortHandle();
      let sort = `${column.prop}Sort`;
      if (column.prop) {
        //该列有绑定prop字段走这个分支
        if (column.order == "ascending") {
          this.searchFrom[sort] = 2;
        } else if (column.order == "descending") {
          //当用户点击的是升序按钮，即descending时
          this.searchFrom[sort] = 1;
        } else {
          console.log("0");
          this.searchFrom[sort] = 0;
        }
        this.getOtherDailyWork();
      }
    },
    // sortHandle() {
    //   this.searchFrom.townNameSort = 0;
    //   this.searchFrom.areaNameSort = 0;
    //   this.searchFrom.routeNameSort = 0;
    //   this.searchFrom.projectCategorySort = 0;
    //   this.searchFrom.routeLengthSort = 0;
    //   this.searchFrom.importanceSort = 0;
    //   this.searchFrom.frequencySort = 0;
    //   this.searchFrom.patrolCountSort = 0;
    //   this.searchFrom.openWellCountSort = 0;
    // },
    selectAll(select, options) {
      if (this.searchFrom[select].length < this[options].length) {
        this.searchFrom[select] = [];
        this[options].map((item) => {
          this.searchFrom[select].push(item);
        });
        this.searchFrom[select].unshift("全部");
      } else {
        this.searchFrom[select] = [];
      }
    },
    // 获取照片
    getPic(id) {
      let picUrl = [];
      try {
        picUrl.push(
          serviceHelper.getBasicPath("gwxjURL") +
            "/uploadFile/downloadFileById?id=" +
            id +
            "&token=" +
            serviceHelper.getToken()
        );
        return picUrl;
      } catch (e) {
        console.log(e);
      }
    },

    // 放大图片
    async showCarousel(row, index, str) {
      this.picVisible = true;
      this.carouselPhotos.splice(0);
      try {
        const res = await new Promise((resolve, reject) => {
          girdService.queryOtherDailyWorkPicList(
            { id: row.id, val: "OTHER_DAILY_WORK_PIC" + str },
            (res) => {
              resolve(res);
            },
            (error) => {
              reject(error);
            }
          );
        });
        console.log(res, "班组");
        res.data.forEach((item) => {
          this.carouselPhotos.push(
            serviceHelper.getBasicPath("gwxjURL") +
              "/uploadFile/downloadFileById?id=" + item + "&token=" + serviceHelper.getToken()
          );
        });
      } catch (e) {
        console.log(e);
      }
      this.initialIndex = index;
    },
    routeExport() {
      if (this.exportColumn.length === 0) {
        this.$message.warning("请至少选择一列");
        return;
      }
      let params = {
        startDate: this.searchFrom.startDate,
        endDate: this.searchFrom.endDate,
        companyId: this.searchFrom.companyId,
        townId:this.searchFrom.townId,
        teamId:this.searchFrom.teamId,
        workType:this.searchFrom.workType,
        projectCategoryList:this.searchFrom.projectCategoryList,
        weather:this.searchFrom.weather,
        dictIds: this.exportColumn.join(","),
      };
      gridDownLoad.getOtherDailyWorkListExcelExport(params, (res) => {});
    },
  },
  watch: {},
  deactivated: function () {
    // this.getTeamManagementLists();
  },
  components: {},
});
module.exports = comm;
