define([
  "config.dev",
  "config.prd",
  "config.test",
  "config.new",
  "utils/axiosHelper",
  "utils/AES",
], function (dev, prd, test, newServer, axiosHelper, AES) {
  var config;
  //RUNNING_ENV 为webpack定义变量，用作判断当前运行环境
  if (RUNNING_ENV === "dev") {
    config = dev;
    // config = prd;
  } else if (RUNNING_ENV === "prd") {
    config = prd;
  } else if (RUNNING_ENV === "test") {
    config = test;
  } else if (RUNNING_ENV === "newServer") {
    config = newServer;
  }
  console.log("环境：", RUNNING_ENV);
  var userToken = "",
    userData = "";
  let normalUrl = config.normalURL;
  var basicUrl = config.basicURL;
  let ewaterUrl = config.ewaterURL;
  let iotUrl = config.iotURL;
  let woUrl = config.woURL;
  let emerCommandUrl = config.emerCommandURL;
  let authUrl = config.authURL;
  var pipeUrl = config.pipeURL;
  var dpBasicURL = config.dpBasicURL;
  let newestVerison = config.newestVersionURL;
  let gwxjURL = config.gwxjURL;
  let safeProdUrl = config.safeProdURL;
  let aeURL = config.aeURL;
  window.basicURL = basicUrl;
  let pumpUrl = config.pumpUrl;
  var serviceEndpoint = {
    pumpUrl: pumpUrl,
    basicPath: basicUrl,
    ewaterPath: ewaterUrl,
    ewater: ewaterUrl,
    iotPath: iotUrl,
    iot: iotUrl,
    woUrl: woUrl,
    emerCommandUrl: emerCommandUrl,
    auth: authUrl,
    newestVerison: newestVerison,
    gwxjURL: gwxjURL,
    safeProdUrl: safeProdUrl,
    socketEndpoint: basicUrl + "/endpointMain",
    // login: basicUrl + '/login/loginValidNew',
    login: basicUrl + "/login/loginValidNew2", //密码基于base64传输
    modifyUserDisabled: ewaterUrl + "/sysUser/modifyUserDisabled", //批量禁用用户
    getUserInfo: ewaterUrl + "/sysUser/getUserInfo", //获取用户信息
    getValidateCode: ewaterUrl + "/vcode/getImage", //获取登录验证码
    getCaptcha: basicUrl + "/vcode/getCaptcha", //获取登录验证码
    userMenu: ewaterUrl + "/sysUser/getUserMenuByRootNodeClassify",
    makeOrder: basicUrl + "/orders/save",
    queryMenu: basicUrl + "/orders/menu",
    queryEmployee: basicUrl + "/employee",
    refreshToken: ewaterUrl + "/login/updateToken",
    queryOrder: basicUrl + "/orders/query",
    getAllFacilityType: iotUrl + "/facility/getAllFacilitysType",
    getCurrentUserFacilitysMonitor:
      iotUrl + "/facility/getFacilityRealTimeDataByToken",
    getCurrentUserFacilityMonitorByType:
      iotUrl + "/facility/getFacilityRealTimeDataByToken", //微服务接口
    refreshFacilityCache: iotUrl + "/facility/refreshFacilityCache",
    getFacilityByWkt: iotUrl + "/facility/getFacilityByWkt", //根据wkt查询测站
    getFacilityByType:
      basicUrl + "/dataRoleFacilityRlt/getFacilityByFacilityTypeName",
    getFacilityDetail: iotUrl + "/facility/getOneFacilityInfo",
    getDglsDataHistoryByItemIdAndRFid: iotUrl + "/monitorData/getDglsByItemID", //获取抽希历史记录
    getDataHistoryByItemIdAndRFid: iotUrl + "/monitorData/getByItemID", //获取非抽希历史记录
    deviceDetail: iotUrl + "/facility/getFacilityRealTimeData",
    monitorRealTimeValue: iotUrl + "/dataReal/getDataRealByItemIds",
    getProjects: ewaterUrl + "/project/getUserProjects",
    getProjectById: ewaterUrl + "/project/getUserProjectLayers",
    getDeviceList: iotUrl + "/iotDevice/list",
    getDeviceObject: iotUrl + "/iotDevice/get",
    getDeviceInfo: iotUrl + "/iotDevice/getDeviceInfo",
    getAllFacilities: iotUrl + "/facility/list",
    getFlowStatus: iotUrl + "/tide/list", //水文情况
    getDevicesByFacility: basicUrl + "/device/list",
    getMonitorsByDevice: iotUrl + "/item/list",
    getMonitorDetailByDevice: iotUrl + "/item/get",
    getOperationStatus: iotUrl + "/wellDayStats/getStatistic",
    saveMonitor: iotUrl + "/item/save",
    getItemFieldByItemTypeId: iotUrl + "/itemType/getItemFieldByItemTypeId",
    saveIotDeviceInfo: iotUrl + "/iotDevice/save",
    sendDataCommand: iotUrl + "/iotDevice/registerSendDataCommand",
    iotFacilityInfo: iotUrl + "/iotDevice/InfoList",
    getIotDeviceRunningState: iotUrl + "/iotDevice/iotDeviceRunningState",
    getIotDeviceOnlineState: iotUrl + "/iotDevice/iotDeviceOnlineState",
    getRainFacility: iotUrl + "/iotDevice/getRainFacility",
    getCurRequestInfo: basicUrl + "/elteVideo/getRequestInfo",
    uploadGpsInfo: basicUrl + "/elteVideo/uploadGpsInfo",
    queryeLTEtrace: basicUrl + "/elteVideo/queryGpsInfo",
    queryCompareIncrease: iotUrl + "/dataHistory/compareIncrease",
    collectFacilities:
      iotUrl + "/userFacilityCollection/updateUserFacilityCollection",
    addCollectFacilities: iotUrl + "/userFacilityCollection/collect", //新增收藏
    deleteCollectFacilities: iotUrl + "/userFacilityCollection/cancel", //取消收藏
    getCollectionFacilityList: iotUrl + "/facility/getCollectionFacilityList",
    gainTemperatureData: "http://wthrcdn.etouch.cn/weather_mini",
    getTodayWeatherReport: basicUrl + "/weatherReport/getTodayWeatherReport",
    getTodayRainfallReport: basicUrl + "/rainfallReport/getTodayRainfallReport",
    getTodayWeatherWarningInfo:
      basicUrl + "/weatherWarningInfo/getTodayWeatherWarningInfo",
    getAntiDataHistoryByItemId:
      iotUrl + "/dataHistory/getAntiDataHistoryByItemId",
    getAlarmOrWarningDataHistory:
      iotUrl + "/alarmOrWarningData/getAlarmOrWarningDataHistory",
    getAlarmOrWaringDataHistoryByFacilityId:
      iotUrl + "/alarmOrWarningData/getAlarmOrWaringDataHistoryByFacilityId",
    getIssueDataLogByFacility: iotUrl + "/dataRemoved/list", //问题数据列表
    getWorkLogByFacility: iotUrl + "/facilityLog/list", //设备运维日志
    saveInformation: iotUrl + "/facility/saveInformation", //设备配置项修改
    getCurUserVersionDetail:
      newestVerison + "/versionInfo/getCurUserVersionDetail", //版本管理-获取用户对应角色的版本信息和详情
    getPipeDataProblemReportList:
      basicUrl + "/pipeDataProblemReport/getPipeDataProblemReportList", // 获取数据审核工单列表
    getDistrictsUserList:
      basicUrl + "/pipeDataProblemReport/getDistrictsUserList", // 数据审核工单：获取分区跟进负责人列表
    sendXZProblemReport:
      basicUrl + "/pipeDataProblemReport/sendXZProblemReport", // 数据审核工单：派发单个工单
    batchSendXzProblemReport:
      basicUrl + "/pipeDataProblemReport/batchSendXzProblemReport", // 数据审核工单：批量派发工单
    checkXZProblemReport:
      basicUrl + "/pipeDataProblemReport/checkXZProblemReport", // 数据审核工单：确认核查
    checkUserHasCreateAndReadRole:
      basicUrl + "/pipeDataProblemReport/checkUserHasCreateAndReadRole", // 数据审核工单：查看用户是否有发起核查工单和查看的权限
    getFacilityMap: iotUrl + "/facility/getFacilityMap", // 获取设备详情
    getBlockInfoByXY: basicUrl + "/flowDirection/getBlockInfoByXY", // 通过地图坐标获取污水系统名称和排水切片名称
    getAllSewageSystem: basicUrl + "/flowDirection/getAllSewageSystem", // 获取所有污水分区
    getCompanySewages: basicUrl + "/pipeDailyStats/getPipeDailyDict", // 获取分公司和污水系统
    getUnitDiversionStatistics: basicUrl + "/shunTypeStat/getChunTypeStat", // 排水单元分流情况统计
    getUnitTypeStatistics: basicUrl + "/psUnitStat/returnTypeStatistics", // 排水单元分类情况统计
    getUnitCheckStatistics: basicUrl + "/psUnitStat/returnCheckStatistics", // 排水单元核查情况统计

    // 在线监测-提质增效(水质液位专题图)
    getSewageAnalysisList: basicUrl + "/sewageAnalysis/getSewageAnalysisList", // 提质增效-专题图：获取污水系统的分析列表
    getRegionAnalysisList: basicUrl + "/regionAnalysis/getRegionAnalysisList", // 提质增效-专题图：获取片区的分析列表
    getBlockAnalysisList: basicUrl + "/blockAnalysis/getBlockAnalysisList", // 提质增效-专题图：获取区块的分析列表
    // getPointAnalysisList: basicUrl + '/pointAnalysisInfo/getPointAnalysisList', // 提质增效-专题图：获取分析点列表
    getPointAnalysisList: basicUrl + "/pointAnalysis/getPointAnalysisList", // 提质增效-专题图：获取分析点列表
    addAnalysisPoint: basicUrl + "/pointAnalysisInfo/savePointAnalysisInfo", // 提质增效-新增分析点：保存分析点
    delFocusPoint: basicUrl + "/focusPoint/delFocusPoint", // 提质增效-新增分析点：删除关注点
    saveAnalysisPointData: basicUrl + "/analysisData/save", // 提质增效-新增分析点：分析点追加数据
    getUnitWktList: basicUrl + "/pointAnalysisInfo/getUnitWktList", // 提质增效-新增分析点：根据点的wkt获取收水范围
    getAnalysisDataById: basicUrl + "/analysisData/getAnalysisData", // 提质增效-新增分析点：获取分析点的列表详情
    checkFocusPointOnline: basicUrl + "/focusPoint/checkFocusPointOnline", // 提质增效-专题图：判断关注点是否在管线上
    saveFocusPointAndFlow: basicUrl + "/focusPoint/saveFocusPointAndFlow", // 提质增效-专题图：单独增加关注点
    flowConnectAnalysisBF: basicUrl + "/focusPoint/flowConnectAnalysisBF", // 排水单元-关注点管理-追溯
    saveFocusPoint: basicUrl + "/focusPoint/save", // 排水单元-关注点管理：保存关注点
    saveAnalaysisPoint: basicUrl + "/analaysisPoint/save", // 排水单元-关注点管理：新增分析点
    deleteAnalaysisPoint: basicUrl + "/analaysisPoint/delAnalysisPoint", // 排水单元-关注点管理：删除分析点
    getFocusPointList: basicUrl + "/focusPoint/list", // 排水单元-关注点管理：获取关注点列表
    getWaterUserByAnalysis: basicUrl + "/focusPoint/getWaterUserByAnalysis", // 排水单元-关注点管理：获取追溯范围列表
    getFocusPointById: basicUrl + "/focusPoint/getFocusPointById", // 排水单元-关注点管理：获取关注点详情
    flowConnectAnalysisBF: basicUrl + "/focusPoint/flowConnectAnalysisBF", // 排水单元-关注点管理-追溯
    deletePointAnalysisInfo:
      basicUrl + "/pointAnalysisInfo/deletePointAnalysisInfo", // 排水单元-删除分析点
    editAnalysisData: basicUrl + "/analysisData/save", // 排水单元-分析点修改
    deleteAnalysisData: basicUrl + "/analysisData/delete", // 排水单元-分析点修改
    getByCompanyOrSewage: basicUrl + "/pipeDailyStats/getByCompanyOrSewage", //获取片区信息
    batchUploadConcernPointData: basicUrl + "/analaysisPoint/inputByExcelModel", // 在线监测-提质增效：批量导入关注点数据
    queryLineFeatureByUsid: basicUrl+ '/pipeQuery/queryLineFeatureByUsid', //通过usid查管点
    getPipeLineByUsidAndXY: basicUrl+ '/pipeQuery/getPipeLineByUsidAndXY',
    // 提质增效 --后台重构接口
    saveFocusPoint: basicUrl + "/analysisPointInfo/analysisPointInfoSave", // 保存抽样点
    getAnalysisPointInfoWkt: basicUrl + "/analysisPointInfo/getWkt", // 获取污水厂行政区区块等wkt
    exportFocusPoint: basicUrl + "/analysisPointInfo/excelExport", // 导出模板
    batchUploadConcernPointData:
      basicUrl + "/analysisPointInfo/inputByExcelModel", // 导入分析点
    getFocusPointList: basicUrl + "/analysisPointInfo/getAnalysisPointInfoList", // 获取抽样点、分析点列表
    saveAnalaysisPoint: basicUrl + "/analysisPointInfo/analysisPointInfoSave", // 新增分析点（接口和抽样点一样）
    delFocusPoint: basicUrl + "/analysisPointInfo/analysisPointInfoDelete", // 删除抽样点
    getAnalysisMountStatDict:
      basicUrl + "/analysisMountStat/getAnalysisMountStatDict", // 获取关注点分析点目录结构
    getAnalysisMountStatList:
      basicUrl + "/analysisMountStat/getAnalysisMountStatList", // 获取各个层级的分析点统计数据
    getAnalysisMountStatList:
      basicUrl + "/analysisMountStat/getAnalysisMountStatList", // 获取各个层级的分析点统计数据
    getFocusPointById:
      basicUrl + "/analysisPointInfo/getAnalysisPointInfoDetails", // 排水单元-抽样点管理：获取抽样点详情(里面有追溯信息)
    deleteAnalaysisPoint: basicUrl + "/analysisData/deleteAnalysisData", // 排水单元-抽样点管理：删除分析点
    getAnalysisDataById: basicUrl + "/analysisData/getAnalysisData", // 提质增效-新增分析点：获取分析点的列表详情
    getAnalysisListData: basicUrl + "/analysisData/getAnalysisListData", // 提质增效-专题图：多点分析当月数据
    exportAnalysisListData: basicUrl + "/analysisData/exportAnalysisListData", // 提质增效-专题图：导出获取多点对比列表

    getFacilityExt: iotUrl + "/facility/getExt", //获取测站扩展字段
    getDeviceExt: iotUrl + "/device/getExt", //获取设备的扩展字段
    getItemExt: iotUrl + "/item/getExt", //获取设备的扩展字段
    updateFacilityExt: iotUrl + "/facility/updateExt", //修改测站扩展字段
    getDataWithGroup: iotUrl + "/dataHistory/getWithGroup", //查询历史监测数据组
    getFacilityDataHistory: iotUrl + "/dataHistory/get", //查询历史监测数据不分组
    getWaterQualityDataHistory: iotUrl + "/dataHistory/getWaterQuality", //查询历史监测数据不分组
    getFacilityAnalyzeData: iotUrl + "/dataDayStatistics/get", //查询日统计数据
    getFacilityDataByFid: iotUrl + "/facility/getFacilityRealTimeData", //根据fid来查询测站
    getRainfall: iotUrl + "/dataHistory/getRainfall",
    getTraceBallDirection: iotUrl + "/dataHistory/getDistance", //获取示踪球轨迹
    getFacilityMapByToken: iotUrl + "/facility/getMapByToken", // 获取设备
    getPumpStatistics: iotUrl + "/dataDayStatistics/getPumpStatistic", // 查询泵站电量

    //在线监测-设备地图总览
    getFacilityExt: iotUrl + "/facility/getExt", //获取测站扩展字段
    getDeviceExt: iotUrl + "/device/getExt", //获取设备的扩展字段
    getItemExt: iotUrl + "/item/getExt", //获取设备的扩展字段
    updateFacilityExt: iotUrl + "/facility/updateExt", //修改测站扩展字段
    getDataWithGroup: iotUrl + "/dataHistory/getWithGroup", //查询历史监测数据组
    getFacilityDataHistory: iotUrl + "/dataHistory/get", //查询历史监测数据不分组
    getWaterQualityDataHistory: iotUrl + "/dataHistory/getWaterQuality", //查询历史监测数据不分组
    getFacilityAnalyzeData: iotUrl + "/dataDayStatistics/get", //查询日统计数据
    getFacilityDataByFid: iotUrl + "/facility/getFacilityRealTimeData", //根据fid来查询测站
    exportRealData: iotUrl + "/dataHistory/exportPrimary", //设备原始数据导出
    collectFacilities:
      iotUrl + "/userFacilityCollection/updateUserFacilityCollection",
    getRainfall: iotUrl + "/dataHistory/getRainfall", // 获取雨量计数据
    downloadTempFile: iotUrl + "/tempFile/downloadFile", //下载图片

    //在线监测-水质数据统计-厂进水检测情况
    importSewageDailyData: iotUrl + "/wasteWaterData/importExcel", //污水厂数据批量导入
    getSewageDailyData: iotUrl + "/wasteWaterData/list", //获取污水浓度数据
    saveSewageDailyDate: iotUrl + "/wasteWaterData/save", //插入污水浓度数据
    deleteSewageDailyDate: iotUrl + "/wasteWaterData/delete", //删除污水浓度数据
    //在线监测-水质数据统计-片区水质浓度
    getWQMonthlyReport: basicUrl + "/checkPointRank/getRankInfoByYearMonth",
    exportWQMonthlyReport: basicUrl + "/checkPointRank/exportRankList",
    //在线监测-水质数据统计-厂进出水数据导入
    getDataDayStatisticsList: iotUrl + "/wasteWaterData/statistic", //获取表格数据-用于大屏
    // importDataDayStatisticsExcel: iotUrl + '/wasteWaterData/importExcel',//导入excel
    importDataDayStatisticsExcel: iotUrl + "/dataHistory/importSewage", //导入excel
    getSewageDataDayList: iotUrl + "/wasteWaterData/statistic", // 查询污水厂水质日数据列表
    // getSewageDataDayList: iotUrl + '/dataDayStatistics/getSewage2',// 查询污水厂水质日数据列表

    // 在线监测-区块相关
    getBlockByPoint: basicUrl + "/blockManagement/getPolygonByPoint", // 分析区块编辑：根据点坐标获取区块
    getNearlyBlockWkt: basicUrl + "/blockManagement/getNearlyWkt", // 分析区块：根据圈定图形查询周围区块的wkt
    getBlockPlaceByWkt: basicUrl + "/blockManagement/getPlaceByWkt", // 分析区块：根据wkt的rings,获取范围内区块的位置
    saveBlock: basicUrl + "/blockManagement/saveOrEditBlockMessage", // 分析区块：保存区块
    deleteBlock: basicUrl + "/blockManagement/deleteBlock", // 分析区块：删除区块

    /**
     * 设备工单
     */
    getWorkFlowList: iotUrl + "/workFlow/getPage", //设备工单列表
    getWorkFlowListByImei: iotUrl + "workFlow/getByImei", //单个设备工单列表
    monthlyMaintainList: iotUrl + "/monthlyMaintain/list", //月度维护计划报表
    saveMonthlyMaintain: iotUrl + "/monthlyMaintain/save", //月度维护计划报表
    deleteMonthlyMaintain: iotUrl + "/monthlyMaintain/delete", //月度维护计划报表
    getUploadImages: iotUrl + "/uploadFile/getUploadFilesByBizId", //获取图片列表
    reportDeviceMove: iotUrl + "/deviceMove/report", // 设备迁移工单-上报
    reviewDeviceMove: iotUrl + "/deviceMove/review", // 设备迁移工单-审核
    reviewDeviceTakeDown: iotUrl + "/deviceTakeDown/review", // 设备拆除工单-审核
    reviewDataErrorDelay: iotUrl + "/dataError/delayReview", // 监测异常工单-延期申请审核
    dataErrorReview: iotUrl + "/dataError/review", // 监测异常工单-审核
    reviewDeviceErrorDelay: iotUrl + "/deviceError/delayReview", // 设备故障工单-延期申请审核
    deviceErrorReview: iotUrl + "/deviceError/review", // 设备故障工单-审核
    addWorkFlowComment: iotUrl + "/workFlowComment/add", //评论-新增

    submitWorkLog: iotUrl + "/facilityLog/save", //设备运维日志
    deleteWorkLog: iotUrl + "/facilityLog/delete", //删除设备运维日志
    getEditLog: basicUrl + "/pipeEditLog/getChartByZone", //获取修改内容
    getLogListByFacilityId:
      iotUrl +
      "/facilityLog/getFacilityLogListByFacilityId?token=07a41e04fe7b4598b081193f067baf3d",
    getFacilityLogListByFacilityId:
      iotUrl + "/facilityLog/getFacilityLogListByFacilityId",
    getRFDataHistoryByItemId: iotUrl + "/dataHistory/getRFDataHistoryByItemId",
    getRFDataHistoryByImei: iotUrl + "/monitorData/getRfByImei",
    getDglsDataHistoryByItemId:
      basicUrl + "/dataHistory/getDglsDataHistoryByItemId",
    //  getHistoryDataStatsByItemId: iotUrl + '/dataHistory/getHistoryDataStatsByItemId',废弃
    getHistoryDataStatsByItemId: iotUrl + "/wellDayStats/getByImei",
    batchUploadSewageData: iotUrl + "/dataHistory/importWithExcel",
    getHourlyDataStatsByItemId: iotUrl + "/dataHistory/getDataByEvenTime", //获取偶数点统计图
    getSewageStatisticData: iotUrl + "/wasteWaterData/statistic", //获取污水浓度日统计数据
    getStatisticByMonth: iotUrl + "/wasteWaterData/statisticByMonth", //获取污水浓度月统计数据
    getCompareByMonth: iotUrl + "/wasteWaterData/compareByMonth", //获取污水浓度月叠加分析统计数据
    getCompareByYear: iotUrl + "/wasteWaterData/compareByYear", //获取污水浓度年叠加分析统计数据
    getMonthlyMaintainPlanList: iotUrl + "/monthlyMaintainPlan/list", //获取月度维护计划记录列表
    saveLstMonthlyMaintainPlan:
      iotUrl + "/monthlyMaintainPlan/saveLstMonthlyMaintainPlan", //批量保存月度维护计划
    deleteCurrentMonthlyMaintainPlan:
      iotUrl + "/monthlyMaintainPlan/deleteByDateAndUnit", //删除月度维护计划
    getMaintainScoreStatistic: iotUrl + "/maintainScore/statistic", //获取月度运维考核成绩
    getByItemScoreStatistic: iotUrl + "/maintainScore/getByItem", //获取月度运维考核扣分详情
    updateMaintainScore: iotUrl + "/maintainScore/update", //保存评分修正信息
    saveMaintainScore: iotUrl + "/maintainScore/save", //新增扣分项目
    getRFRainTime: basicUrl + "/dataHistory/getRFRainTime",
    getAllReport: basicUrl + "/report/listPage",
    launchPlan: basicUrl + "/recordCity/launchPlan",
    queryEmergencyRecrod: basicUrl + "/recordCity/listV2",
    endPlan: basicUrl + "/recordCity/endPlan",
    curLaunchPlan: basicUrl + "/recordCity/curLaunchPlan",
    createSluiceReport: basicUrl + "/sluiceRecord/createSluiceReport",
    createProcessReport: basicUrl + "/processRecord/createProcessReport",
    createRainReport: basicUrl + "/rainsRecord/createRainReport",
    uploadSluicePuddlePic: basicUrl + "/sluicePuddleRecord/uploadAndSave",
    obtainPicStream: basicUrl + "/sluicePuddleRecord/obtainPicStream",
    obtainProblemPicStream: basicUrl + "/problemRecord/obtainPicStream",
    getFuzzyNames: basicUrl + "/position/getFuzzyNames",
    positionByName: basicUrl + "/position/positionByName",
    drainHouseHoldFileSave: basicUrl + "/drainHouseHoldFile/save",
    positionByName2: basicUrl + "/position/positionByName",
    updateHandleRisk: gwxjURL + "/riskPoint/updateHandleRisk",
    getAllSewerageUser: basicUrl + "/drainHouseHoldFile/getAllSewerageUser",
    getUploadFilesByBizId: basicUrl + "/uploadFile/getUploadFilesByBizId",
    downloadFileById: basicUrl + "/uploadFile/downloadFileById",
    getAllDistrict: basicUrl + "/pipeDailyStats/getAllDistrict",
    getOwnerDeptByDistrict: basicUrl + "/pipeDailyStats/getOwnerDeptByDistrict",
    getPipeDailyStats: basicUrl + "/pipeDailyStats/getPipeDailyStats",
    getPipeDailyDetailStats:
      basicUrl + "/pipeDailyStats/getPipeDailyDetailStats",
    exportValveData: basicUrl + "/pipeDailyStats/exportValveData", // 截流井下载
    getChartStatics: basicUrl + "/pipeEditLog/getChartStatics", //运行统计
    getNewChartStatic: basicUrl + "/chartPipeDailyStats/getNewChartStatic", //运行统计2
    workOrderHelperCountTable: gwxjURL + "/workOrderHelper/countTable", //运行统计2
    workOrderHelperCountStreetTable:
      gwxjURL + "/workOrderHelper/countStreetTable", //运行统计2
    operateCount: gwxjURL + "/jhListCount/operateCount", //设施运营统计
    riskStatic: gwxjURL + "/riskFacility/riskStatic", //设施运营统计--养护
    attendEndAndClearCount: gwxjURL + "/jhListCount/attendEndAndClearCount", //设施运营统计--总表巡检部分
    countByOwner: gwxjURL + "/pumpPatrol/countByOwner", //泵站运营统计
    // getPipeDailyDetailStats: basicUrl + "/staByRoad/getPipeDailyDetailStats",
    queryPipeLayerByWkt: basicUrl + "/pipeQuery/queryPipeLayerByWkt",
    getStatPipeLineByWkt: basicUrl + "/pipeQuery/getStatPipeLineByWkt",
    getPipeLayers: basicUrl + "/pipeQuery/getPipeLayers",
    queryPipeLayerFeatureByWkt:
      basicUrl + "/pipeQuery/queryPipeLayerFeatureByWkt",
    getRandomInspectionInfo:
      basicUrl + "/facilityRandomInspection/getRandomInspectionInfo",
    getRandomStreetInfoByDistrict:
      basicUrl + "/facilityRandomInspection/getRandomStreetInfoByDistrict",
    save: basicUrl + "/facilityRandomInspection/save",
    pointLabelSave: basicUrl + "/pointLabel/save",
    getCollectionZoom: iotUrl + "/basin/getAllBasinAndFacility", //获取集水区域
    getCollectionZoomDetail: iotUrl + "/basinPipeline/getPipeLineByBasinName", //获取集水区域详情
    getAllLabelRecords: basicUrl + "/pointLabel/getAllLabelRecords",
    getPipeAndPointInfs: basicUrl + "/SearchPipeAndPoint/getPipeAndPointInfs",
    saveOnClick: basicUrl + "/SearchPipeAndPoint/saveOnClick",
    deleteRecords: basicUrl + "/checkPipeRecords/deleteRecords",
    deletPpointLabel: basicUrl + "/pointLabel/deleteRecords",
    getStreetWkt: basicUrl + "/checkPipeRecords/getStreetWkt",
    getMixedDatas: basicUrl + "/mixedTable/getMixedDatas",
    getAllMixedRecords: basicUrl + "/mixedTable/getAllMixedRecords",
    saveMixedValue: basicUrl + "/mixedTable/saveMixedValue",
    getRoads: gwxjURL + "/riskPoint/getRoads",
    getApprovadLogs: basicUrl + "/pipeEditLog/skimLogs", //已审批日志记录
    getDownloadApprovadLogs: basicUrl + "/pipeEditLog/outputLogs", //下载已审批日志记录
    downloadApprovadLogs: basicUrl + "/pipeUploadFile/downloadFileByPath", //下载已审批日志记录
    resetPassword: basicUrl + "/pipeUserManager/modifyPasswordNoLogin", //修改密码
    getWordReport: basicUrl + "/mixedTable/outputWord",
    getCheckReport: basicUrl + "/dataUpdate/downloadReport",
    checkUpdateArea: basicUrl + "/dataUpdate/checkWktArea", //校验核查范围
    submitCheckArea: basicUrl + "/dataUpdate/creatExportGdb", //提交核查范围
    saveCheckAreaForm: basicUrl + "/dataUpdate/save", //保存核查表单
    getRecordsByRiver: basicUrl + "/anadromousInfos/getRecordsByRiver",
    getRiverData: basicUrl + "/anadromousInfos/getRiverData",
    getAllRiverStatistic: basicUrl + "/anadromousInfos/getAllRiverStatistic",
    getLetOutRecordsByRiver:
      basicUrl + "/anadromousInfos/getLetOutRecordsByRiver",
    getPressRecordsByLetout:
      basicUrl + "/anadromousInfos/getPressRecordsByLetout", //埋压点
    getConnectRecordsByLetout:
      basicUrl + "/anadromousInfos/getConnectRecordsByLetout", //接驳点
    getMixedRecordsByLetout:
      basicUrl + "/anadromousInfos/getMixedRecordsByLetout", //混接点
    queryPipeLineByPoint:
      basicUrl + "/pipeReception/getPipeLineForReceptionByPoint", //点查询管线
    queryPipeLineByWKT:
      basicUrl + "/pipeReception/getPipeLineForReceptionByWkt", //框查询管线
    submitPipes: basicUrl + "/pipeReception/submitPipeReception", //提交管线
    submitExtraPipeLine: basicUrl + "/pipeReception/receiveExtraPipeLine", //提交额外管线
    deletePipes: basicUrl + "/pipeReception/deleteReceivedPipe", //删除接收管线
    getReceptionStatsByUser:
      basicUrl + "/pipeReception/getReceptionStatsByUser", //接收情况统计
    getPolygonByPoint: basicUrl + "/pipeDailyStats/getPolygonByPoint", //片区管理---获取当前点击的片区数据
    getReceptionByFlow: basicUrl + "/pipeReception/getReceptionByFlow", //线选接收管线
    partitionTypeList: basicUrl + "/partitionType/getAll", //片区管理--获取片区类型
    manualDistrictSave: basicUrl + "/pipeDailyStats/manualDistrictSave", //片区管理--片区信息提交保存
    getAllManualDistrict: basicUrl + "/pipeDailyStats/getAllManualDistrict", //片区管理--获取所有片区信息
    manualDistrictDelete: basicUrl + "/pipeDailyStats/manualDistrictDelete", //片区管理--删除片区
    getDistrictByUser: basicUrl + "/pipeReception/getDistrictWktByUserOrg", //片区管理--获取当前用户片区
    regionReport: basicUrl + "/paritionStatsResualt/getAll", //片区管理--统计
    checkLoginName: basicUrl + "/registerApply/checkLoginName", //验证用户名
    checkPhone: basicUrl + "/registerApply/checkPhone", //验证电话号码
    registerUser: basicUrl + "/registerApply/save", //提交注册信息
    getCadProjectSummary: basicUrl + "/geoGraphResult/getGeoGrapInfos", //获取图纸入库总览
    getCadProject: basicUrl + "/geoGraphResult/getGeoDetailsByCompany", //获取入库信息
    getDrawingRecords: basicUrl + "/drawingRecords/getDrawingRecords", //获取图纸入库列表
    getCompanyRecords: basicUrl + "/drawingRecords/getCompanyRecords", //获取图纸入库列表
    addOrEidtRecords: basicUrl + "/drawingRecords/addOrEidtRecords", //新增入库
    deleteDrawingRecords: basicUrl + "/drawingRecords/deleteRecords", //新增入库
    uploadFilePsgs: basicUrl + "/uploadFile/batchUploadFile", //上传附件
    updateOperate: basicUrl + "/drawingRecords/updateOperate", //更改入库状态
    removeRecords: basicUrl + "/drawingRecords/removeRecords", //图纸入库---数据迁移

    saveCadProject: basicUrl + "/geoGraphResult/saveOrderAndStatic", //录入注册信息
    deleteCadProject: basicUrl + "/geoGraphResult/deleteEntityByProject", //提交注册信息
    getOrgTree: basicUrl + "/registerApply/getOrgTree", //获取部门架构
    getDutyTree: basicUrl + "/registerApply/getDutyTree", //获取职务数列
    getWaitingList: basicUrl + "/registerApply/showApplyList", //获取等待验证列表
    distributeRole: basicUrl + "/pipeUserRole/distributeRole", //运行图权限列表
    distributeUser: basicUrl + "/pipeUserRole/distributeUser", //运行图权限列表
    handleApplication: basicUrl + "/registerApply/auditApply", //处理审核列表
    getInitFormValue: ewaterUrl + "/sysUser/getInitFormValue", //获取注册表单初始化信息
    getOnlineUser: basicUrl + "/otherFuns/getOnlineCount", //获取在线用户
    getWaitingListNumber: basicUrl + "/registerApply/notApproveCount", //获取等待审核列表
    getReceptionStatistic: basicUrl + "/pipeReception/getReceptionStatistic", //获取等待审核列表
    updatePassword: ewaterUrl + "/sysUser/modifyPassword", //修改密码
    getUserRoles: ewaterUrl + "/sysUser/get", //
    getSysUser: basicUrl + "/pipeSysUser/get", //用户列表-获取用户信息
    getLoginUserInfo: basicUrl + "/pipeSysUser/getLoginUserInfo", //用户个人信息获取
    getUserOrgTree: basicUrl + "/pipeSysUser/getOrgTree", //部门数据联动
    saveLoginUserInfo: basicUrl + "/pipeSysUser/saveLoginUserInfo", //用户个人信息保存
    sendSystemMessage: basicUrl + "/systemMessage/sendDeployMessageToTopic", //推送系统消息
    getStatic: basicUrl + "/areaStatic/getStatic", //获取总汇统计
    initValue: basicUrl + "/areaStatic/initValue", //获取切片统计下拉单数据
    getAllZoneDetails: basicUrl + "/areaStatic/getAllZoneDetails", //获取切片名称（带模糊查询）
    getCutByZone: basicUrl + "/areaStatic/getPolygonByZone", //按片区获取切片
    addNameForCut: basicUrl + "/areaStatic/addMesage2Block", //保存片区起止点
    click2ZoneDetails: basicUrl + "/areaStatic/click2ZoneDetails", //点击分块获取详细统计信息
    toSaveUpdateGuide: basicUrl + "/otherFuns/saveUpdateGuide", //保存用户手册
    initValue2Output: basicUrl + "/areaStatic/initValue2Output", //初始化统计条件
    searchStatisticResult: basicUrl + "/areaStatic/searchStatisticResult", //查询统计结果
    outPutAndCollect: basicUrl + "/areaStatic/outPutAndCollect", //导出并收藏
    getRecordByTitle: basicUrl + "/collects/getRecordByTitle", //收藏记录搜索或刷新
    getEarlyRecords: basicUrl + "/outBlockRecords/getEarlyRecords", //收藏记录搜索或刷新
    deleteRecord: basicUrl + "/collects/deleteRecord", //删除收藏记录
    createQuestionArea: basicUrl + "/pipeDataProblemReport/save", //创建问题记录
    deleteQuestionArea: basicUrl + "/pipeDataProblemReport/deleteOne", //删除问题记录
    getQuestionArea:
      basicUrl + "/pipeDataProblemReport/getAllPipeDataProblemReport", //查询问题记录
    facilityRealTimeDateByFlow:
      iotUrl + "/dataHistory/statsWaterLineByUseAndTide", //潮位统计
    getWaterLevelStatistic: iotUrl + "/wellDayStats/getStatistic", //统计水位和数据质量
    getWaterLineDayStats: iotUrl + "/dataHistory/getWaterLineDayStats", //获取中心区统计
    exportWaterDayStats: iotUrl + "/dataHistory/getWaterLineDayStatsExcel", //导出统计
    downloadTempFile: iotUrl + "/tempFile/downloadFile", //下载图片
    updatedValueByItemid: iotUrl + "/dataHistory/updateDataHistory", //更新监测数据
    getDailyStatsByImeis: iotUrl + "/wellDayStats/getStatisticDayByImeis", //设备日统计-根据imeis
    getDailyStatsByDataRole:
      iotUrl + "/wellDayStats/getStatisticDayByDataRoleId", //设备日统计-根据数据角色
    saveSewageData: iotUrl + "/monitorData/save", //考核点污水抽查统计
    //跟设备类型获取设备列表
    getFacilityByFacilityTypeName:
      iotUrl + "/facility/getFacilityRealTimeState",
    getStatsWaterLineExcel:
      iotUrl + "/dataHistory/statsWaterLineByUseAndTideExcel", //导出水位高低峰统计Excel
    queryChartLog: basicUrl + "/pipeEditLog/queryChartLog", //运行图日志
    queryLableInfo: basicUrl + "/pipeEditLog/queryLableInfo", //获取运行图备注信息
    getEditInfoByDate: basicUrl + "/pipeEditLog/getEditInfoByDate", //按日期搜索当天所有管网修改
    getEditInfo: basicUrl + "/pipeEditLog/getEditInfo", //分页搜索当天所有管网修改
    getStaticList: basicUrl + "/maintainNoStatic/getStaticList", //维护清单
    saveNewTask: basicUrl + "/approval/saveNewTask", //	保存新增核查工单
    getAreaWktByData: basicUrl + "/pipeEditLog/getAreaWktByData", //数据审批
    checkLogData: basicUrl + "/pipeEditLog/checkLogData", //数据审批
    batchCheckData: basicUrl + "/pipeEditLog/batchCheckData", //批量审批
    clickListToShowInfos: basicUrl + "/approval/clickListToShowInfos", //点击列表获取详细交互信息
    addReplyInfos: basicUrl + "/trail/addReplyInfos", //9.6.	批复提交
    getLeadCheckList: basicUrl + "/approval/getLeadCheckList", //点击列表获取详细交互信息
    queryMixedPoints: basicUrl + "/mixedPoint/getMixedRecordsByZone", //获取混接点
    summaryMixedPointsType: basicUrl + "/mixedPoint/getStatisByMixedType", //获取混接点类型
    summaryMixedPointsRoad: basicUrl + "/mixedPoint/getRoadData", //获取混接点道路
    getSelectValue: basicUrl + "/mixedPoint/getSelectValue", //获取下拉框的值
    updatePipe: iotUrl + "/facility/updateRfPipePoint", //获取混接点道路
    getCompanyAndZone: basicUrl + "/otherFuns/getCompanyAndZone", //	查询公司某片区管网修改记录
    getFlowDirectionBySewage:
      basicUrl + "/flowDirection/getFlowDirectionBySewage", //	获取排水切片流向
    downLoadFlowDirectionBySewage: basicUrl + "/flowDirection/exportBlocksInfo", //	下载排水切片流向
    getUnitDirection: basicUrl + "/flowDirection/click2AnalysisEs", //	获取追溯排水切片流向
    // queryMarkInChart: basicUrl + "/pipe2Update/queryMarkInChart", //查询标注点
    queryMarkInChart: gwxjURL + "/pipe2Update/queryMarkInChart", //查询标注点
    queryPumpAndSewageFactory: gwxjURL + "/toQuery/getInfoByPointAndBuffer", //查询泵站和污水厂信息  接口从psgs迁移gwxj
    getThematicMapWorkOrderAllDetail: gwxjURL + "/problemImportThematicMap/getWorkOrderAllDetail", //根据坐标点查询工单信息
    getVersionInfo: basicUrl + "/versionInfo/list", //获取版本信息列表
    saveVersionInfo: basicUrl + "/versionInfo/save", //创建与编辑版本信息
    deleteVersionInfo: basicUrl + "/versionInfo/delete", //删除版本信息接口
    getNewestVersionInfo:
      newestVerison + "/facility-management-app/www/package.json", //获取最新版本信息
    getPipeDailyDict: basicUrl + "/pipeDailyStats/getPipeDailyDict", //获取运行图的字典
    getAllSewageSystem: basicUrl + "/flowDirection/getAllSewageSystem", //获取所有的污水系统分区
    getReceptionStatsByDistrict:
      basicUrl + "/pipeReception/getReceptionStatsByDistrict", //获取已接收管渠总长
    exportBaiDuSimpleMainPipeJson:
      basicUrl + "/facilityStatis/exportBaiDuMainPipeSimplify", //百度地图动态获取简化管网json数据

    getRoleDistributeList: authUrl + "/roleDistribute/listPage", //获取角色配置列表信息
    getRoleSysDictInfo: authUrl + "/roleDistribute/getSysDictInfo", //获取角色配置页面字典信息
    saveRoleDistribute: authUrl + "/roleDistribute/save", // 新增或保存角色配置信息
    deleteRoleDistribute: authUrl + "/roleDistribute/deleteRoleDistribute", //删除角色配置信息

    getChartInfos: basicUrl + "/bigScreen/getChartInfos", //中心城市图标
    getInfoBySewageName: basicUrl + "/bigScreen/getInfoBySewageName", //获取污水系统的详细信息
    getGdWeatherInfoNow: basicUrl + "/fsData/getGdWeatherInfoNow", //获取大屏广州天气
    getStatCheckPointByDistrict:
      iotUrl + "/facilityStat/getStatCheckPointByDistrict",
    getRainFallByDistrict: iotUrl + "/facilityStat/getRainFallByDistrict",
    getTqybWeatherDetailInfo: basicURL + "/fsData/getTqybWeatherDetailInfo", // 获取广州市雨量分布
    getScreenChartInfos: basicURL + "/bigScreen/getScreenChartInfos", // 获取大屏管网统计数据
    getZoneWktByName: basicURL + "/bigScreen/getZoneWktByName", // 获取大屏镇街范围
    getSewageWktByName: basicURL + "/bigScreen/getSewageWktByName", // 获取大屏镇街下污水厂范围
    exportFacilityJsonByZone:
      basicURL + "/facilityStatis/exportFacilityJsonByZone", // 获取镇管线

    /* 厂站报表*/
    getAllFacilityName:
      normalUrl + "/psgs/facilityDailyReport/getAllFacilityName", //获取所有厂站的名称
    getTemplateByFacilityName:
      normalUrl + "/psgs/facilityDailyReport/getTemplateByFacilityName", //通过厂站名称获取对应的模板
    getTop3LogTypelst: normalUrl + "/psgs/inputOutPutLog/getTop3LogTypelst", //厂站导入导出最新记录
    postFsExcelFile: normalUrl + "/psgs/facilityDailyReport/postFsExcelFile", //上传报表
    getDailyReportList: normalUrl + "/psgs/facilityDailyReport/list", //获取厂站运行日志报表的列表
    getFacilityDailyInfo:
      normalUrl + "/psgs/facilityDailyReport/getFacilityDailyInfo", //获取厂站详细信息
    deleteDailyReport: normalUrl + "/psgs/facilityDailyReport/delete", //删除厂站日志报信息
    getFsByStatType: normalUrl + "/psgs/fsData/getFsDatasByStatType", //厂站运行工况统计
    getFsByWaterStat: normalUrl + "/psgs/fsData/getFsDatasByWaterStat", //厂站送水量统计
    exportFsByStatType: normalUrl + "/psgs/fsData/exportFsDatasByStatType", //厂站运行工况统计导出
    exportFsByWaterStat: normalUrl + "/psgs/fsData/exportFsDatasByWaterStat", //厂站送水量统计导出
    downloadFileByPath: normalUrl + "/psgs/pipeUploadFile/downloadFileByPath", //下载文件路径
    getTideListByTime: iotUrl + "/tide/getTideListByTime", //获取潮位曲线数据
    exportBaiDuFacilityJson: basicUrl + "/facilityStatis/exportFacilityJson", //百度地图动态获取管网json数据
    exportGzMainPipeJson: basicUrl + "/facilityStatis/exportGzMainPipeJson", //百度地图动态获取所有主线管
    exportByWktFacilityJson:
      basicUrl + "/facilityStatis/exportByWktFacilityJson", //获取截取面里面的所有管线
    exportBaiDuRiversJson: basicUrl + "/facilityStatis/exportBaiDuRiversJson", //获取污水处理分区河涌信息
    getWaterUserDaily: basicUrl + "/waterUser/getWaterUserDaily", //获取日均总用水量
    exportBaiDuSewageFarmJson:
      basicUrl + "/facilityStatis/exportBaiDuSewageFarmJson", //取echart的百度地图加入污水厂覆盖物
    exportBaiDuPumpJson: basicUrl + "/facilityStatis/exportBaiDuPumpJson", //取echart的百度地图加入泵站覆盖物
    exportBaiDuSewageSystemJson:
      basicUrl + "/facilityStatis/exportBaiDuSewageSystemJson", //取echart的百度地图加入污水处理范围覆盖物

    // 大鹏巡检接口
    showMaintProblemReport:
      basicUrl + "/facilityMaintProblemReport/listWithConfig",
    obtainFacilityInspectorInfo:
      basicUrl + "/facilityMaintProblemReport/obtainInspectorInfo",
    dispatchProblemReport:
      basicUrl + "/facilityMaintProblemReport/dispatchProblemReport",
    listMaintUserAttendStatic:
      basicUrl + "/maintUserAttendStatic/listWithConfig", //获取人员考勤list
    saveSafeTydisclosure: gwxjURL + "/safeTydisclosure/save", //新增或者修改安全交底内容
    findOneSafeTydisclosure:
      gwxjURL + "/safeTydisclosure/findOneSafeTydisclosure", //根据部门部门编号，获取安全交底内容
    deleteFileById: basicUrl + "/uploadFile/deleteById", //根据id删除图片
    getUploadFileByBiztype:
      basicUrl + "/pipeUploadFile/getUploadFilesByBiztype", //根据bizType获取附件
    addSafeTydisclosureLog:
      gwxjURL + "/safeTydisclosureLog/addSafeTydisclosureLog", //新增安全交底记录信息
    loadSafeTydisclosureLog: gwxjURL + "/safeTydisclosureLog/listWithConfig", //安全交底记录列表查询
    exportSafeTydisclosureLog:
      gwxjURL + "/safeTydisclosureLog/exportSafeTydisclosureLog", //安全交底记录导出
    safeTydisclosureTree: gwxjURL + "/safeTydisclosure/safeTydisclosureTree", //获取安全交底目录数据

    //获取邀请码
    getInvitationCode: basicUrl + "/pipeSysUser/invitationCodeandTime",

    // 人员车辆管理
    listWithConfigOfFacility: gwxjURL + "/maintTeam/listWithConfigOfFacility", // 班组管理->获取列表
    saveMaintTeam: gwxjURL + "/maintTeam/saveMaintTeamOfFacility", // 班组管理->添加班组
    initMainTeamDirt: gwxjURL + "/gwxjManager/initMainTeamDirt", // 班组管理->获取所属区域(字典)
    initTeamDirt: gwxjURL + "/gwxjManager/initTeamDirt", // 班组管理->获取班组类型(字典)
    getMaintTeamPersonnel: gwxjURL + "/maintTeam/getPeopleByTeamId", // 班组管理->班组人员->获取列表
    getAddUserOfFacility: gwxjURL + "/maintTeam/getAddUserOfFacility", // 班组管理->班组人员->获取可添加人员,
    addPersonnel: gwxjURL + "/maintTeam/addPersonnel", // 班组管理->班组人员->添加
    carPageList: gwxjURL + "/carInfo/carPageList", // 车辆管理->获取列表
    saveCarInfo: gwxjURL + "/carInfo/save", // 车辆管理->新增/修改车辆
    deleteContainRelate: gwxjURL + "/maintTeam/deleteContainRelate", // 班组管理->删除班组
    carInfoLoadDict: gwxjURL + "/carInfo/loadDict", // 车辆管理->获取字典
    deleteCarInfo: gwxjURL + "/carInfo/deleteCarInfo", // 车辆管理->删除
    saveCarMaintain: gwxjURL + "/carMaintain/save", // 车辆管理->详情->新增保养信息
    findOneCarMaintain: gwxjURL + "/carMaintain/findOneCarMaintain", // 车辆管理->详情->获取最新一次保养信息
    carMaintainPageList: gwxjURL + "/carMaintain/carMaintainPageList", // 车辆管理->详情->获取保养信息列表
    maintUserAttendListPage:
      gwxjURL + "/maintUserAttendDayStatic/listPageManager", // 人员考勤管理
    removePersonnel: gwxjURL + "/maintTeam/removePersonnel", // 班组管理->班组人员->删除
    makeCaptainOrRemove: gwxjURL + "/maintTeam/makeCaptainOrRemove", // 班组管理->班组人员->任命班长
    personHistory: gwxjURL + "/maintUserAttend/personHistory", // 人员巡查轨迹列表
    personMonitoring: gwxjURL + "/maintUserAttend/personMonitoring", // 人员出勤监测
    findAllUser: gwxjURL + "/maintUserAttend/findAllUser", // 人员巡查轨迹列表-获取所有用户列表
    personHistoryPlay: gwxjURL + "/maintUserAttend/personHistoryPlay", // 人员巡查轨迹列表-地图-获取列表
    getCarRealTimeGps: gwxjURL + "/carRealTimeGpsInfo/getCarRealTimeGps", // 车辆在线监测
    getCarHisTimeGps: gwxjURL + "/carHisTimeGpsInfo/getCarHisTimeGps", // 车辆轨迹回放

    // 工程维修
    workOrderListPage: gwxjURL + "/workOrder/engMaiWorkOrderListPage", // 工程管理->问题上报->列表
    engMaiWorkOrderlistCount: gwxjURL + "/workOrder/engMaiWorkOrderlistCount", // 工程管理->问题上报->统计
    engMaiWorkOrderCheck: gwxjURL + "/workOrder/engMaiWorkOrderCheck", // 工程维修->问题上报->核查
    engMaiWorkOrderSave: gwxjURL + "/workOrder/engMaiWorkOrderSave", // 工程维修->问题上报->详情->修改工单
    engMaiWorkOrderHandle: gwxjURL + "/workOrder/engMaiWorkOrderHandle", // 工程维修->处理
    getEngMaiWorkOrderServiceDetail:
      gwxjURL + "/workOrder/getEngMaiWorkOrderServiceDetail", // 工程维修->获取单个详情
    // 井盖维修
    manCovRepairWorkOrderListPage:
      gwxjURL + "/workOrder/manCovRepairWorkOrderListPage", // 井盖维修->问题上报->列表
    manCovRepairWorkOrderlistCount:
      gwxjURL + "/workOrder/manCovRepairWorkOrderlistCount", // 井盖维修->问问题上报->统计
    manCovRepairWorkOrderCheck:
      gwxjURL + "/workOrder/manCovRepairWorkOrderCheck", // 井盖维修->问题上报->核查
    manCovRepairWorkOrderSave: gwxjURL + "/workOrder/manCovRepairWorkOrderSave", // 井盖维修->问题上报->详情->修改工单
    manCovRepairWorkOrderHandle:
      gwxjURL + "/workOrder/manCovRepairWorkOrderHandle", // 井盖维修->处理
    getManCovRepairWorkOrderDetail:
      gwxjURL + "/workOrder/getManCovRepairWorkOrderDetail", // 井盖维修->获取单个详情
    // 清疏
    clearHydWorkOrderListPage: gwxjURL + "/workOrder/clearHydWorkOrderListPage", // 清疏->问题上报->列表
    clearHydWorkOrderlistCount:
      gwxjURL + "/workOrder/clearHydWorkOrderlistCount", // 清疏->问问题上报->统计
    clearHydWorkOrderCheck: gwxjURL + "/workOrder/clearHydWorkOrderCheck", // 清疏->问题上报->核查
    clearHydWorkOrderSave: gwxjURL + "/workOrder/clearHydWorkOrderSave", // 清疏->问题上报->详情->修改工单
    clearHydWorkOrderPlanSave: gwxjURL + "/workOrder/clearHydWorkOrderPlanSave", // 清疏计划->上报
    planClearHydTaskListPage: gwxjURL + "/planClearHydTask/listPage", // 清疏计划->上报列表
    planClearHydTaskDelete: gwxjURL + "/planClearHydTask/delete", // 计划清疏->计划清疏单删除
    getAddressLike: gwxjURL + "/planClearHydTask/getAddressLike", // 计划清疏->地址的远程搜索
    clearHydWorkOrderHandle: gwxjURL + "/workOrder/clearHydWorkOrderHandle", // 计划清疏->处理
    getClearHydWorkOrderServiceDetail:
      gwxjURL + "/workOrder/getClearHydWorkOrderServiceDetail", // 计划清疏->获取单个详情

    workOrderIsMyHandle: gwxjURL + "/workOrder/isHandle", // 是否我的待处理
    exportEngMaiExcal: gwxjURL + "/workOrderHelper/exportEngMaiExcel", // 工程导出
    exportManCovRePairExcel:
      gwxjURL + "/workOrderHelper/exportManCovRePairExcel", // 井盖导出
    exportClearHExcel: gwxjURL + "/workOrderHelper/exportClearHExcel", // 清疏导出

    dispatchProcessing: gwxjURL + "/workOrder/dispatchProcessing", // 问题上报--下派工单(工程维修)
    manCovRepairDispatchProcessing:
      gwxjURL + "/workOrder/manCovRepairDispatchProcessing", // 问题上报--下派工单(井盖维修)
    clearHydWorkOrderDispatchProcessing:
      gwxjURL + "/workOrder/clearHydWorkOrderDispatchProcessing", // 问题上报--下派工单(清疏)
    engMaiWorkOrderIsOutSourced: gwxjURL + "/engMaiWorkOrder/isOutSourced", // 转外委(工程维修)
    manCovRepairWorkOrderIsOutSourced:
      gwxjURL + "/manCovRepairWorkOrder/isOutSourced", // 转外委(井盖维修)
    clearHydWorkOrderIsOutSourced: gwxjURL + "/clearHydWorkOrder/isOutSourced", // 转外委(清疏)
    workOrderDelete: gwxjURL + "/workOrder/deleteById", // 问题上报--删除工单
    queryByXYWithBuff: gwxjURL + "/sdeOrder/queryByXYWithBuff", // 根据xy获取点击问题上报
    workOrderEditLog: gwxjURL + "/workOrderEditLog/list", // 过程记录(工程维修、井盖维修、清疏)
    getMainTeamWorkOrderCount: gwxjURL + "/workOrder/getMainTeamWorkOrderCount", // 班组在办案件统计

    // 巡检问题上报台账导出
    problemExportExcel: gwxjURL + "/problemImport/exportExcel", // 巡检问题上报台账导出
    // 暴雨巡查值守点台帐导出
    problemRainExportExcel: gwxjURL + "/problemImport/exportExcelRain", // 暴雨巡查值守点台帐导出

    // 违法违章、在建工地
    breakLawWorkOrderListPage: gwxjURL + "/workOrder/breakLawWorkOrderListPage", //违法违章/在建工地列表
    breakLawWorkOrderListCount:
      gwxjURL + "/workOrder/breakLawWorkOrderListCount", //违法违章统计
    getBreakLawWorkOrderServiceDetail:
      gwxjURL + "/workOrder/getBreakLawWorkOrderServiceDetail", //违法违章详情

    // 人员管理接口
    getRoleDict: ewaterUrl + "/role/v1/findAll", // 排口字典

    // 追溯流向工具
    changePipeFlow: basicUrl + "/pipeLineFlowFix/changePipeFlow", // 修改单条管线流向
    flowConnectAnalysisFlag:
      basicUrl + "/pipeLineFlowFix/flowConnectAnalysisFlag", // 管线追溯流向标记正常
    queryPipeCanalByWkt: basicUrl + "/pipeLineFlowFix/queryPipeCanalByWkt", // 获取范围内的管线和沟渠
    batchChangePipeFlow: basicUrl + "/pipeLineFlowFix/batchChangePipeFlow", // 批量修改流向
    changePipeLineGrade: basicUrl + "/pipeLineFlowFix/changePipeLineGrade", // 改变单条管线标志
    batchChangePipeLineGrade:
      basicUrl + "/pipeLineFlowFix/batchChangePipeLineGrade", // 批量修改管线标志

    // 排水户监管
    sewUserRegisterWorkOrderSave:
      gwxjURL + "/workOrderHelper/sewUserRegisterWorkOrderSave", // 办证登记上报
    sewUserRegisterWorkOrderListPage:
      gwxjURL + "/workOrderHelper/sewUserRegisterWorkOrderListPage", // 办证登记列表
    sewUserSuperviseWorkOrderListPage:
      gwxjURL + "/workOrderHelper/sewUserSuperviseWorkOrderListPage", // 证后监管列表
    pipeRecScoutingWorkOrderListPage:
      gwxjURL + "/workOrderHelper/pipeRecScoutingWorkOrderListPage", // 管网踏勘列表

    //布防管理接口
    protectionPointPageList:
      basicUrl + "/protectionPoint/listPageProtectionPoint", // 获取布防点管理列表
    deleteProtectionPoint: basicUrl + "/protectionPoint/deleteProtectionPoint", // 删除布防点
    insertOrUpdatePoint: basicUrl + "/protectionPoint/insertOrUpdatePoint", // 新增修改布防点
    isOpenProtection: gwxjURL + "/protectionLog/isOpenProtection", // 判断是否布防
    protectionLogList: gwxjURL + "/protectionLog/list", // 布防记录列表
    protectionPointWorkListPage:
      gwxjURL + "/workOrderHelper/protectionPointWorkListPage", // 布防点异常上报和无异常上报列表
    startProtection: gwxjURL + "/protectionLog/startProtection", // 布防点异常上报和无异常上报列表
    protectPointReportParamPageList:
      gwxjURL + "/workOrderHelper/protectionPointWorkListPage", // 布防点上报表格列表
    deleteProtectionPointReport:
      gwxjURL + "/workOrderHelper/deleteProtectionPointWorkOrderById", // 删除布防点上报表格的数据
    endProtection: gwxjURL + "/protectionLog/endProtection", // 取消布防
    deleteProtectionLog: gwxjURL + "/protectionLog/delete", // 删除布防启动记录

    /**
     * 应急调度平台
     */
    queryDefencePoints: emerCommandUrl + "/defencePosition/queryDefencePoints", //查询所有重点区域
    saveDefencePoint: emerCommandUrl + "/defencePosition/saveDefencePoint", //保存重点区域
    batchDeleteDefencePoint:
      emerCommandUrl + "/defencePosition/batchDeleteDefencePoint", //批量删除重点区域
    queryDefenceLines: emerCommandUrl + "/defenceLine/queryDefenceLines", //查询主干路布防信息
    saveDefenceLine: emerCommandUrl + "/defenceLine/saveDefenceLine", //主干路布防信息保存
    batchDeleteDefenceLine:
      emerCommandUrl + "/defenceLine/batchDeleteDefenceLine", //批量删除布防主干路
    queryDefenceAreas: emerCommandUrl + "/defenceArea2Point/queryDefenceAreas", //查询所有大型保障区域
    saveDefenceAreas: emerCommandUrl + "/defenceArea2Point/saveDefenceAreas", //保存大型保障区域
    batchDeleteDefenceArea:
      emerCommandUrl + "/defenceArea2Point/batchDeleteDefenceArea", //批量删除大型保障区域
    getCarOrgInfos: emerCommandUrl + "/carInfo/getCarOrgInfos", //查询车辆架构
    getCarInfosByNums: emerCommandUrl + "/realInfo/getCarInfosByNums", //查询车辆实时信息
    queryDraftInfo: emerCommandUrl + "/schedualTime/queryDraftInfo", //查询暂存信息
    saveDraftInfo: emerCommandUrl + "/schedualTime/saveDraftInfo", //暂存排班信息
    getOndutyOrgs: emerCommandUrl + "/dutyManager/getOndutyOrgs", //获取抢险组信息
    click2GetOndutyInfo: emerCommandUrl + "/dutyManager/click2GetOndutyInfo", //点击抢险组获取详细信息
    saveOfficerOnduty: emerCommandUrl + "/dutyManager/saveOfficerOnduty", //保存值班人员
    outputTemplateXls: emerCommandUrl + "/dutyManager/outputTemplateXls", //导出模板表
    outputDutyXls: emerCommandUrl + "/dutyManager/outputDutyXls", //导出值班信息表
    getTodayDutyInfo: emerCommandUrl + "/dutyManager/getTodayDutyInfo", //获取当日值班信息表
    uploadEmgFile: emerCommandUrl + "/uploadFile/batchUploadFile", //上传应急文件
    inputDutyInfo: emerCommandUrl + "/dutyManager/inputDutyInfo", //导入值班信息表
    getTreeData: ewaterUrl + "/org/getTreeData", //获取组织架构
    queryEmergencyTeam: emerCommandUrl + "/team/queryEmergencyTeam", //获取抢险队伍信息
    addOrSaveEmergencyTeam: emerCommandUrl + "/team/addOrSaveEmergencyTeam", //新增或保存保障队伍信息
    deleteEmergencyTeam: emerCommandUrl + "/team/deleteEmergencyTeam", //删除保障组信息
    getGPSInfoByTime: emerCommandUrl + "/carGPS/getGPSInfoByTime", //查询车辆轨迹信息
    getTeamOrg: emerCommandUrl + "/team/getTeamOrg", //获取抢险组组织架构
    getReservePlanList: emerCommandUrl + "/reserveInfo/getReservePlanList", //获取预警信息列表
    addOrEditReservePlan: emerCommandUrl + "/reserveInfo/addOrEditReservePlan", //新增/编辑预警信息
    deleteReservePlan: emerCommandUrl + "/reserveInfo/deleteReservePlan", //删除预警
    outputReservePlanList:
      emerCommandUrl + "/reserveInfo/outputReservePlanList", //导出预案列表信息
    searchReservePlan: emerCommandUrl + "/reserveInfo/searchReservePlan", //查询预警配置信息
    saveReserveStart: emerCommandUrl + "/reserveStart/saveReserveStart", //预警启动
    getActualWeatherInfos: emerCommandUrl + "/actual/getActualWeatherInfos", //获取实时天气信息
    getForecastWeatherInfos:
      emerCommandUrl + "/forecast/getForecastWeatherInfos", //获取天气预报信息
    getRadarInfo: emerCommandUrl + "/radarInfo/getRadarInfos", //获取雷达图信息
    getCarInfoList: emerCommandUrl + "/carInfo/getCarInfo", // 应急车辆管理:列表
    editCarInfo: emerCommandUrl + "/carInfo/editCarOrgInfo", // 应急车辆管理:修改
    batchEditCarInfo: emerCommandUrl + "/carInfo/batchEditCarOrgInfo", // 应急车辆管理:批量编辑
    exportCarInfoExcel: emerCommandUrl + "/carInfo/exportCarOrgInfo", // 应急车辆管理:导出Excel
    getAllDefenceAndWaterPoint:
      emerCommandUrl + "/waterpoint/getDefencePointAndWaterPointInfos", // 获取所有布防点与历史风险点
    endReserveStart: emerCommandUrl + "/reserveStartRecords/toEndReserve", // 停止当前预警
    queryReserveStart:
      emerCommandUrl + "/reserveStartRecords/queryReserveStart", // 是否有启动预警
    getTurnOutList: emerCommandUrl + "/turnOutInfo/getTurnOutList", // 获取出勤信息列表（布防点列表）
    getWaterPointList: emerCommandUrl + "/waterpoint/getWaterPointList", // 应急-获取风险点列表
    getWaterPointAndProblemInfos:
      emerCommandUrl + "/waterpoint/getWaterPointAndProblemInfos", // 通过风险点列表获取该风险点问题上报详细信息
    getTurnOutAndProblemInfos:
      emerCommandUrl + "/turnOutInfo/getTurnOutAndProblemInfos", // 通过布防点列表获取风险点详细信息
    getEmgFileUrl: emerCommandUrl + "/uploadFile/downloadFileById", // 应急-根据附件id获取附件地址
    getFileList: emerCommandUrl + "/uploadFile/getUploadFilesByBizId", // 应急-根据bizType和bizId获取附件列表
    getBaiduDrivingInfo: emerCommandUrl + "/baiduAPI/driving", // 获取百度驾车规划
    getFreeCaps: emerCommandUrl + "/problemReport/getFreeCaps", // 获取空闲队伍信息
    getShortestCaps: emerCommandUrl + "/problemReport/getShortestCaps", // 获取离目标点最近的三个空闲队伍
    toMissionDispatch: emerCommandUrl + "/waterpoint/toMissionDispatch", // 任务派遣
    getDispatchAndRequest: emerCommandUrl + "/waterpoint/getDispatchAndRequest", // 获取所有调度与增援数据
    addWaterPoint: emerCommandUrl + "/waterpoint/addWaterPoint", // 新增风险点
    getBranchOffice: emerCommandUrl + "/team/getBranchOffice", // 获取所有分支机构列表
    distributeToBranch: emerCommandUrl + "/dispatchRecords/distributeToBranch", // 统筹分配调度权限到分支机构
    secondDispatch: emerCommandUrl + "/dispatchRecords/secondDispatch", // 指派新命令
    getNotifications: emerCommandUrl + "/notification/getNotifications", // 获取消息列表
    getTurnOutTeams: emerCommandUrl + "/turnOutInfo/getTurnOutTeams", // 获取队伍列表
    getAllTurnOutInfos: emerCommandUrl + "/turnOutInfo/getAllTurnOutInfos", // 获取所有队伍列表
    clickToGetTurnOutInfos:
      emerCommandUrl + "/turnOutInfo/clickToGetTurnOutInfos", // 点击队伍列表获取详细出勤信息
    checkAndOrderTurnOut: emerCommandUrl + "/turnOutInfo/checkAndOrderTurnOut", // 督办出勤
    getDispatchList: emerCommandUrl + "/dispatchRecords/getDispatchList", // 获取任务列表信息
    getReserveInfo: emerCommandUrl + "/changeGradeRecords/getReserveInfo", // 获取当前预警配置信息
    changeReserveGrade:
      emerCommandUrl + "/changeGradeRecords/changeReserveGrade", // 预警升降级
    clickToCheckWaterPoint:
      emerCommandUrl + "/waterpoint/clickToCheckWaterPoint", // 点击地图获取50米范围内的风险点状态
    thatTimeReserveList:
      emerCommandUrl + "/changeGradeRecords/thatTimeReserveList", // 获取当次预警记录
    getReserveStaticList:
      emerCommandUrl + "/ReserveStatic/getReserveStaticList", // 水浸统计总表-获取预警统计列表
    clickStaticList2Waterpoints:
      emerCommandUrl + "/waterPointStatic/clickStaticList2Waterpoints", // 水浸统计总表-获取预警统计列表的详细风险点统计信息
    getReserveStartList: emerCommandUrl + "/reserveStart/getReserveStartList", // 获取历史所有预警信息
    getSubmitInfoList: emerCommandUrl + "/submittedInfo/getSubmitInfoList", // 获取到位情况列表
    clickInplaceInfoList:
      emerCommandUrl + "/submittedInfo/clickInplaceInfoList", // 点击列表获取详细报告信息
    queryInplaceInfo: emerCommandUrl + "/submittedInfo/queryInplaceInfo", // 选择某个预警获取到位信息
    saveInplaceInfo: emerCommandUrl + "/submittedInfo/saveInplaceInfo", // 保存当次到位情况
    clickSafeInfoList: emerCommandUrl + "/submittedInfo/clickSafeInfoList", //获取巡防安全详细报告信息
    saveSafeInfo: emerCommandUrl + "/submittedInfo/saveSafeInfo", //保存当次巡防情况
    querySafeInfoInfo: emerCommandUrl + "/submittedInfo/querySafeInfoInfo", //选择某个预警获取巡防安全信息
    batchDeleteSubmitInfo:
      emerCommandUrl + "/submittedInfo/batchDeleteSubmitInfo", // 批量删除上报信息
    queryWaterOutInfo: emerCommandUrl + "/submittedInfo/queryWaterOutInfo", // 点击水浸列表获取详细报告信息
    clickWaterOutInfoList:
      emerCommandUrl + "/submittedInfo/clickWaterOutInfoList", // 点击水浸列表获取详细报告信息
    saveWaterOutInfo: emerCommandUrl + "/submittedInfo/saveWaterOutInfo", // 保存当次水浸信息
    queryOneRainInfo: emerCommandUrl + "/submittedInfo/queryOneRainInfo", // 选择某个预警获取一雨一报信息
    clickOneRainInfoList:
      emerCommandUrl + "/submittedInfo/clickOneRainInfoList", // 点击一雨一报列表获取详细报告信息
    saveOneRainInfo: emerCommandUrl + "/submittedInfo/saveOneRainInfo", // 保存当次一雨一报信息
    clickThreeDayInfoList:
      emerCommandUrl + "/submittedInfo/clickThreeDayInfoList", // 点击三天一报列表获取详细报告信息
    queryThreeDayInfo: emerCommandUrl + "/submittedInfo/queryThreeDayInfo", // 选择某个预警获取三天一报信息
    saveThreeDayInfo: emerCommandUrl + "/submittedInfo/saveThreeDayInfo", // 保存当次三天一报信息
    exportReserveStartList:
      emerCommandUrl + "/reserveStart/exportReserveStartList", // 导出预警信息
    getNextDayCheckDutyRecords:
      emerCommandUrl + "/checkDutyRecords/getNextDayCheckDutyRecords", // 值班表检查记录
    exportDispatchList: emerCommandUrl + "/dispatchRecords/exportDispatchList", // 导出任务列表信息
    getMessageRecordsList:
      emerCommandUrl + "/messageRecords/getMessageRecordsList", // 短信记录表
    thatTimeReserveList:
      emerCommandUrl + "/changeGradeRecords/thatTimeReserveList", // 获取历史调度信息
    getWaterPointList: emerCommandUrl + "/waterpoint/getWaterPointList", // 预警记录-任务列表获取
    getTurnOutTeamsByReserveId:
      emerCommandUrl + "/turnOutInfo/getTurnOutTeamsByReserveId", //获取队伍列表信息
    exportMessageRecordsList:
      emerCommandUrl + "/messageRecords/exportMessageRecordsList", // 导出短信记录表
    exportStaticList2Waterpoints:
      emerCommandUrl + "/waterPointStatic/exportStaticList2Waterpoints", // 导出风险点信息
    getWaterpointsList:
      emerCommandUrl + "/waterPointStatic2/getWaterpointsList", // 获取水浸点列表
    exportWaterpoints2List:
      emerCommandUrl + "/waterPointStatic2/exportWaterpoints2List", // 导出水浸点列表
    revocationReserveStart:
      emerCommandUrl + "/reserveStartRecords/revocationReserveStart", // 撤防当前预警
    revocationWaterPoint: emerCommandUrl + "/waterpoint/revocationWaterPoint", // 撤销风险点
    editReserveStatic: emerCommandUrl + "/ReserveStatic/editReserveStatic", // 编辑统计总表
    getCarInfosByReserve: emerCommandUrl + "/turnOutInfo/getCarInfosByReserve", // 获取预警所有出勤得车辆信息
    sendReserveStartMessageLead:
      emerCommandUrl + "/reserveStart/sendReserveStartMessageLead", // 给领导发送预警启动信息
    queryTurnOutPowerInfoInfo:
      emerCommandUrl + "/submittedInfo/queryTurnOutPowerInfoInfo", // 选择某个预警获取出动力量信息
    clickTurnOutPowerInfoList:
      emerCommandUrl + "/submittedInfo/clickTurnOutPowerInfoList", // 点击出动力量列表获取详细报告信息
    saveTurnOutPowerInfo:
      emerCommandUrl + "/submittedInfo/saveTurnOutPowerInfo", // 保存当次出动力量情况
    queryLargeGroupInfoInfo:
      emerCommandUrl + "/submittedInfo/queryLargeGroupInfoInfo", // 选择某个预警获取大型组信息
    clickLargeGroupInfoList:
      emerCommandUrl + "/submittedInfo/clickLargeGroupInfoList", // 点击大型组列表获取详细报告信息
    saveLargeGroupInfo: emerCommandUrl + "/submittedInfo/saveLargeGroupInfo", // 保存当次大型组情况
    editWaterpointsList:
      emerCommandUrl + "/waterPointStatic2/editWaterpointsList", // 水浸点列表编辑
    searchReservePush: emerCommandUrl + "/reservePush/searchReservePush", // 预警推送查询
    saveReservePush: emerCommandUrl + "/reservePush/save", // 保存修改预警推送
    getTeamInfosList: emerCommandUrl + "/teamRecords/getTeamInfosList", // 获取抢险队伍列表
    addOrUpdateTeamInfos: emerCommandUrl + "/teamRecords/addOrUpdateTeamInfos", // 新增或保存抢险队伍信息
    deleteTeamInfos: emerCommandUrl + "/teamRecords/deleteTeamInfos", // 删除抢险队伍
    getReserveInfoList: emerCommandUrl + "/reserveInfo/getReserveInfoList", // 获取预案配置列表
    addOrSaveReserveInfo: emerCommandUrl + "/reserveInfo/addOrSaveReserveInfo", // 新增或编辑预警方案
    click2ReserveInfoList:
      emerCommandUrl + "/reserveInfo/click2ReserveInfoList", // 点击列表获取配置方案
    deleteReserveInfo: emerCommandUrl + "/reserveInfo/deleteReserveInfo", // 删除预警方案
    getLimitsList: emerCommandUrl + "/limitsOfEnd/getLimitsList", // 获取撤防权限列表
    addOrSaveLimits: emerCommandUrl + "/limitsOfEnd/addOrSaveLimits", // 新增或编辑撤防权限配置
    deleteLimits: emerCommandUrl + "/limitsOfEnd/deleteLimits", // 删除撤防权限配置
    getReserveInfoByLevel:
      emerCommandUrl + "/reserveInfo/getReserveInfoByLevel", // 根据预警等级获取配置相关的信息
    toStartReserve: emerCommandUrl + "/reserveStartRecords/toStartReserve", // 启动预警
    getMemberStateList:
      emerCommandUrl + "/reserveStartRecords/getMemberStateList", // 获取班组成员列表
    toSureRevocation: emerCommandUrl + "/waterpoint/toSureRevocation", // 撤防管理人确认布防点撤防
    personHistoryPlayByUser:
      gwxjURL + "/maintUserAttend/personHistoryPlayByUser", // 应急人员巡查轨迹回放
    signOutAttendanceEmg: gwxjURL + "/maintUserAttend/signOutAttendanceEmg", // 对已出勤未撤防的人员进行签退

    // 清疏(新需求)
    clearHydPointList: gwxjURL + "/clearHydPoint/list", // 问题点列表
    getWorkOrderProblemTypeDteail: gwxjURL + "/workOrder/getProblemTypeDteail", // 问题点列表

    outsourcingUnitList: gwxjURL + "/outsourcingUnit/list", // 外委单位列表
    outsourcingUnitSave: gwxjURL + "/outsourcingUnit/save", // 外委单位增加/修改
    outsourcingUnitDelete: gwxjURL + "/outsourcingUnit/delete", // 外委单位删除
    outsourcingUnitAddUnitAdmin: gwxjURL + "/outsourcingUnit/addUnitAdmin", // 外委单位添加管理员
    outsourcingUnitGet: gwxjURL + "/outsourcingUnit/get", // 外委单位获取人员
    getUnitNameAndUnitIdByUnitType:
      gwxjURL + "/outsourcingUnit/getUnitNameAndUnitIdByUnitType", // 根据类型获取外委单位
    outsourcedUserList: gwxjURL + "/workOrderHelper/outsourcedUserList", // 外委管理员列表
    workOrderUnDeleteById: gwxjURL + "/workOrder/unDeleteById", // 工单回收站恢复

    // 客服投诉
    getComplaintsListPage: gwxjURL + "/workOrderHelper/complaintsListPage", // 获取投诉列表
    complaintWorkOrderSave: gwxjURL + "/workOrderHelper/complaintWorkOrderSave", // 客服投诉上报
    dispatchProcessing: gwxjURL + "/complaints/dispatchProcessing", // 下派投诉问题
    completeWorkOrder: gwxjURL + "/complaints/completeWorkOrder", // 客服回复
    getAllDistinct: gwxjURL + "/workOrderHelper/getAllDistinct", // 获取所有片区（和巡检内容一样）
    getDistrictHead: gwxjURL + "/workOrderHelper/getDistrictHead", // 获取对应片区的片长
    deleteWorkOrderById: gwxjURL + "/workOrderHelper/deleteWorkOrderById", // 删除投诉
    manCovRepairWorkOrderCount: gwxjURL + "/count/manCovRepairWorkOrderCount", // 井盖维修统计
    exportComplaintList:
      gwxjURL + "/complaintWorkOrder/complaintWorkOrderExport", // 投诉问题管理-导出
    getCurrentWorkOrder: gwxjURL + "/workOrder/getCurrentProgressWorkOrder", // 投诉问题管理-下派类型
    saveProblemImport: gwxjURL + "/problemImport/saveProblemImport",// 修改问题上报

    // 运行图管线增删改查
    wgs84ToGzCoord: basicUrl + "/coordTrans/wgs84ToGz", // 新增-广州转wgs84坐标
    increatePoint: basicUrl + "/pipe3Update/increatePoint", // 新增-新增管点
    queryPipeByGeometry: basicUrl + "/pipeQuery/queryPipeLayerFeatureByPoint", // 关联和删除-点查询设施
    queryPipeByWkt: basicUrl + "/pipeQuery/simpleQueryPipeLayerFeatureByWkt", // 关联-wkt查询设施
    createPipeLine: basicUrl + "/pipe3Update/increateLine", // 关联-确定
    getSewagUserDetail: basicUrl + "/sewageUser/getSewagUserDetail", // 关联-查询排水户或者在建工地的详情数据
    saveRltTable: basicUrl + "/sewageUser/saveRltTable", // 关联-接户井关联保存
    deletePipeList: basicUrl + "/pipe3Update/batchDeleteData", // 删除-批量删除管线
    queryDeletePipeList: basicUrl + "/pipe3Update/getDeleteFetureInfos", // 回收站-获取删除管线
    restoreDeletePipeList: basicUrl + "/pipe3Update/renewFeture", // 回收站-恢复删除管线
    queryFacilityByUsid: basicUrl + "/pipe3Update/getInfoByUsid", // 修改-通过usid查询管线
    editPointAttribute: basicUrl + "/pipe3Update/editPointAttribute", // 修改-修改管点信息
    editLineAttribute: basicUrl + "/pipe3Update/editLineAttribute", // 修改-修改管綫信息
    movePoint: basicUrl + "/pipe3Update/movePoint", // 修改-移动管点
    queryPipeUsage: basicUrl + "/recipientsRlt/queryBothRlt", // 修改-查询区块排水户
    removePipeUsage: basicUrl + "/recipientsRlt/deleteBothRlt", // 修改-删除区块排水户关联
    findLinkSewageUser: basicUrl + "/sewageUser/findLinkSewageUser", // 修改-获取当前接户井已经关联的排水户或在建工地
    deleteLinkSewageUser: basicUrl + "/sewageUser/deleteLinkSewageUser", // 修改-删除当前接户井已经关联的一个(排水户或在建工地)
    findSpoutLinkRiver: basicUrl + "/riverAndSpoutAnalyze/findSpoutLinkRiver", // 修改-查询排口关联的河涌
    getPolygonByPoint: basicUrl + "/pipeDailyStats/getPolygonByPoint", // 修改-区块查询
    savePipeUsage: basicUrl + "/recipientsRlt/saveBothRlt", // 修改-挂接区块排水户
    findSpoutLinkRiver: basicUrl + "/riverAndSpoutAnalyze/findSpoutLinkRiver", // 修改-查询排口关联的河涌
    quitOrAddSpoutLinkRiver:
      basicUrl + "/riverAndSpoutAnalyze/quitOrAddSpoutLinkRiver", // 修改-排口关联的河涌
    getFileNameByLayer: basicUrl + "/shapeFieldConfig/getFileNameByLayer", // 获取图层可编辑字段列表

    // 江海区巡检模块
    // 投诉问题管理（江海区巡检模块）
    getIsInAdminExtent: gwxjURL + "/workOrder/getXYAndIsJurisdiction", // 是否管辖内
    getCompliantManageListPage:
      gwxjURL + "/jhList/jhComplaintManagementListPage", // 页面列表
    complaintWorkOrderManagementListPage:
      gwxjURL + "/complaintWorkOrder/managementListPage", // 页面列表(改版)
    complaintWorkOrderEnd: gwxjURL + "/complaintWorkOrder/end", // 关闭工单
    complaintWorkOrderAddEnclosure:
      gwxjURL + "/complaintWorkOrder/addEnclosure", // 追加投诉图片
    compliantReport: gwxjURL + "/complaintWorkOrder/jhStartComplainWf", // 投诉上报（新增）
    resultFeedback: gwxjURL + "/complaintWorkOrder/resultFeedback", // 技术员处理结果反馈
    getFeedbackPic: gwxjURL + "/complaintWorkOrder/getFeedbackPic", // 技术员处理结果反馈
    issueCompliantProblemJHQ: gwxjURL + "/complaintWorkOrder/jhDispatch", // 确认下发
    checkAllAuthority: gwxjURL + "/workOrderHelper/checkAllAuthority", // 确认下发权限识别
    workOrderHelperGetUserDistrict:
      gwxjURL + "/workOrderHelper/getUserDistrict", // 获取用户哪个区
    // 管辖外案件上报（江海区巡检模块）
    getAdminExtendReportListPage:
      gwxjURL + "/jhList/jhWebInAdminExtentReportListPage", // 管辖外案件上报页面列表
    feedbackUrbanManagement:
      gwxjURL + "/inAdminExtentReport/feedbackUrbanManagement", // 管辖外案件110反馈
    // 上报问题管理（江海区巡检模块）
    getProblemReportTotalJHQ: gwxjURL + "/jhListCount/jhReportManagementCount", // 获取上报问题管理页面工单的统计
    submitHyd110Feedback:
      gwxjURL + "/clearHydWorkOrder/jhUrbanManagementFeedback", // 提交清疏反馈城管110
    submitRepair110Feedback:
      gwxjURL + "/engMaiWorkOrder/jhUrbanManagementFeedback", // 提交维修反馈城管110
    getPJListSysDict: gwxjURL + "/workOrderHelper/getPJListSysDict", // 获取所属分公司的字典
    getMultilevelDict: gwxjURL + "/gwxjDict/getMultilevelDict", // 获取设施问题字典
    getExcelExportDict: gwxjURL + "/excelExportDict/listByClassify", // 获取默认导出字典
    getExcelExportDictListByClassifyIn:
      gwxjURL + "/excelExportDict/listByClassifyIn", // 获取巡检工单导出字典
    getIsHasten: gwxjURL + "/workOrder/isHasten", // 待处理-催办
    submitHandledCheck: gwxjURL + "/workOrder/check", // 已处理-核查
    // 共用（江海区巡检模块）
    getInspectionWorkOrderWaitDealListJHQ:
      gwxjURL + "/jhList/jhWebToBeProcessedListPage", // 获取列表(上报问题管理->待处理处理)
    getInspectionWorkOrderDealedListJHQ:
      gwxjURL + "/jhList/jhWebHandledListPage", // 获取列表(上报问题管理->已处理)
    getInspectionWorkOrderDispatchedListJHQ:
      gwxjURL + "/jhList/jhWebToBeDispatchedListPage", // 获取列表(上报问题管理->待分派)
    getAllDistinct2:
      gwxjURL + "/workOrderHelper/getHandleDistinctAndAccommodation", //下发单位的字典
    submitExtendPhoneFeedbackJHQ:
      gwxjURL + "/complaintWorkOrder/jhCaseReturnCityManagement", // 大流程(管辖外->电话通知、案件返回至城管110)
    getJHQAllDistinct: gwxjURL + "/workOrderHelper/getJHSysDict", // 获取江海字典
    getInspectionModuleDict: gwxjURL + "/workOrderHelper/getSysDict", // 获取巡检模块字典
    getWorkOrderDetail: gwxjURL + "/workOrder/getWorkOrderDetail", // 获取详情(即获取流程信息)
    commonApprove: gwxjURL + "/commonApprove/get", // 根据approvalId获取当前流程详情
    engDispatch: gwxjURL + "/engMaiWorkOrder/dispatch", // 维修上报分派
    clearHydDispatch: gwxjURL + "/clearHydWorkOrder/dispatch", // 清疏上报分派
    engRecallDispatch: gwxjURL + "/engMaiWorkOrder/continue", // 维修上报撤回分派
    clearHydRecallDispatch: gwxjURL + "/clearHydWorkOrder/continue", // 维修上报撤回分派
    // 巡查记录查看
    dailyPatrolDayList: gwxjURL + "/dailyPatrolDay/list", // 日常巡检上报主表
    dailyPatrolList: gwxjURL + "/dailyPatrol/list", // 日常巡检上报子表
    dailyPatrolFindLstXAndY: gwxjURL + "/dailyPatrol/findLstXAndY", // 日常巡检获取巡查轨迹
    // 工单设施统计，蓬江和江海共用
    monthlyFacilityStatistics:
      gwxjURL + "/workOrderHelper/monthlyFacilityStatistics", // 月度设施统计
    customMonthlyFacilityStatistics:
      gwxjURL + "/workOrderHelper/customMonthlyFacilityStatistics", // 自定义设施统计
    workOrderLedgerListPage: gwxjURL + "/pjList/workOrderLedgerListPage", // 工单台账
    facilityPatrolLedgerListPage: gwxjURL + "/facilityPatrol/ledgerListPage", // 工单台账-新
    getTownStreet: gwxjURL + "/workOrderHelper/getUserTownStreet", // 获取所属街道
    exportWorkOrderExcel: gwxjURL + "/workOrderHelper/workOrderLedgerExport", // 工单台账导出
    ledgerListExcelExport: gwxjURL + "/facilityPatrol/ledgerListExcelExport", // 工单台账导出-新
    workOrderDelete: gwxjURL + "/workOrder/deleteById", // 问题上报--删除工单
    getTownStreetDistrict: gwxjURL + "/workOrderHelper/getTownStreet", // 根据分公司获取所属街道
    exportPersonHistory: gwxjURL + "/maintUserAttend/exportPersonHistory", // 巡查轨迹回放--导出
    jhProblemImportEngMai: gwxjURL + '/engMaiWorkOrder/jhProblemImportEngMai',//江海_维修上报

    //排水户
    sewUserListPage: gwxjURL + "/sewUser/listPage", // 排水户列表
    sewUserQueryByCuuid: gwxjURL + "/sewUser/queryByCuuid", // 排水户列表(详情)
    workOrderHelperGetSysDict: gwxjURL + "/workOrderHelper/getSysDict", // 排水户列表(字典)
    sewUserSaveSewUser: gwxjURL + "/sewUser/saveSewUser", // 排水户列表(修改)
    sewUserDelete: gwxjURL + "/sewUser/delete", // 排水户列表(删除)
    getTemplateByFileName: basicUrl + "/sewageUser/getTemplateByFileName", //下载排水户模板
    inputExcelData: basicUrl + "/sewageUser/inputExcelData", //导入排水户Excel文件数据
    getSewageUserPageList: basicUrl + "/sewageUser/getSewageUserPageList", //获取排水户列表
    getSewageInitDirt: basicUrl + "/sewageUser/initDirt", //获取排水户字典
    deleteSewageByCuuid: basicUrl + "/sewageUser/deleteByCuuid", //删除排水户
    updateSewageUser: basicUrl + "/sewageUser/saveOrUpdateSewageUser", //保持和编辑排水户
    saveSewageUserUploadFile: basicUrl + "/sewageUser/saveUploadFileByCuuid", //保存附件
    sewUserExportExcel: gwxjURL + "/sewUser/exportExcel", // 排水户导出
    getSewUserBycuuid: gwxjURL + "/sewUser/getSewUserBycuuid", // 获取排水户信息
    sewageFlowAnalysis: basicUrl + "/pipeAnalyze/sewageFlowAnalysis", // 排水户数量数据统计
    sewUserCheckList: gwxjURL + "/sewUserCheck/listPage", // 排水户审批列表
    sewUserCheckDetail: gwxjURL + "/sewUserCheck/getDetail", // 排水户审批流程详情
    managerApprove: gwxjURL + "/sewUser/managerApprove", // 经办人提交审批
    headUnitApprove: gwxjURL + "/sewUser/headUnitApprove", // 股室负责人提交审批
    leaderApprove: gwxjURL + "/sewUser/leaderApprove", // 分管领导提交审批

    // 排水户巡检
    getWebLedgerListPage: gwxjURL + "/breakLawWorkOrder/webLedgerListPage", // 获取工单台账列表
    getWebHandledListPage: gwxjURL + "/breakLawWorkOrder/webHandledListPage", // 获取已处理列表
    getWebHandlingListPage: gwxjURL + "/breakLawWorkOrder/webHandlingListPage", // 获取待处理列表
    getWebProblemCount: gwxjURL + "/breakLawWorkOrder/webCount", // 获取问题上报统计
    getCustomizeSysDict: gwxjURL + "/workOrderHelper/getCustomizeSysDict", // 排水户巡查获取获取字典数据
    getReportDict: gwxjURL + "/problemImport/getReportDict", // 获取设施类型字典
    editWorkOrderDrainer: gwxjURL + "/breakLawWorkOrder/drainersImport", // 排水户工单修改
    editWorkOrderConstruct: gwxjURL + "/breakLawWorkOrder/breakLwaImport", // 在建工地工单修改
    exportDraingeInspectionWorkOrder:
      gwxjURL + "/breakLawWorkOrder/webLedgerExport", // 工单台账导出
    delExternalConstructionImport:
      gwxjURL + "/externalConstructionImport/deleteByObjectId", // 外部施工删除
    delProblemImport: gwxjURL + "/problemImport/deleteByObjectId", // 问题上报删除
    closeExternalWorkOrder:
      gwxjURL + "/externalConstructionImport/closeWorkOrder", // 通过id 结束工单
    closeProblemWorkOrder: gwxjURL + "/problemImport/closeWorkOrder", // 通过id 结束工单
    modifyWorkingHours: gwxjURL + "/pumpPatrol/modifyWorkingHours", // 修改工时数

    // 排口
    getSpoutPageManage: basicUrl + "/riverAndSpoutAnalyze/spoutPageManage", // 排口管理列表
    saveSpoutPageManage: basicUrl + "/riverAndSpoutAnalyze/spoutEditHandle", // 排口详情保存
    getpjSpoutReportListPage: gwxjURL + "/pjList/pjSpoutReportListPage", // 排口无问题列表
    getSpoutReportDict: gwxjURL + "/workOrderHelper/getSysDict", // 排口字典
    saveSpoutReport: gwxjURL + "/spoutWorkOrder/spoutImport", // 保存排口上报详情
    spoutImgUpload: gwxjURL + "/uploadFile/batchUploadFile", // 上传图片
    getspoutFileList: gwxjURL + "/uploadFile/getUploadFilesByBizId", // 获取图片
    getspoutEmgFileUrl: gwxjURL + "/uploadFile/downloadFileById", // 根据附件id获取附件地址
    getWorkOrderDetail: gwxjURL + "/workOrder/getWorkOrderDetail", // 获取详情(即获取流程信息)
    getDailyPatrolDayList: gwxjURL + "/dailyPatrolDay/list", // 排口通用巡检记录列表(主表)
    getDailyPatrolList: gwxjURL + "/dailyPatrol/list", // 排口通用巡检记录列表(子表)
    riverPageManage: basicUrl + "/riverAndSpoutAnalyze/riverPageManage", // 河涌管理
    spoutPageManage: basicUrl + "/riverAndSpoutAnalyze/spoutPageManage", // 排口管理
    spoutEditHandle: basicUrl + "/riverAndSpoutAnalyze/spoutEditHandle", // 排口编辑
    getDistrictAndSortAndName:
      basicUrl + "/riverAndSpoutAnalyze/getDistrictAndSortAndName", // 排口字典
    spoutWorkOrderListPage: gwxjURL + "/workOrder/spoutWorkOrderListPage", // 排口上报列表
    riverWorkOrderListPage: gwxjURL + "/workOrder/riverWorkOrderListPage", // 河涌上报列表
    spoutWorkOrderSave: gwxjURL + "/workOrder/spoutWorkOrderSave", // 排口上报
    getUploadFileByDate: gwxjURL + "/spoutWorkOrder/getUploadFileByDate", // 地图点击排口获取图片
    getAllDistrictOrg: gwxjURL + "/workOrder/getAllDistrictOrg", // 字典->所属街道
    getWorkOrderSysDict: gwxjURL + "/workOrder/getSysDict", // 字典->所有字典
    workOrderIsHasten: gwxjURL + "/workOrder/isHasten", // 问题上报--催办
    getUserDistrictOrg: gwxjURL + "/workOrder/getUserDistrictOrg", // 获取当前登录用户的片区

    // 计划保养管理
    getAllDistinct: gwxjURL + "/workOrderHelper/getAllDistinct", //下发单位的字典
    maintenancePlanList: gwxjURL + "/maintenancePlan/list", // 养护计划(列表)
    maintenancePlanSave: gwxjURL + "/maintenancePlan/save", // 养护计划(新增/审批)
    maintenancePlanDelete: gwxjURL + "/maintenancePlan/delete", // 养护计划(删除)
    channelPunchPointList: gwxjURL + "/channelPunchPoint/list", // 河道打卡点列表
    channelPunchPointDetele: gwxjURL + "/channelPunchPoint/delete", // 河道打卡点删除
    channelPunchPointSave: gwxjURL + "/channelPunchPoint/save", // 河道打卡点新增/修改

    getPersonNotes: gwxjURL + "/maintUserAttend/findNotesByMainUserAttendId", // 获取日常巡检记录
    getTownStreetDict: gwxjURL + "/maintUserAttend/getTownStreetDict", // 巡检记录获取镇街信息

    //安全生产
    safeProdDownloadFileById:
      safeProdUrl + "/safeProdUploadFile/downloadFileById", //下载安全生产系统附件
    obtainSafeProdPicStream:
      safeProdUrl + "/safeProdUploadFile/obtainPicStream", //获取安全生产图片数据流
    loadProjectInfList: safeProdUrl + "/projectInfo/projectList", //获取项目申报列表
    getProjectDict: safeProdUrl + "/projectInfo/getProjectDict", //获取项目字典信息
    saveProject: safeProdUrl + "/projectInfo/saveProject", //信息申报-新增
    updateProject: safeProdUrl + "/projectInfo/updateProject", //信息申报-修改
    getUserOrgDepartment: safeProdUrl + "/infoDeclare/getUserOrgDepartment", //信息申报-修改
    deleteUploadFile: safeProdUrl + "/safeProdUploadFile/deleteUploadFile", // 信息申报-删除附件
    declareDisposeList: safeProdUrl + "/projectPumpAudit/declareDisposeList", //信息申报-申报处理-获取列表
    getOneInfoProject: safeProdUrl + "/projectInfo/getOneInfo", //信息申报-项目申报-查询单个项目信息
    getOneInfoPump: safeProdUrl + "/pumpStationInfo/getOneInfo", //信息申报-项目申报-查询单个泵站信息
    projectPumpDeclareAudit:
      safeProdUrl + "/projectPumpAudit/projectPumpDeclareAudit", //信息申报-项目申报-查询单个泵站信息
    getUserOrgId: safeProdUrl + "/dailyPerformCheck/getUserByCompany", //获取指定机构的人员信息
    loadCheckHistory: safeProdUrl + "/dailyPerformCheck/getAllUserCheckHistory", //获取所有用户的打卡记录列表
    exportCheckHistory: safeProdUrl + "/dailyPerformCheck/exportCheckHistory", //导出打卡记录列表
    loadCompanysInfo: safeProdUrl + "/dailyPerformCheck/getCheckStatisticsDict", //获取人员履职统计页面字典信息
    loadCheckStatistics: safeProdUrl + "/dailyPerformCheck/getCheckStatistics", //获取人员履职统计信息
    getWorkDynamicDict: safeProdUrl + "/dailyPerformCheck/getWorkDynamicDict", //获取每日报告-工作动态页面字典信息
    getNotCheckProjectDetails:
      safeProdUrl + "/dailyPerformCheck/getNotCheckProjectDetails", //获取每日报告-未履职列表
    getUserCheckDetails: safeProdUrl + "/dailyPerformCheck/getUserCheckDetails", //获取每日报告-人员履职清单
    userCheckDetailsExportExcel:
      safeProdUrl + "/dailyPerformCheck/userCheckDetailsExportExcel", //获取每日报告-人员履职清单导出
    getCheckProRltStatistics:
      safeProdUrl + "/personProRltStatistics/getCheckProRltStatistics", //每日一报-履职清单履职详情
    exportCheckProRltStat:
      safeProdUrl + "/personProRltStatistics/exportCheckProRltStat", //每日一报-履职清单履职详情导出
    getUserTodayReport: safeProdUrl + "/dailyReport/getUserTodayReport", //每日报告菜单进入
    getDailyReportDict: safeProdUrl + "/dailyReport/getDailyReportDict", //每日报告相关信息获取
    svaeDailyReport: safeProdUrl + "/dailyReport/svaeDailyReport", //每日报告保存
    getDispatchUsers: safeProdUrl + "/dailyReportDispatch/getDispatchUsers", //每日报告-获取指派名单
    saveReportDispatch: safeProdUrl + "/dailyReportDispatch/saveReportDispatch", //每日报告-指派
    getDailyReportLedgerList: safeProdUrl + "/dailyReport/list", //每日报告台账-获取列表
    dailyReportExportExcel: safeProdUrl + "/dailyReport/dailyReportExportExcel", //每日报告台账-表格导出
    getDailyReportDetails: safeProdUrl + "/dailyReport/getDailyReportDetails", //每日一报-每日报告清单
    dailyReportDetailsExportExcel:
      safeProdUrl + "/dailyReport/dailyReportDetailsExportExcel", //每日一报-每日报告清单导出
    getCheckHistory: safeProdUrl + "/dailyPerformCheck/getCheckHistory", //获取打卡记录列表
    deleteCheckHistory: safeProdUrl + "/dailyPerformCheck/delete", //删除打卡记录列表
    modifyUpdateStatus: safeProdUrl + "/dailyPerformCheck/modifyUpdateStatus", //修改打卡记录能否更新
    getPipeRelateInfo: safeProdUrl + "/pipeMaintInfo/getPipeRelateInfo", //获取管网相关信息
    addGateSationInfo: safeProdUrl + "/gateStationInfo/addGateSationInfo", //新增闸站相关信息
    addPipeMainInfo: safeProdUrl + "/pipeMaintInfo/addPipeMainInfo", //新增管网信息
    getScreenNextDayPlan: safeProdUrl + "/nextDayPlan/getScreenNextDayPlan", //次日计划-查询
    getOneNextDayPlanInfo: safeProdUrl + "/nextDayPlan/getOneNextDayPlanInfo", //次日计划-根据id获取详情
    getNextDayPlanDict: safeProdUrl + "/nextDayPlan/getNextDayPlanDict", //次日计划-筛选字典
    getAllNextPlanDict: safeProdUrl + "/nextDayPlan/getAllNextPlanDict", //次日计划-查询字典
    saveNextDayPlan: safeProdUrl + "/nextDayPlan/saveNextDayPlan", //次日计划-新增
    saveNextDayPlanCustomProject:
      safeProdUrl + "/nextDayPlan/saveNextDayPlanCustomProject", //次日计划-新增自定义计划
    exportNextDayPlan: safeProdUrl + "/nextDayPlan/exportNextDayPlan", //次日计划-导出次日计划
    checkDispatchListPage:
      safeProdUrl + "/dailyPerformCheckDispatch/checkDispatchListPage", //打卡调度记录
    exportCheckDispatchList:
      safeProdUrl + "/dailyPerformCheckDispatch/exportCheckDispatchList", //导出打卡调度记录
    deleteDheckDispatch: safeProdUrl + "/dailyPerformCheckDispatch/delete", //打卡调度记录
    loadCompanysInfoByReportStatic:
      safeProdUrl + "/riskEliminateReport/riskEliminateStatisticDict", //获取隐患排查治理统计页面字典信息
    loadReportStatistics:
      safeProdUrl + "/riskEliminateReport/riskEliminateStatistic", //获取隐患排查治理统计信息
    getSysDictInfo: safeProdUrl + "/riskEliminateReport/getSysDictInfo", //隐患上报-获取隐患字典
    getProjectAndUser: safeProdUrl + "/riskEliminateReport/getProjectAndUser", //隐患上报-通过项目id获取整改人
    commitRiskInfo: safeProdUrl + "/riskEliminateReport/commitRiskInfo", // 隐患上报-提交隐患
    updateRiskInfo: safeProdUrl + "/riskEliminateReport/updateRiskInfo", // 我的上报-修改隐患
    batchUploadFile: safeProdUrl + "/uploadFile/batchUploadFile", // 隐患上报-隐患图片上传
    getDictInfo: safeProdUrl + "/riskEliminateReportDict/getDictInfo", // 隐患字典-获取隐患类型、分级、描述字典
    getAllRiskReportAbout:
      safeProdUrl + "/riskEliminateReport/getAllRiskReportAbout", // 隐患台账-获取台账列表
    commitRiskEliminateDispose:
      safeProdUrl + "/riskEliminateReport/commitRiskEliminateDispose", // 整改处理-待整改-确认整改
    extension: safeProdUrl + "/extensionRequest/extension", // 整改处理-待整改-提交延期申请
    extensionAudit: safeProdUrl + "/extensionRequestAudit/extensionAudit", // 整改处理-待延期审批-提交延期申请
    rectifyDisposeList: safeProdUrl + "/riskEliminateReport/rectifyDisposeList", // 整改处理-获取列表
    getRiskById: safeProdUrl + "/riskEliminateReport/getOneById", // 获取通过id获取隐患数据
    riskReportAudit: safeProdUrl + "/riskEliminateDisposeAudit/riskReportAudit", // 整改处理-待审批-审批（通过/不通过）
    getAssignUserList:
      safeProdUrl + "/riskEliminateDisposeAudit/getAssignUserList", // 隐患相关流程指定审核人时选择获取用户列表
    assignRiskAuditUsers:
      safeProdUrl + "/riskEliminateDisposeAudit/assignRiskAuditUsers", // 隐患相关流程指定审核人
    getAllMyHaseDone: safeProdUrl + "/riskEliminateReport/getAllMyHaseDone", // 我的处理-获取列表
    loadReportPageDict:
      safeProdUrl + "/riskEliminateReportDict/loadReportPageDict", // 上报字典管理-获取页面字典选项
    loadNextPlanPageDict:
      safeProdUrl + "/riskEliminateReportDict/loadNextPlanPageDict", // 每日一报-次日计划字典选项
    loadDictList: safeProdUrl + "/riskEliminateReportDict/getDictInfo", // 上报字典管理-获取字典列表
    saveReportDict: safeProdUrl + "/riskEliminateReportDict/save", // 上报字典管理-保存上报字典项
    delReportDict: safeProdUrl + "/riskEliminateReportDict/delete", // 上报字典管理-删除上报字典项
    getRelateInfo: safeProdUrl + "/riskEliminateReportDictRelate/getRelateInfo", // 上报字典关联-获取关联信息-通过字典编号
    commitPumpStationInfo:
      safeProdUrl + "/pumpStationInfo/commitPumpStationInfo", // 新增泵站
    updatePumpStationInfo:
      safeProdUrl + "/pumpStationInfo/updatePumpStationInfo", // 修改泵站信息
    findAllPumpDuty: safeProdUrl + "/pumpStationInfo/findAllPumpDuty", // 泵站台账
    getAllBranchCompany: safeProdUrl + "/pumpStationInfo/getAllBranchCompany", // 泵站-获取分公司
    getBranchAllPumpUser: safeProdUrl + "/pumpStationInfo/getBranchAllPumpUser", // 获取分公司下泵站所有人
    getPumpStationTypeDict:
      safeProdUrl + "/pumpStationInfo/getPumpStationTypeDict", // 泵站台账-获取泵站类型
    getAllMyReport: safeProdUrl + "/riskEliminateReport/getAllMyReport", // 我的上报-获取列表
    getReportOperation:
      safeProdUrl + "/riskEliminateOperation/getReportOperation", // 获取隐患整改流程列表
    getProjectUserAndCompanyInfo:
      safeProdUrl + "/projectInfo/getProjectUserAndCompanyInfo", // 获取当前用户和所在公司信息
    findAllUserAndCompanyInfo:
      safeProdUrl + "/projectInfo/findAllUserAndCompanyInfo", // 获取当前用户和所在公司信息(如果是总公司，展示全部)
    getAllScreenReportInfo:
      safeProdUrl + "/riskEliminateReport/getAllScreenReportInfo", // 隐患台账-查询
    getAllMyScreenReportInfo:
      safeProdUrl + "/riskEliminateReport/getAllMyScreenReportInfo", // 我的上报-查询
    getAllMyDoneScreenReportInfo:
      safeProdUrl + "/riskEliminateReport/getAllMyDoneScreenReportInfo", // 我的处理-查询
    getScreenDict: safeProdUrl + "/riskEliminateReport/getScreenDict", // 隐患查询条件列表字典
    getMyDeclareList: safeProdUrl + "/infoDeclare/getMyDeclareList", // 我的申报-获取申报列表
    getInfoPageList: safeProdUrl + "/infoDeclare/getInfoPageList", // 台账获取
    exportInfoToExcel: safeProdUrl + "/infoDeclare/exportInfoToExcel", //表格导出
    getOnePipeMain: safeProdUrl + "/pipeMaintInfo/getOnePipeMain", //根据id获取管网信息
    updatePipeMainInfo: safeProdUrl + "/pipeMaintInfo/updatePipeMainInfo", //修改管网信息
    getOneGateById: safeProdUrl + "/gateStationInfo/getOneGateById", //根据id获取闸站信息
    updateGateSationInfo: safeProdUrl + "/gateStationInfo/updateGateSationInfo", //修改闸站信息
    infoDeclareAudit: safeProdUrl + "/projectPumpAudit/infoDeclareAudit", //信息审核
    getProjectProcess: safeProdUrl + "/projectPumpAudit/getProjectProcess", //获取项目审核流程
    getOneInfo: safeProdUrl + "/operateLog/getOneInfo", //获取项目审核修改记录
    riskReportExportToExcel:
      safeProdUrl + "/riskEliminateReport/riskReportExportToExcel", //隐患台账导出
    downloadExcelTemplate:
      safeProdUrl + "/onlineExamSubject/downloadExcelTemplate", //下载题目模板
    getRelateInfoByDictId:
      safeProdUrl + "/riskEliminateReportDictRelate/getRelateInfoByDictId", // 上报字典关联-获取关联信息-通过字典编号
    saveReportRelateDict: safeProdUrl + "/riskEliminateReportDictRelate/save", // 上报字典关联-保存字典关联记录
    deleteReportRelateDict:
      safeProdUrl + "/riskEliminateReportDictRelate/delete", // 上报字典关联-删除字典关联记录
    safeProductionSystemStatistic:
      safeProdUrl + "/systemUsageStatistic/safeProductionSystemStatistic", // 分析统计-使用分析
    safeProductionUserActivityStatistic:
      safeProdUrl + "/systemUsageStatistic/safeProductionUserActivityStatistic", // 分析统计-各单位活跃度分析
    webIndexStatistic: safeProdUrl + "/systemUsageStatistic/webIndexStatistic", // 首页-获取统计数据
    examSituationStatistics:
      safeProdUrl + "/onlineExamAnswerResult/examSituationStatistics", //分析统计-学习统计分析
    getAllGroupData: safeProdUrl + "/onlineExamSubjectGroup/getAllGroupData", //分析统计-获取题库名称
    getUserComanyLimit: safeProdUrl + "/infoDeclare/getUserComanyLimit", //分析统计-获取公司和用户
    loadOnlineStudyList: safeProdUrl + "/onlineStudy/onlineStudyPage", //在线学习-获取在线学习列表内容
    saveOnlineStudy: safeProdUrl + "/onlineStudy/onlineStudySaveOrUpdate", //在线学习-新增修改在线学习列表内容
    deleteOnlineStudy: safeProdUrl + "/onlineStudy/delete", //在线学习-删除在线学习内容
    getAllRelateLinks: safeProdUrl + "/relateLinks/getAllRelateLinks", //在线学习-获取在线学习相关连接列表
    saveRelateLink: safeProdUrl + "/relateLinks/save", //在线学习-保存在线学习相关连接
    deleteRelateLink: safeProdUrl + "/relateLinks/delete", //在线学习-删除在线学习相关连接
    saveParameterConfig:
      safeProdUrl + "/onlineStudyConfig/saveOrUpdateStudyConfig", //在线学习-保存在线学习参数配置
    loadParameterConfig: safeProdUrl + "/onlineStudyConfig/getStudyConfig", //在线学习-获取在线学习参数配置
    updateStudyDuration:
      safeProdUrl + "/onlineStudyTimeRecord/saveOnceStudyTime", //在线学习-更新阅读时长
    loadUserStudyDuration: safeProdUrl + "/onlineStudyFullRank/getAllStudyTime", //在线学习-获取用户总阅读时长
    loadUserReport: safeProdUrl + "/onlineStudyTimeRecord/getUserReport", //在线学习-获取用户个人学习报表
    loadTopRankList: safeProdUrl + "/onlineStudyTimeRecord/getTopRankList", //在线学习-获取学习时长排行榜新
    updateSubjectGroupConfig:
      safeProdUrl + "/onlineExamSubjectGroup/updateSubjectGroupConfig", //在线考试-专项设置-保存、修改
    addOrUpdateSpecialGroup:
      safeProdUrl + "/onlineExamSubjectGroup/addOrUpdateSpecialGroup", //在线考试-专项-新增、修改
    deleteSubjectGroup: safeProdUrl + "/onlineExamSubjectGroup/delete", //在线考试-题组信息-删除(即专项信息)
    getAllExamLabelList: safeProdUrl + "/onlineExamLabel/getAllExamLabelList", //在线考试配置-总题库-获取标签列表
    deleteLabel: safeProdUrl + "/onlineExamLabel/delete", //在线考试配置-总题库-删除标签
    saveOrUpdateExamLabel:
      safeProdUrl + "/onlineExamLabel/saveOrUpdateExamLabel", //在线考试配置-总题库-保存或更新标签
    getSubjectDict: safeProdUrl + "/onlineExamSubject/getSubjectDict", //在线考试-试题相关信息获取
    getContestSubjectRelate:
      safeProdUrl + "/contestAnswerSubjectGroup/getContestSubjectRelate", //在线考试-获取竞赛抢答相关信息
    getAllExamSubjectIdList:
      safeProdUrl + "/onlineExamSubject/getAllExamSubjectIdList", //在线考试-获取题目列表-条件筛选
    getOneSubjectInfo: safeProdUrl + "/onlineExamSubject/getOneSubjectInfo", //在线考试-获取单个题目详情
    deleteSubjectByIds: safeProdUrl + "/onlineExamSubject/deleteByIds", //在线考试-删除题目
    addSubjectsToGroup: safeProdUrl + "/onlineExamSubject/addSubjectsToGroup", //在线考试-批量加入题库
    saveOrUpdateSubject: safeProdUrl + "/onlineExamSubject/saveOrUpdateSubject", //在线考试-题目信息-保存或修改
    saveFromExcelData: safeProdUrl + "/onlineExamSubject/saveFromExcelData", //在线考试-题目信息-批量导入
    getSubjectGroupByType:
      safeProdUrl + "/onlineExamSubjectGroup/getSubjectGroupByType", //在线考试-根据题组类型获取题组详细信息
    delGroupSubject:
      safeProdUrl + "/onlinEexamSubjectGroupRelate/delGroupSubject", //在线考试-移出题库
    getUserScoreInfo: safeProdUrl + "/onlineExamFullScore/getUserScoreInfo", //在线考试-个人积分情况
    getScoreTopRankInfo:
      safeProdUrl + "/onlineExamFullScore/getScoreTopRankInfo", //在线考试-积分排行榜列表获取
    getWeekGroupAndUserGrade:
      safeProdUrl + "/onlineExamSubjectGroup/getWeekGroupAndUserGrade", //在线考试-获取每周答题个人成绩信息
    getExamGroupAnwserData:
      safeProdUrl + "/onlineExamSubjectGroup/getExamGroupAnwserData", //在线考试-获取每周和专项答题题目
    getContestAnswerSubject:
      safeProdUrl + "/contestAnswerSubjectGroup/getContestAnswerSubject", //在线考试-获取竞赛抢答答题题目
    saveExamAnswerResult:
      safeProdUrl + "/onlineExamAnswerResult/saveExamAnswerResult", //在线考试-答案提交
    saveContestAnswerResult:
      safeProdUrl + "/contestAnswerResult/saveContestAnswerResult", //在线考试-竞赛抢答答案提交
    getSpecialGroupAndUserGrade:
      safeProdUrl + "/onlineExamSubjectGroup/getSpecialGroupAndUserGrade", //在线考试-获取专项答题页面数据分页
    userContestAnswerPageList:
      safeProdUrl + "/contestAnswerSubjectGroup/userContestAnswerPageList", //在线考试-获取竞赛抢答答题页面数据分页
    contestAnswerConfigPageList:
      safeProdUrl + "/contestAnswerSubjectGroup/contestAnswerConfigPageList", //在线考试-获取竞赛抢答列表数据
    deleteContestAnswerSubjectGroup:
      safeProdUrl + "/contestAnswerSubjectGroup/deleteById", //在线考试-删除竞赛抢答题库
    getContestSubjectGroupById:
      safeProdUrl + "/contestAnswerSubjectGroup/getContestSubjectGroupById", //在线考试-获取单个竞赛题库信息
    getSpecailTopRankList:
      safeProdUrl + "/onlineExamAnswerResult/getSpecailTopRankList", //在线考试-获取专项题库分数排行榜前五十
    getContestAnswerTopRankList:
      safeProdUrl + "/contestAnswerResult/getTopRankList", //在线考试-获取竞赛题库分数排行榜前五十
    getExamConfigData: safeProdUrl + "/onlineExamConfig/getExamConfigData", //在线考试-获取参数配置
    saveOrUpdateExamConfig:
      safeProdUrl + "/onlineExamConfig/saveOrUpdateExamConfig", //在线考试-保存参数配置
    saveOrUpdateSubejctGroup:
      safeProdUrl + "/contestAnswerSubjectGroup/saveOrUpdateSubejctGroup", //在线考试-新增或保存竞赛抢答题库
    userAnswerResultList:
      safeProdUrl + "/onlineExamAnswerResult/userAnswerResultList", //在线考试-获取学习培训详情
    getRiskAndProjectAuditNum:
      safeProdUrl + "/systemUsageStatistic/getRiskAndProjectAuditNum", //隐患审批和项目审批数量获取
    saveLimtspWorkInfo: safeProdUrl + "/limtspWorkInfo/save", //有限空间作业信息保存
    deleteLimtspWorkInfo: safeProdUrl + "/limtspWorkInfo/delete", //有限空间作业信息删除
    saveCloseRemark: safeProdUrl + "/limtspWorkInfo/saveCloseRemark", //有限空间作业信息删除
    getWorkDict: safeProdUrl + "/limtspWorkInfo/getWorkDict", //有限空间作业安全措施字典
    workPageList: safeProdUrl + "/limtspWorkInfo/workPageList", //获取有限空间作业台账
    workListExport: safeProdUrl + "/limtspWorkInfo/workListExport", //获取有限空间作业台账导出
    supervisionPageList: safeProdUrl + "/limtspSupervision/supervisionPageList", //获取督察台账
    supervisionListExport:
      safeProdUrl + "/limtspSupervision/supervisionListExport", //获取有限空间作业督察台账导出
    checkFileExists: safeProdUrl + "/safeProdUploadFile/checkFileExists", //判断导出文件是否存在
    safeProdDownloadFileByPath:
      safeProdUrl + "/safeProdUploadFile/downloadFileByPath", //安全生产下载

    /**workPageList
     * 泵站巡检
     * **/
    deletePump: pumpUrl + "/pumpPatrol/delete", // 删除泵站问题上报列表
    listMaintTeamOfPump: pumpUrl + "/maintTeam/listWithConfigOfPump",
    listPersonnel: pumpUrl + "/maintTeamUserRelate/getTeamPersonnel",
    listAddUserOfPump: pumpUrl + "/maintTeam/getAddUserOfPump",
    saveMaintTeamOfPump: pumpUrl + "/maintTeam/saveMaintTeamOfPump",
    appointCaptain: pumpUrl + "/maintTeamUserRelate/appointCaptain",
    deleteMaintTeam: pumpUrl + "/maintTeam/deleteContainRelate",
    getRetroactiveAnalysis: pumpUrl + "/pipeAnalyze/flowConnectAnalysis",
    listPlantRecord: pumpUrl + "/plantsInfo/listWithConfig",
    delPlantRecord: pumpUrl + "/plantsInfo/delete",
    savePlants: pumpUrl + "/plantsInfo/save",
    findAllPlantsInfo: pumpUrl + "/plantsInfo/findAllPlantsInfo",
    getOnePlantsReportInfo: pumpUrl + "/plantsInfo/getOnePlantsReportInfo",
    loadReportRecord: pumpUrl + "/inspectReport/listWithConfig",
    obtainInspectorInfo: pumpUrl + "/inspectReport/obtainInspectorInfo",
    getPumpListByToken: pumpUrl + "/cleanMaintainProblem/getPumpListByToken", //获取泵站列表
    dispatchInspectReport: pumpUrl + "/inspectReport/dispatchInspectReport",
    findAllFacilityInfo: pumpUrl + "/plantsFacilityInfo/findAllFacilityInfo",
    savePlantsFacility: pumpUrl + "/plantsFacilityInfo/save",
    delPlantFacilityRecord: pumpUrl + "/plantsFacilityInfo/delete",
    getInspectYearPlanList: pumpUrl + "/inspectYearPlan/listWithConfig",
    findInitialParameter: pumpUrl + "/inspectYearPlan/findInitialParameter",
    validateYearOfPlants: pumpUrl + "/inspectYearPlan/validateYearOfPlants",
    saveInspectYearPlan: pumpUrl + "/inspectYearPlan/saveInspectYearPlan",
    deleteInspectYearPlan: pumpUrl + "/inspectYearPlan/deleteYearPlan",
    getInspectMonthPlanList: pumpUrl + "/inspectMonthPlan/listWithConfig",
    findMonthInitialParameter:
      pumpUrl + "/inspectMonthPlan/findInitialParameter",
    saveInspectMonthPlan: pumpUrl + "/inspectMonthPlan/saveInspectMonthPlan",
    passPlantsYearPlan: pumpUrl + "/inspectYearPlan/passYearPlan",
    findPlantsOpinionList: pumpUrl + "/plantsApprovaOpinion/findOpinionList",
    savePlantsApprovaOpinion:
      pumpUrl + "/plantsApprovaOpinion/saveApprovaOpinion",
    delPlantsApprovaOpinion:
      pumpUrl + "/plantsApprovaOpinion/deleteApprovaOpinion",
    denyPlantsYearPlan: pumpUrl + "/inspectYearPlan/denyYearPlan",
    passPlantsMonthPlan: pumpUrl + "/inspectMonthPlan/passMonthPlan",
    denyPlantsMonthPlan: pumpUrl + "/inspectMonthPlan/denyMonthPlan",
    yearPlanCopyCheck: pumpUrl + "/inspectMonthPlan/inspectYearPlanCopyCheck",
    yearPlanCopy: pumpUrl + "/inspectMonthPlan/inspectYearPlanCopy",
    monthPlanCopyCheck: pumpUrl + "/inspectMonthPlan/inspectMonthPlanCopyCheck",
    monthPlanCopy: pumpUrl + "/inspectMonthPlan/inspectMonthPlanCopy",
    //导出一雨一报word文档
    exportRainRepToWord: pumpUrl + "/rainsRecord/exportDoc",
    //导出事中报告word文档
    exportProcessRepToWord: pumpUrl + "/processRecord/exportDoc",
    //巡检记录管理
    getDailyInspectList: pumpUrl + "/dailyInspect/listWithConfig",
    getPumpActivateLogList: pumpUrl + "/pumpActivateLog/listWithConfig",
    getDetailOfDailyInspect:
      pumpUrl + "/dailyInspectDetail/getDetailOfDailyInspect",
    deleteDailyInspect: pumpUrl + "/dailyInspect/delete",
    //泵闸巡检值班管理
    getDutyRecordsOfDump: pumpUrl + "/pumpDutyManager/getDutyManagerList",
    saveDutyRecordsOfDump: pumpUrl + "/pumpDutyManager/saveDutyManager",
    getPumpInspectPersonInfo:
      pumpUrl + "/pumpDutyManager/getPumpInspectPersonInfo",
    //泵闸站出勤监测
    listRealTimeMonitorOfPump: pumpUrl + "/maintUserAttend/pumpAttendanceInfo",
    //泵闸站人员考勤列表
    listUserAttendOfPump: pumpUrl + "/maintUserAttend/pumpListWithConfig",
    //泵闸站人员考勤管理
    listUserAttendStaticOfPump:
      pumpUrl + "/maintUserAttendStatic/pumpListWithConfig",
    //泵站用户使用统计
    pumpStatic: pumpUrl + "/pumpStat/statXcDetails",
    //泵站使用统计导出
    exportPumpStatic: pumpUrl + "/pumpStat/exportStatXcDetails",
    //获取泵闸站的设备参数信息
    obtainPlantsFacilityDetailInfo:
      pumpUrl + "/plantsFacilityDetailInfo/obtainPlantsFacilityDetailInfo",
    //保存泵闸站的设备参数信息
    savePlantsFacilityDetailInfo: pumpUrl + "/plantsFacilityDetailInfo/save",
    //删除泵闸站的设备参数信息
    delPlantsFacilityDetailInfo: pumpUrl + "/plantsFacilityDetailInfo/delete",
    //获取泵站监测报警列表
    getAlarmOrWaringDataHistoryByFacilityId:
      pumpUrl + "/alarmOrWarningData/getAlarmOrWaringDataHistoryByFacilityId",
    //获取泵站运行记录
    getPumpWorkingLog: pumpUrl + "/pumpWorkingLog/getPumpWorkingLog",
    //获取泵站水位曲线
    getDataHistoryByItemId: pumpUrl + "/dataHistory/getDglsDataHistoryByItemId",
    //导出日常巡检记录
    dailyInspectionRecordExport:
      pumpUrl + "/dailyInspect/dailyInspectionRecordExport",
    //泵站维保清疏记录
    getListWithConfig: pumpUrl + "/cleanMaintainProblem/listWithConfig",
    //泵站维保清疏记录导出
    exportPumpProblemLedger:
      pumpUrl + "/cleanMaintainProblem/exportPumpProblemLedger",
    //泵闸站巡检问题工单下派核查
    dispatchCheckPumpProblemReport:
      pumpUrl + "/inspectDispose/distributeReportDispose",
    //泵闸站巡检问题工单核查通过
    saveCheckPumpReport: pumpUrl + "/inspectDispose/alreadyCheck",
    //获取泵闸站巡检问题工单的流程记录
    loadPumpProblemInstLog:
      pumpUrl + "/pumpProblemInstLog/findProblemInstLogList",
    //删除指定河道巡查问题工单
    deletePumpReportInfo: pumpUrl + "/inspectReport/deleteReportInfo",
    // 泵站工单台账
    pumpPatrolLedgerListPage: pumpUrl + "/pumpPatrol/pumpPatrolLedger",

    /*
     * 排水设施->管网数据
     * */
    //修补测工单
    getDataUpdateList: basicUrl + "/dataUpdate/list", //获取列表
    deleteDataUpdate: basicUrl + "/dataUpdate/delete", //删除核查表单
    processDataUpdateReport: aeURL + "/dataUpdate/uploadDataFile", //上传修补测文件
    insertGDBFile: aeURL + "/dataUpdate/insertFeatures2DB", //插入修补测数据
    aeUploadFilePsgs: aeURL + "/uploadFile/batchUploadFile", //上传附件
    aeDownloadFilePsgs: aeURL + "/uploadFile/downloadFileById", //下载附件
    downloadFileByFileId: aeURL + "/uploadFile/downloadFileByFileId", //下载附件---新
    asGetUploadFilesByBizId: aeURL + "/uploadFile/getUploadFilesByBizId",
    // 泵站管理
    savePump: gwxjURL + "/pump/savePump", // 新增/编辑泵站
    pumpListPage: gwxjURL + "/pump/listPage", // 获取泵站列表
    countPump: gwxjURL + "/pump/countPump", // 获取泵站概况
    deletePumpById: gwxjURL + "/pump/deleteById", // 删除泵站
    deviceListPage: gwxjURL + "/pumpDevice/listPage", // 查找管线的泵站设备
    savePumpDevice: gwxjURL + "/pump/savePumpDevice", // 新增/编辑设备
    deleteDeviceById: gwxjURL + "/pumpDevice/deleteById", // 删除设备
    maintainListPage: gwxjURL + "/maintainItem/listPage", // 获取养护项目列表
    saveMaintainItem: gwxjURL + "/pumpDevice/saveMaintainItem", // 新增/编辑养护项目
    deleteMaintainById: gwxjURL + "/maintainItem/deleteById", // 删除养护项目
    getDGAllDistinct: gwxjURL + "/workOrderHelper/getDGAllDistinct", // 获取东莞分区字典
    getCustomizeSysDict: gwxjURL + "/workOrderHelper/getCustomizeSysDict", // 获取字典（根据传值决定是什么的字典）
    getDeviceTypeList: gwxjURL + "/deviceTypeDomain/listPage", // 获取设备类型
    pumpDetail: gwxjURL + "/pump/detail", // 泵站详情
    getPumpHandoverStatusDict: gwxjURL + "/pump/getPumpHandoverStatusDict", // 泵站详情获取移交状态字典
    exportPump: gwxjURL + "/pump/exportPump", // 泵站详情获取移交状态字典
    constructionExportExcel:
      gwxjURL + "/externalConstructionImport/exportExcel", // 外部工地台账导出

    // 泵站巡检
    getPumpDealList: gwxjURL + "/pumpPatrol/endList", // 获取已处理工单
    getPumpHandlingList: gwxjURL + "/pumpPatrol/handlingList", // 获取待处理工单
    pumpPatrolManagementCount:
      gwxjURL + "/pumpPatrol/pumpPatrolManagementCount", // 获取工单统计
    getDistributingList: gwxjURL + "/pumpPatrol/getById", // 获取泵站巡检详情
    jumpCadres: gwxjURL + "/pumpPatrol/jumpCadres", // 转本部
    distributePump: gwxjURL + "/pumpPatrol/distribute", // 派发处理
    pumpHandle: gwxjURL + "/pumpPatrol/handle", // 处理登记
    pumpCheck: gwxjURL + "/pumpPatrol/check", // 填写核定信息
    getOwnerUserDict: gwxjURL + "/pumpPatrol/getOwnerUserDict", // 获取下派人员名单
    getAllPumpDict: gwxjURL + "/pump/getAllPumpDict", // 获取所属泵站

    // 泵站巡检计划
    patrolPlanList: gwxjURL + "/patrolPlan/listPage", // 获取月巡检计划列表
    patrolPlanAdd: gwxjURL + "/patrolPlan/save", // 添加月巡检计划
    patrolPlanDelete: gwxjURL + "/patrolPlan/delete", // 删除月巡检计划
    patrolPlanItemList: gwxjURL + "/patrolPlanItem/listPage", // 获取个人巡检计划列表
    patrolPlanItemAdd: gwxjURL + "/patrolPlanItem/save", // 添加个人巡检计划
    patrolPlanItemDelete: gwxjURL + "/patrolPlanItem/delete", // 删除个人巡检计划
    getUserIdDict: gwxjURL + "/orgDict/getUserIdDict", // 获取所属街道
    getUserIdDictV2: gwxjURL + "/orgDict/getUserIdDictV2", // 获取所属街道(新)
    getUserIdDictV3: gwxjURL + "/orgDict/getUserIdDictV3", // 获取所属街道(新)
    patrolPlanItemCopy: gwxjURL + "/patrolPlanItem/copy", // 获取所属街道
    pipeReCheckQueryPipeCanal: basicUrl + "/pipeReCheck/queryPipeCanal", // 获取选中的管线
    pipeReCheckRecheckFlowPipeLine:
      basicUrl + "/pipeReCheck/recheckFlowPipeLine", // 根据起点和终点，获取连接的管线

    // 泵站养护
    getLoginUserDistrict: gwxjURL + "/workOrderHelper/getUserDistrict", // 获取用户哪个区
    maintainPlanDomainListPage: gwxjURL + "/maintainPlanDomain/listPage", // 泵站养护计划列表
    maintainPlanDomainDelete: gwxjURL + "/maintainPlanDomain/delete", // 泵站养护计划删除
    maintainRecordDomainList: gwxjURL + "/maintainRecordDomain/listPageGroupBy", // 泵站养护计划详情
    updateThisMonthMaintain:
      gwxjURL + "/maintainPlanDomain/updateThisMonthMaintain", // 泵站养护计划新增刷数据
    maintainPlanListPage: gwxjURL + "/maintainPlanDomain/maintainPlanListPage", // 泵站养护计划新增列表
    maintainPlanDomainSave: gwxjURL + "/maintainPlanDomain/save", // 泵站养护计划新增保存
    maintainRecordDomainListPage: gwxjURL + "/maintainRecordDomain/listPage", // 泵站养护记录列表
    countAllByMaintainType:
      gwxjURL + "/maintainRecordDomain/countAllByMaintainType", // 泵站养护记录列表统计
    dictDomainGetMaintainTypeDict: gwxjURL + "/dictDomain/getMaintainTypeDict", // 泵站养护记录字典
    exportMaintenanceRecord:
      gwxjURL + "/maintainRecordDomain/exportMaintenanceRecord", // 导出泵站养护记录
    maintainPlanListPageAdd:
      gwxjURL + "/maintainPlanDomain/maintainPlanListPageAdd", // 泵站养护计划（历史计划）新增列表
    saveByPlanId: gwxjURL + "/maintainPlanDomain/saveByPlanId", // 泵站养护计划（历史计划）新增保存

    coordTransWgs84ToDg2000: basicUrl + "/coordTrans/wgs84ToDg2000", // wgs84转大地2000平面坐标
    coordTransDg2000ToWgs84: basicUrl + "/coordTrans/dg2000ToWgs84", // 大地2000平面坐标转wgs84

    // 短信配置
    getSendMsgInfo: basicUrl + "/sendMsg/getSendMsgInfo", // 获取当前配置的人员名单
    setupSendMsgTime: basicUrl + "/sendMsg/setupSendMsgTime", // 短信配置

    /**
     * psgs附件相关
     */
    deleteFilePsgs: basicUrl + "/uploadFile/deleteById", //删除图片
    getFileListPsgs: basicUrl + "/uploadFile/getUploadFilesByBizId", //获取图片类型和id
    // ### 泵站设备巡检上报 ###
    deviceTypeDomainListPage: gwxjURL + "/deviceTypeDomain/listPage", // 设备类型列表
    deviceTypeDomainSave: gwxjURL + "/deviceTypeDomain/save", // 设备类型新增
    deviceTypeDomainDelete: gwxjURL + "/deviceTypeDomain/delete", // 设备类型删除
    deviceTypePatrolItemDomainListPage:
      gwxjURL + "/deviceTypePatrolItemDomain/listPage", // 巡检项列表
    deviceTypePatrolItemDomainSave:
      gwxjURL + "/deviceTypePatrolItemDomain/save", // 巡检项新增
    deviceTypePatrolItemDomainDelete:
      gwxjURL + "/deviceTypePatrolItemDomain/delete", // 巡检项删除
    deviceTypePatrolRecordDomainListPage:
      gwxjURL + "/deviceTypePatrolRecordDomain/listPage", // 泵站巡检记录列表
    patrolItemRecordDomainListPage:
      gwxjURL + "/patrolItemRecordDomain/listPage", // 泵站巡检项记录列表
    pumpQueryPump: gwxjURL + "/pump/queryPump", // 泵站巡检记录字典
    deviceTypeDomainFindAll: gwxjURL + "/deviceTypeDomain/findAll", // 泵站巡检记录字典
    getPumpTypeDict: gwxjURL + "/pump/getPumpTypeDict", // 泵站类型字典

    // ### 泵站设备巡检上报 ###

    //单点登录系统登记
    getSystemInfoList: basicUrl + "/systemInfo/list", // 列表
    saveSystemInfo: basicUrl + "/systemInfo/save", // 新增、编辑
    deleteSystemInfo: basicUrl + "/systemInfo/delete", // 删除
    expSystemInfoList: basicUrl + "/systemInfo/export", // 导出
    getSystemInfoAll: basicUrl + "/systemInfo/getAllList", // 列表
    //单点登录用户映射
    getUserMappingList: basicUrl + "/userMapping/list", // 列表
    saveUserMapping: basicUrl + "/userMapping/save", // 新增、编辑
    deleteUserMapping: basicUrl + "/userMapping/delete", // 删除
    expUserMappingList: basicUrl + "/userMapping/export", // 导出
    //单点登录
    getCurrentSystemInfoList: basicUrl + "/userMapping/getUserAllSystem",
    getSystemLink: basicUrl + "/userMapping/getSystemLink",
    //获取报表系统token
    getReportSysToken: basicUrl + "/systemInfo/getReportSysToken",



    //项目台账登记
    getBookInfoList: basicUrl + "/standingbook/getBookInfoList", // 列表
    saveBookInfo: basicUrl + "/standingbook/save", // 新增、编辑
    deleteBookInfo: basicUrl + "/standingbook/delete", // 删除
    getWaterZoneAndSewageSys:
      basicUrl + "/standingbook/getWaterZoneAndSewageSys", // 流域、污水系统字典
    expBookInfoList: basicUrl + "/standingbook/expBookInfoList", // 导出

    //隐患点
    initRiskSelectLine: gwxjURL + "/riskFacility/insertCheck", //检查选线是否已经绑定隐患点了
    riskFacilityList: gwxjURL + "/riskFacility/newList", //获取隐患点设施统计列表
    riskFacilitySubList: gwxjURL + "/riskFacility/list", //获取隐患点设施列表
    riskPointList: gwxjURL + "/riskPoint/list", //获取隐患点列表
    riskPointLst: gwxjURL + "/riskPoint/riskPointLst",//获取隐患点列表
    getRiskFacility: gwxjURL + "/riskFacility/get", //获取设施详情
    getRiskFacilities: gwxjURL + "/riskFacility/getFacilities", //获取多次检测设施详情
    getRiskPoint: gwxjURL + "/riskPoint/getRiskPointInfo", //获取隐患点详情
    saveRiskFacility: gwxjURL + "/riskFacility/save", //保存设施
    editRiskFacility: gwxjURL + "/riskFacility/updateRiskFacility", //修改设施
    saveRiskPoint: gwxjURL + "/riskPoint/saveOneRiskPoint", //保存隐患点
    saveRiskPointWorkOrder:  gwxjURL + "/riskPoint/saveRiskPointWorkOrder",// 回写隐患的工单主键
    deleteRiskFacilityById: gwxjURL + "/riskFacility/deleteRiskFacilityById", //删除隐患设施
    deleteRiskPointById: gwxjURL + "/riskPoint/deleteRiskPointById", //删除隐患点
    getRouteAreaList: gwxjURL + "/route/getRouteAreaList", //路由分公司+镇街下片区字典
    getRouteList: gwxjURL + "/route/getRouteList", //路由字典查询
    getGwxjFileList: gwxjURL + "/uploadFile/getUploadFilesByBizId", // 获取图片


    //信息发布
    getNoticeAndHelpList: basicUrl + "/noticeAndHelp/getNoticeAndHelpList", //获取列表
    addOrEditNoticeAndHelp: basicUrl + "/noticeAndHelp/addOrEditNoticeAndHelp", //新增和修改公告
    deleteNoticeAndHelp: basicUrl + "/noticeAndHelp/deleteNoticeAndHelp", //删除公告

    //运营统计-资源目录统计
    getResourceStaitc: basicUrl + "/pipeDailyStats/getResourceStaitc",

    // 排口迁移仲恺新加的接口
    getAllBelongDistrict: gwxjURL + "/workOrderHelper/getDistrictDict", //获取用户全部片区
    exportSpoutWorkOrderExcel:
      gwxjURL + "/workOrderHelper/kfqSpoutWorkOrderLedgerExport", // 排口巡检台账导出
    spoutExcelExport: basicUrl + "/riverAndSpoutAnalyze/spoutExcelExport", // 排口管理列表导出
    getRiveNameByDistrict:
      basicUrl + "/riverAndSpoutAnalyze/getRiveNameByDistrict", // 根据所属镇街获取所属河涌
    flowConnectAnalysisHdxj: basicUrl + "/pipeAnalyze/flowConnectAnalysis", // 追溯分析
    spoutProblemTypeCount: gwxjURL + "/spoutListCount/problemTypeCount", // 排口统计-排口溯源问题分类统计
    spoutTraceProgressCount: gwxjURL + "/spoutListCount/traceProgressCount", // 排口统计-排口整改分类统计
    spoutTownStreetCount: gwxjURL + "/spoutListCount/townStreetCount", // 排口统计-排口问题覆盖统计
    spoutSortCount: gwxjURL + "/spoutListCount/sortCount", // 排口统计-排口分类统计

    // 泵站巡检-班组管理
    getPumpMaintTeamManageList: gwxjURL + "/pumpPatrolOperationTeam/list", // 班组管理-列表
    savePumpMaintTeamManage: gwxjURL + "/pumpPatrolOperationTeam/save", // 班组管理-保存
    getTeamMemberList: gwxjURL + "/pumpPatrolOperationTeam/getTeamMember", // 班组管理-人员管理-列表
    addTeamMember: gwxjURL + "/pumpPatrolOperationTeam/addTeamMember", // 班组管理-人员管理-新增
    getSbUserIdDictByOrgId:
      gwxjURL + "/pumpPatrolOperationTeam/getSbUserIdDictByOrgId", // 班组管理-人员管理-人员字典
    deleteTeamMember: gwxjURL + "/pumpPatrolOperationTeam/deleteTeamMember", // 班组管理-人员管理-删除
    getDutyPlanList: gwxjURL + "/pumpPatrolDutyPlan/list", // 值班管理-列表
    getPumpDict: gwxjURL + "/pumpPatrolDutyPlan/getPumpDict", // 值班管理-所属泵站字典（传分公司id则为获取所属分公司的泵站，不传则为获取全部）
    saveOnDutyManage: gwxjURL + "/pumpPatrolDutyPlan/save", // 值班管理-新增编辑
    deleteOnDutyManage: gwxjURL + "/pumpPatrolDutyPlan/delete", // 值班管理-删除
    getlistPerform: gwxjURL + "/pumpPatrolDutyPlan/listPerform", // 人员履职管理-列表
    getOperationPlanList: gwxjURL + "/pumpPatrolOperationPlan/list", // 运营计划-列表
    saveOperationPlan: gwxjURL + "/pumpPatrolOperationPlan/save", // 运营计划-新增编辑
    deleteOperationPlan: gwxjURL + "/pumpPatrolOperationPlan/delete", // 运营计划-删除
    getlistApprove: gwxjURL + "/pumpPatrolOperationPlan/listApprove", // 运营计划审批-列表
    approvelistApprove: gwxjURL + "/pumpPatrolOperationPlan/approve", // 运营计划审批-审批

    pumpPatrolListPage: gwxjURL + "/pumpPatrol/listPage", // 维保清疏台账
    pumpPatrolPumpPatrolExport: gwxjURL + "/pumpPatrol/pumpPatrolExport", // 维保清疏台账导出
    getHandleWayDict: gwxjURL + "/pumpPatrol/getHandleWayDict", // 维保清疏台账-处理方式字典
    //通用属性查询
    getDataBaseList: basicURL + "/commonSearch/getDataBaseList", //获取数据库列表
    getDataBaseTableList: basicURL + "/commonSearch/getTableList", //获取数据库下的图层列表
    getColumList: basicURL + "/commonSearch/getColumList", //获取数据库下的图层列表
    commonSearch: basicURL + "/commonSearch/search", //普通查询
    sqlSearch: basicURL + "/commonSearch/searchBySql", //sql查询
    commonQueryFeatureByPoint: basicUrl + "/commonSearch/queryFeatureByPoint", //点选查询管线要素
    exportProblemData: gwxjURL + "/riskPoint/exportData", //获取官网问题管理列表详情

    probleReportParameterList: gwxjURL + "/problemImport/listPage", // 问题上报工单台账
    constructionReportParameterList:
      gwxjURL + "/externalConstructionImport/listPage", // 问题上报工单台账

    // 系统运维-在线监测管理-污水厂管理
    getWasteWaterList: iotUrl + "/wasteWater/list", // 查询污水厂列表
    wasteWaterSave: iotUrl + "/wasteWater/save", // 新增污水厂
    wasteWaterDelete: iotUrl + "/wasteWater/delete", // 删除污水厂

    //网格管理接口
    getAreasTree: gwxjURL + "/gridManage/getAreasTree", //获取地区树
    getChangeGisDetail: gwxjURL + "/gridManage/qryWithId", //根据gisid获取地区详细信息
    getTeamManagementList: gwxjURL + "/team/list", //获取班组列表
    teamManagementAdd: gwxjURL + "/team/add", //新增班组
    teamManagementDel: gwxjURL + "/team/batchDel", //删除班组
    teamManagementUpdate: gwxjURL + "/team/update", //编辑班组
    dictList: gwxjURL + "/gridManage/dictList", //通用下拉框字典
    teamManagementPeopleList: gwxjURL + "/gridManage/peopleList", //班组管理-人员下拉框
    getProjectList: gwxjURL + "/project/getProjectList", //获取项目列表
    projectPlanAdd: gwxjURL + "/project/addProject", //新增项目
    projectPlanBatchDel: gwxjURL + "/project/batchDel", //删除项目
    getGirdList: gwxjURL + "/grid/list", //查看网格列表
    getGirdDetail: gwxjURL + "/grid/detail", //查看网格详情
    getGridBoList: gwxjURL + "/grid/boList", //获取管网设施列表
    batchAddOrUpdate: gwxjURL + "/grid/batchAddOrUpdate", //新增网格
    girdBatchDel: gwxjURL + "/grid/batchDel", //删除网格
    simplelist: gwxjURL + "/team/simplelist", //网格班组列表
    batchDelProject: gwxjURL + "/project/batchDelProject", //批量删除项目
    copyProject: gwxjURL + "/project/copyProject", //复制项目
    getProjectPlanList: gwxjURL + "/projectPlan/getProjectPlanList", //获取项目计划列表
    getProjectPlanGridDictTree: gwxjURL + "/project/getProjectPlanGridDictTree", //获取计划网格列表
    projectCompanyDictTree: gwxjURL + "/project/projectCompanyDictTree", //获取项目新增公司下拉字典
    projectFirstTypeDictTree: gwxjURL + "/project/projectFirstTypeDictTree", //获取项目新增类型下拉字典
    getProjectPlanProviderDictTree:
      gwxjURL + "/project/getProjectPlanProviderDictTree", //获取项目供应商下拉字典
    addProjectPlan: gwxjURL + "/projectPlan/addProjectPlan", //新增项目计划
    getProjectPlanById: gwxjURL + "/projectPlan/getProjectPlanById", //获取项目计划详情
    batchDelProjectPlan: gwxjURL + "/projectPlan/batchDelProjectPlan", //删除项目计划
    getPipeList: gwxjURL + "/grid/getPipeList", //获取管道列表
    getWellList: gwxjURL + "/grid/getWellList", //获取管井列表
    getRecommendDeviceList: gwxjURL + "/projectPlanDevice/getRecommendDeviceList", //获取推荐设备
    getProjectPlanDeviceAllDictTree:
      gwxjURL + "/project/getProjectPlanDeviceAllDictTree", //获取管井管段所有下拉框信息
    getreportList: gwxjURL + "/project/reportList", //获取项目台账列表
    getProjectPlanPipeDeviceList:
      gwxjURL + "/projectPlanDevice/getProjectPlanPipeDeviceList", //获取项目计划查看管段
    getProjectPlanWellDeviceList:
      gwxjURL + "/projectPlanDevice/getProjectPlanWellDeviceList", //获取项目计划查看管井
    updateProjectPlan: gwxjURL + "/projectPlan/updateProjectPlan", //编辑项目计划
    copyProjectPlan: gwxjURL + "/projectPlan/copyProjectPlan", //复制项目计划
    getProjectPlanTeamDictTree: gwxjURL + "/project/getProjectPlanTeamDictTree", //项目计划获取班组
    engineerSummaryList: gwxjURL + "/project/engineerSummaryList", //项目计划获取工程量汇总
    getExternalConstructions: gwxjURL + "/projectPlan/getExternalConstructions", //项目计划新增获取外部施工
    getProblems: gwxjURL + "/projectPlan/getProblems", //项目计划新增获取问题上报
    getProjectPlanExternalConstructions: gwxjURL + "/projectPlan/getProjectPlanExternalConstructions", //项目计划获取外部施工
    getProjectPlanProblems: gwxjURL + "/projectPlan/getProjectPlanProblems", //项目计划获取问题上报
    getProjectPlanRisks: gwxjURL + "/projectPlan/getProjectPlanRisks", //项目计划获取管道检测
    getProjectPlanClears: gwxjURL + "/projectPlan/getProjectPlanClears", //项目计划获取管道检测
    getSortChart: basicURL + "/gridManage/getSortChart", //分类柱状图
    getCategoryChart: basicURL + "/gridManage/getCategoryChart", //类属柱状图
    reportListDownload: gwxjURL + "/project/reportListDownload", //项目台账导出
    getProjectPlanListDownload:
      gwxjURL + "/projectPlan/getProjectPlanListDownload", //项目计划列表导出
    oneProjectDownload: gwxjURL + "/project/oneProjectDownload", //项目导出
    oneProjectPlanDownload: gwxjURL + "/projectPlan/oneProjectPlanDownload", //项目计划导出
    engineerSummaryListDownload:
      gwxjURL + "/project/engineerSummaryListDownload", //工程量汇总导出
    projectPlanDispatch: gwxjURL + "/projectPlan/projectPlanDispatch", //项目计划派发
    projectPlanTakeBack: gwxjURL + "/projectPlan/projectPlanTakeBack", //项目计划收回
    projectPlanApprove: gwxjURL + "/projectPlan/projectPlanApprove", //项目计划审核通过或驳回
    clockDetail: gwxjURL + "/projectPlan/clockDetail", //获取设施详情
    girdUpdate: gwxjURL + "/grid/update", //网格编辑功能
    projectPlanBatchDispatch: gwxjURL + "/projectPlan/projectPlanBatchDispatch", //网格计划批量派发功能
    batchUpdateProjectPlan: gwxjURL + "/projectPlan/batchUpdateProjectPlan", //网格计划批量编辑功能
    projectEngineerSummaryListDownload: gwxjURL + "/project/projectEngineerSummaryListDownload", //总工程量汇总导出
    projectPlanDeviceGetWellList: gwxjURL + "/projectPlanDevice/getWellList", //项目计划获取管井
    projectPlanDeviceGetPipeList: gwxjURL + "/projectPlanDevice/getPipeList", //项目计划获取管段
    openWellInspectionList: gwxjURL + "/project/openWellInspectionList", //开井检查列表
    openWellInspectionListExport: gwxjURL + "/project/openWellInspectionListExport", //开井检查列表导出
    externalConstructClockList: gwxjURL + "/projectPlan/externalConstructClockList", //查看外部工地,问题上报打卡详情(新)
    getPlanCompleteProgressInfo: gwxjURL+"/projectPlan/getPlanCompleteProgressInfo", //获取轨迹导出
    getProjectPlanPatrolRoute: gwxjURL+"/projectPlanPatrolroute/getProjectPlanPatrolRoute", //获取地图轨迹
    operationDashboardA: gwxjURL+"/chart/operationDashboardA", //仪表盘图标A
    operationDashboardB: gwxjURL+"/chart/operationDashboardB", //仪表盘图标B
    operationDashboardC: gwxjURL+"/chart/operationDashboardC", //仪表盘图标C
    getRouteStatisticPageList: gwxjURL+"/route/getRouteStatisticPageList", //路由台账
    getRouteInspectionPageList: gwxjURL+"/route/getRouteInspectionPageList", //路由巡查台账
    externalConstructClockListExcelExport: gwxjURL + "/projectPlan/externalConstructClockListExcelExport", //打卡详情导出
    getRouteStatisticPageListExcelExport: gwxjURL +"/route/getRouteStatisticPageListExcelExport", //路由台账导出
    getRouteInspectionPageListExcelExport: gwxjURL +"/route/getRouteInspectionPageListExcelExport", //路由巡查台账导出
    getRoutePageList: gwxjURL +"/route/getRoutePageList", //路由管理列表
    selectRoutePageList: gwxjURL +"/route/selectRoutePageList", //路由列表
    getRouteAreaList: gwxjURL +"/route/getRouteAreaList", //路由片区字典
    addRoute: gwxjURL +"/route/addRoute", //路由新增
    batchDelRoute: gwxjURL +"/route/batchDelRoute", //批量删除路由
    updateRoute: gwxjURL +"/route/updateRoute", //编辑路由
    repairProblemStatistics: gwxjURL +"/chart/repairProblemStatistics", //仪表盘问题图表
    clearProblemStatistics: gwxjURL +"/chart/clearProblemStatistics", //清疏问题图表
    reportProblemStatistics: gwxjURL +"/chart/reportProblemStatistics", //问题上报图表
    routeExcelExport: gwxjURL +"/route/routeExcelExport", //路由管理导出
    routeExcelImport: gwxjURL +"/route/routeExcelImport", //路由管理导入
    otherDailyWork: gwxjURL +"/otherDailyWork/queryOtherDailyWorkList", //工作日常上报台账
    getCompanyTeamDictTree: gwxjURL +"/grid/getCompanyTeamDictTree", //根据公司查询班组
    getCompanyTeamDictTreeByCompanyName: gwxjURL +"/grid/getCompanyTeamDictTreeByCompanyName", //根据公司名称查询班组
    queryOtherDailyWorkPicList: gwxjURL +"/otherDailyWork/queryOtherDailyWorkPicList", //根据列表获取图表集合
    getRouteOpenWellStatistic: gwxjURL +"/chart/getRouteOpenWellStatistic", //路由开井统计
    getProjectPlanOperateOrderInfo: gwxjURL +"/projectPlanDevice/getProjectPlanOperateOrderInfo", //获取项目计划操作的工单信息
    getDistrictUserOrderStatistic: gwxjURL +"/chart/getDistrictUserOrderStatistic", //镇街统计上报人员图
    getOtherDailyWorkListExcelExport: gwxjURL +"/otherDailyWork/getOtherDailyWorkListExcelExport", //其他日常工作台账excel导出
    getCurrentUserCompanyOwner: gwxjURL +"/grid/getCurrentUserCompanyOwner", //查询所在公司
    getProjectPlanDeviceOperateOrderDetail: gwxjURL +"/projectPlanDevice/getProjectPlanDeviceOperateOrderDetail", //查询工单信息

    // 一张图相关
    getDistrictDict: gwxjURL +"/layerHelper/getDistrictDict", //获取片区字典
    getPipeProjectTypeDict: gwxjURL +"/layerHelper/getPipeProjectTypeDict", //获取片区字典
    queryPageRiskFacilityData: gwxjURL + "/riskLayer/queryPageRiskFacilityData", //查询风险设施
    getRiskLayerData: gwxjURL + "/riskLayer/getRiskLayerData", //查询风险设施
    queryRiskListByGeoId: gwxjURL + '/riskLayer/queryRiskListByGeoId', //根据地理id查询风险设施
    queryRiskListByObjectId: gwxjURL + '/riskLayer/queryRiskListByObjectId', //根据对象id查询风险设施
    getClearLayerData: gwxjURL + "/clearLayer/getClearLayerData", //查询清疏设施
    searchClearData: gwxjURL + "/clearLayer/searchClearData", //查询清疏设施数据

    queryLayerFeatureByObjectId: basicUrl + '/pipeQuery/queryLayerFeatureByObjectId', //根据对象id查询图层要素
    findClearByFacilityCuuid: gwxjURL + '/oneMap/findClearByFacilityCuuid', //根据设施id查询工单
    findEngmaiByFacilityCuuid: gwxjURL + '/oneMap/findEngmaiByFacilityCuuid', //根据设施id查询工单
    findClockByFacilityCuuid: gwxjURL + '/oneMap/findClockByFacilityCuuid', //根据设施id查询工单
    findProblemByCuuid: gwxjURL + '/oneMap/findProblemByCuuid', //根据设施id查询工单
    getProblemById: gwxjURL + '/problemImport/getWorkOrderById',
    exportClearData: gwxjURL + '/clearLayer/exportClearData', //清疏设施导出
    exportRiskData: gwxjURL + '/riskLayer/exportRiskData', //检测设施导出
  };

  let vueInstance;
  var errorCallback = function (response, url) {
    console.log(response, url);
    if (!!response.data && response.data.msg == "权限验证失败") {
      vueInstance.$alert("您的登录已过期，请重新登录,谢谢", "登录过期提示", {
        confirmButtonText: "确定",
        callback: (action) => {
          location.reload();
        },
      });

      return;
    }
    if (!!response.data && response.data.returnMsg) {
      vueInstance.$message({
        message: response.data.returnMsg,
        type: "error",
      });
      return;
    }
    var alertMsg = !!response.msg ? response.msg : "系统错误，请稍后再试。";
    if (!!vueInstance.$message) {
      vueInstance.$message({
        message: alertMsg,
        type: "error",
      });
    } else {
      alert(alertMsg);
    }
    eventHelper.emit("loading-end");
  };
  //初始化axios
  let $axios = axiosHelper.initAxios(errorCallback);
  return {
    getExcelUrl: function (fileName) {
      return (
        iotUrl +
        "/tempFile/downloadFile?fileName=" +
        fileName +
        "&token=" +
        userToken
      );
    },
    downLoadFile: function (param) {
      let params = "";
      for (let key in param.parameter) {
        params += "&" + key + "=" + param.parameter[key];
      }
      let _url =
        serviceEndpoint[param.id] + "?token=" + this.getToken() + params;
      console.log(_url);
      window.open(_url);
    },
    getStaticUrl: function () {
      return config.staticURL;
    },
    getEwaterUrl: function () {
      return ewaterUrl;
    },
    getNormalPath: function () {
      return normalUrl;
    },
    getIotPath: function () {
      return serviceEndpoint["iotPath"];
    },
    getSocketEndpoint: function () {
      return serviceEndpoint["socketEndpoint"];
    },
    getBasePath: function () {
      return basicUrl;
    },
    getGwxjPath: function () {
      return gwxjURL;
    },
    getArcgisServer: function () {
      return config.arcgisURL;
    },
    getAeURLUrl: function () {
      return aeURL;
    },
    setToken: function (token) {
      userToken = token;
    },
    getToken: function () {
      return userToken;
    },
    /**
     * 获取后台url
     * 支持多个后台，参数1（pathKey）=后台的key，为空返回默认后台basicPath
     * @param pathKey
     * @returns {*}
     */
    getBasicPath: function (pathKey) {
      if (pathKey) {
        return serviceEndpoint[pathKey];
      } else {
        return serviceEndpoint["basicPath"];
      }
    },
    getEwaterPath: function () {
      return serviceEndpoint["ewaterPath"];
    },
    getPathByEndpoint: function (id) {
      return serviceEndpoint[id];
    },
    //获取ajax请求默认参数（所有ajax请求都必须有的参数）
    getDefaultAjaxParam: function () {
      var formData = {};
      //token登录用
      formData.token = this.getToken();
      //随机数，防止缓存
      formData.r = Math.random();

      return formData;
    },
    getIotAjaxParam: function () {
      var formData = {};
      //token登录用
      formData.token = userToken;
      //随机数，防止缓存
      formData.r = Math.random();

      return formData;
    },
    //get请求，获取json格式
    //请求结果格式要求按ewater标准
    getJson: function (url, formData, successHandler, errorHandler) {
      //对jquery的ajax进行封装，主要为了精简代码，业务功能有需要可以用jquery原版的
      let needEncryption = false;
      //需要加密接口
      let urlList = [
        // this.getBasePath()+'/pipeSysUser/list',
        // this.getBasePath()+'/pipeSysUser/get',
        this.getEwaterUrl() + "/sysUser/get",
        this.getEwaterUrl() + "/sysUser/list",
        this.getEwaterUrl() + "/sysUser/save",
        // this.getBasePath()+'/pipeSysUser/save',
        // this.getEwaterUrl()+'/sysUser/getUserInfo',
        // this.getBasePath()+'/pipeSysUser/getLoginUserInfo',
      ];
      urlList.forEach((u) => {
        if (url === u) needEncryption = true;
      });
      if (needEncryption) {
        //加密要用post请求
        for (let k in formData) {
          formData[k] = AES.encrypt(formData[k]);
        }
        if (Object.keys(formData).length == 0) {
          formData.token = AES.encrypt(userToken);
          formData.r = AES.encrypt(Math.random());
        }
        $.ajax({
          type: "post",
          dataType: "json",
          url: url,
          data: formData,
          success: function (ajaxResult) {
            //请求结果格式要求按ewater标准
            if (ajaxResult) {
              if (ajaxResult.success === true) {
                var result = JSON.parse(AES.decrypt(ajaxResult.data));
                if (successHandler) {
                  successHandler(result);
                }
              } else {
                //后台操作失败的代码
                if (!!errorHandler) {
                  errorHandler(ajaxResult);
                }
              }
            }
          }.bind(this),
        });
      } else {
        //对jquery的ajax进行封装，主要为了精简代码，业务功能有需要可以用jquery原版的
        $.ajax({
          type: "get",
          dataType: "json",
          url: url,
          data: formData,
          success: function (ajaxResult) {
            //请求结果格式要求按ewater标准
            if (ajaxResult) {
              if (ajaxResult.success === true) {
                var result = ajaxResult.data;

                if (successHandler) {
                  successHandler(result);
                }
              } else {
                //后台操作失败的代码
                if (!!errorHandler) {
                  errorHandler(ajaxResult);
                }
              }
            }
          }.bind(this),
        });
      }
    },
    //post请求，获取json格式
    //请求结果格式要求按ewater标准
    postJson: function (url, formData, successHandler, errorHandler) {
      //对jquery的ajax进行封装，主要为了精简代码，业务功能有需要可以用jquery原版的
      let needEncryption = false;
      //需要加密接口
      let urlList = [
        // this.getBasePath()+'/pipeSysUser/list',
        // this.getBasePath()+'/pipeSysUser/get',
        this.getEwaterUrl() + "/sysUser/get",
        this.getEwaterUrl() + "/sysUser/list",
        this.getEwaterUrl() + "/sysUser/save",
        // this.getBasePath()+'/pipeSysUser/save',
        // this.getEwaterUrl()+'/sysUser/getUserInfo',
        // this.getBasePath()+'/pipeSysUser/getLoginUserInfo',
        this.getAeURLUrl() +'/uploadFile/getUploadFilesByBizId',
      ];
      urlList.forEach((u) => {
        if (url === u) needEncryption = true;
      });
      if (needEncryption) {
        for (let k in formData) {
          formData[k] = AES.encrypt(formData[k]);
        }
        formData.token = AES.encrypt(userToken);
        formData.r = AES.encrypt(Math.random());
      } else {
        formData.token = this.getToken();
        formData.r = Math.random();
      }
      $.ajax({
        type: "post",
        dataType: "json",
        url: url,
        data: formData,
        success: function (ajaxResult) {
          //请求结果格式要求按ewater标准
          if (ajaxResult) {
            if (ajaxResult.success === true) {
              var result = ajaxResult.data;

              if (successHandler) {
                successHandler(result);
              }
            } else {
              //后台操作失败的代码
              if (!!errorHandler) {
                errorHandler(ajaxResult.msg);
              } else {
                alert(ajaxResult.msg);
              }
            }
          }
        }.bind(this),
      });
    },
    //长处理时间的post
    postJsonBigData: function (url, formData, successHandler, errorHandler) {
      //对jquery的ajax进行封装，主要为了精简代码，业务功能有需要可以用jquery原版的
      formData.token = this.getToken();
      formData.r = Math.random();
      $.ajax({
        type: "post",
        dataType: "json",
        url: url,
        timeout: 300000, //5分钟的timeout
        data: formData,
        success: function (ajaxResult) {
          //请求结果格式要求按ewater标准
          if (ajaxResult) {
            if (ajaxResult.success === true) {
              var result = ajaxResult.data;

              if (successHandler) {
                successHandler(result);
              }
            } else {
              //后台操作失败的代码
              if (!!errorHandler) {
                errorHandler(ajaxResult.msg);
              } else {
                alert(ajaxResult.msg);
              }
            }
          }
        }.bind(this),
      });
    },
    //post请求，获取json格式
    //成功返回数据code：200
    postJsonCode200: function (url, formData, successHandler, errorHandler) {
      //对jquery的ajax进行封装，主要为了精简代码，业务功能有需要可以用jquery原版的
      formData.token = this.getToken();
      formData.r = Math.random();
      $.ajax({
        type: "post",
        dataType: "json",
        url: url,
        data: formData,
        success: function (ajaxResult) {
          //请求结果格式要求按ewater标准
          if (ajaxResult) {
            if (ajaxResult.code == 200) {
              var result = ajaxResult.data;

              if (successHandler) {
                successHandler(result);
              }
            } else {
              //后台操作失败的代码
              if (!!errorHandler) {
                errorHandler(ajaxResult.msg);
              } else {
                alert(ajaxResult.msg);
              }
            }
          }
        }.bind(this),
      });
    },
    // post 发送包含文件的formData
    postFile: function (url, formData, successHandler, errorHandler) {
      formData.append("token", this.getToken());
      formData.append("r", Math.random());
      $.ajax({
        url: url,
        type: "POST",
        cache: false,
        data: formData,
        processData: false,
        contentType: false,
        success: function (ajaxResult) {
          //请求结果格式要求按ewater标准
          if (ajaxResult) {
            if (ajaxResult.success === true) {
              if (successHandler) {
                successHandler(ajaxResult);
              }
            } else {
              //后台操作失败的代码
              if (!!errorHandler) {
                errorHandler(ajaxResult);
              }
            }
          }
        }.bind(this),
      });
    },
    getUrl: function (id) {
      return serviceEndpoint[id];
    },
    getPath: function (connectionObj) {
      var url;
      if (
        !(connectionObj instanceof Object) &&
        !!serviceEndpoint[connectionObj]
      ) {
        url = serviceEndpoint[connectionObj];
      } else {
        url = serviceEndpoint[connectionObj.id];
      }
      if (!url) {
        console.log("ERROR:Cant get the url with id:", connectionObj);
        return serviceEndpoint.basicUrl;
      }
      let needEncryption = false;
      //需要加密接口
      let urlList = [
        // this.getBasePath()+'/pipeSysUser/list',
        // this.getBasePath()+'/pipeSysUser/get',
        this.getEwaterUrl() + "/sysUser/get",
        this.getEwaterUrl() + "/sysUser/list",
        this.getEwaterUrl() + "/sysUser/save",
        // this.getBasePath()+'/pipeSysUser/save',
        // this.getEwaterUrl()+'/sysUser/getUserInfo',
        // this.getBasePath()+'/pipeSysUser/getLoginUserInfo',
        this.getAeURLUrl() +'/uploadFile/getUploadFilesByBizId',
      ];
      urlList.forEach((u) => {
        if (url === u) needEncryption = true;
      });
      if (!needEncryption) {
        if (!!userToken) {
          url += "?token=" + userToken;
          url += "&r=" + Math.random();
        }
        if (!!connectionObj.parameter) {
          var parameters = connectionObj.parameter;
          var parameterURL = !!userToken ? "&" : "?";
          for (var key in parameters) {
            parameterURL += key + "=" + parameters[key] + "&";
          }
          parameterURL += "r=" + Math.random();
          return encodeURI(
            url + parameterURL.substring(0, parameterURL.length - 1)
          );
        }
      }
      return url;
    },
    getHasTokenPath: function (connectionObj) {
      var url;
      if (
        !(connectionObj instanceof Object) &&
        !!serviceEndpoint[connectionObj]
      ) {
        url = serviceEndpoint[connectionObj];
      } else {
        url = serviceEndpoint[connectionObj.id];
      }
      if (!url) {
        console.log("ERROR:Cant get the url with id:", connectionObj);
        return serviceEndpoint.basicUrl;
      }
      if (!!connectionObj.parameter) {
        var parameters = connectionObj.parameter;
        var parameterURL = "?";
        for (var key in parameters) {
          parameterURL += key + "=" + parameters[key] + "&";
        }
        parameterURL += "r=" + Math.random();
        return encodeURI(
          url + parameterURL.substring(0, parameterURL.length - 1)
        );
      }
      return url;
    },
    //重新封装get方法，自动对异常请求进行处理
    get: function (url, success, errorCb) {
      $.ajax({
        url: url,
        timeout: 120000,
        success: function (response) {
          if (response) {
            success(response);
          } else {
            if (!!errorCb) {
              errorCb(response);
            } else {
              errorCallback(response, url);
            }
          }
        },
        error: !!errorCb ? errorCb : errorCallback,
        dataType: "json",
      });
    },
    getWithCredentials: function (url, success, errorCb) {
      $.ajax({
        url: url,
        timeout: 120000,
        xhrFields: { withCredentials: true },
        success: function (response) {
          if (response) {
            success(response);
          } else {
            if (!!errorCb) {
              errorCb(response);
            } else {
              errorCallback(response, url);
            }
          }
        },
        error: !!errorCb ? errorCb : errorCallback,
        dataType: "json",
      });
    },
    postWithCredentials: function (url, formData, success, errorCb) {
      $.ajax({
        url: url,
        type: "POST",
        data: formData,
        dataType: "json",
        xhrFields: { withCredentials: true },
        success: function (response) {
          if (response.success) {
            success(response);
          } else {
            if (!!errorCb) {
              errorCb(response);
            } else {
              errorCallback(response);
            }
          }
        },
        error: !!errorCb ? errorCb : errorCallback,
      });
    },
    /***
     * 获取图片流数据地址
     * @param picId
     * @returns {string}
     */
    obtainPicStream(picId) {
      return (
        pumpUrl +
        "/uploadFile/downloadFileById?id=" +
        picId +
        "&token=" +
        userToken
      );
    },
    getPicUrl: function (picId) {
      return (
        basicUrl +
        "/uploadFile/downloadFileById?id=" +
        picId +
        "&token=" +
        userToken
      );
    },
    // 获取安全生产图片流
    getSafeProdPic: function (id, cache = false) {
      return (
        serviceEndpoint["obtainSafeProdPicStream"] +
        "?id=" +
        id +
        "&token=" +
        userToken +
        (cache ? "" : "&r=" + Math.random())
      );
    },
    // 获取安全生产文件下载地址
    getSafeProdFileUrl: function (id) {
      return (
        safeProdUrl +
        "/safeProdUploadFile/downloadFileById?id=" +
        id +
        "&token=" +
        userToken
      );
    },
    getIotFileUrl: function (picId) {
      return (
        iotUrl +
        "/uploadFile/downloadFileById?id=" +
        picId +
        "&token=" +
        userToken
      );
    },

    // 获取管网巡检图片
    getGwxjPicUrl: function (picId) {
      return (
        gwxjURL +
        "/uploadFile/downloadFileById?id=" +
        picId +
        "&token=" +
        userToken
      );
    },
    getPipeUrl: function () {
      return pipeUrl;
    },
    setUpVue: function (vue) {
      vueInstance = vue;
    },
    /***
     * axiosGet封装调用
     *
     * @param serviceEndpoint 传入服务Id，自动加入token和随机数
     * @param config axios动态配置信息，具体参考axios官网
     * @returns {*}
     */
    axiosGet(serviceEndpoint, config) {
      let fullUrl = this.getPath(serviceEndpoint);
      return $axios.get(fullUrl, {}, config);
    },
    /***
     * axiosPost封装调用
     *
     * @param serviceEndpoint 传入服务Id，自动加入token和随机数
     * @param formData 传入要提交的表单数据
     * @param config axios动态配置信息，具体参考axios官网
     * @returns {*}
     */
    axiosPost(serviceEndpoint, formData, config) {
      let fullUrl = this.getPath(serviceEndpoint);
      return $axios.post(fullUrl, formData, config);
    },

    /**
     * 泵站巡检
     * **/
    post(url, formData, success, errorCb) {
      $.ajax({
        url: url,
        type: "POST",
        data: formData,
        dataType: "json",
        success: function (response) {
          if (response.success) {
            success(response);
          } else {
            if (!!errorCb) {
              errorCb(response);
            } else {
              errorCallback(response);
            }
          }
        },
        error: !!errorCb ? errorCb : errorCallback,
      });
    },

    //网格管理post请求处理
    girdPost(url, formData, success, errorCb) {
      $.ajax({
        url: url,
        type: "POST",
        dataType: "json",
        contentType: "application/json",
        data: JSON.stringify(formData),
        success: function (response) {
          if (response.data && response.data.returnCode === "200") {
            success(response.data);
          } else if (response.returnCode === "200") {
            success(response);
          } else {
            if (!!errorCb) {
              errorCb(response);
            } else {
              errorCallback(response);
            }
          }
        },  
        error: !!errorCb ? errorCb : errorCallback,
      });
    },
    //管点缺陷管理导出excel
    pipeDefectExportExcel(url, cb, fail) {
      axios({
        method: "get",
        url,
        headers: {
          token: this.getToken(),
        },
        responseType: "blob",
      })
        .then((res) => {
          if (res.data) {
            cb(res.data);
          } else {
            fail && fail(res);
          }
        })
        .catch((err) => {
          fail && fail(err);
        });
    },
    //导出excel
    exportExcel(url,params,bodyData, cb, fail) {
      params.token = this.getToken();
      params.r = Math.random();
      axios({
        method: "post",
        url,
        headers: {
          token: this.getToken(),
          'Content-Type': 'application/json; charset=UTF-8',
          'X-Requested-With': 'XMLHttpRequest'
        },
        params: params,
        data: bodyData, //
        responseType: "blob",
      })
        .then((response) => {
          if(cb){
            cb();
          }
          // 解析文件名（UTF-8编码处理）
          const disposition = response.headers['content-disposition'];
          const fileNameMatch = disposition.match(/filename\*=UTF-8''(.+?)(;|$)/);
          const fileName = fileNameMatch ?
              decodeURIComponent(fileNameMatch[1]) :
              `export_${new Date().getTime()}.xlsx`;

          // 创建Blob链接
          const blob = new Blob([response.data], {
            type: response.headers['content-type']
          });

          // 兼容IE的下载方式
          if (window.navigator.msSaveBlob) {
            window.navigator.msSaveBlob(blob, fileName);
          } else {
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(link.href);
          }
        })
        .catch((err) => {
          fail && fail(err);
        });
    },
    //post请求，content-type格式为application/json的
    postJsonStringifyData(
      url,
      formData,
      bodyData,
      successHandler,
      errorHandler
    ) {
      formData.token = this.getToken();
      formData.r = Math.random();
      let sendUrl = url + "?";
      Object.keys(formData).forEach((key) => {
        sendUrl += key + "=" + formData[key] + "&";
      });
      sendUrl = sendUrl.substring(0, sendUrl.length - 1);
      $.ajax({
        type: "post",
        dataType: "json",
        url: sendUrl,
        contentType: "application/json",
        data: JSON.stringify(bodyData),
        success: function (ajaxResult) {
          //请求结果格式要求按ewater标准
          if (ajaxResult) {
            if (ajaxResult.success === true) {
              var result = ajaxResult.data;

              if (successHandler) {
                successHandler(result);
              }
            } else {
              //后台操作失败的代码
              if (!!errorHandler) {
                errorHandler(ajaxResult.msg);
              } else {
                alert(ajaxResult.msg);
              }
            }
          }
        }.bind(this),
      });
    },
  };
});
