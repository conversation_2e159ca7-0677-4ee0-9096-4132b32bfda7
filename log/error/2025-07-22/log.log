2025-07-22 16:10:00.159 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-22 16:20:00.120 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-22 16:30:00.129 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-22 16:40:00.123 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-22 16:50:00.149 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-22 17:00:00.169 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-22 17:10:00.124 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-22 17:20:00.130 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-22 17:30:00.139 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
