2025-04-11 09:10:00.170 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 09:20:00.124 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 09:30:00.136 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 09:40:00.123 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 09:50:00.123 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 10:00:00.131 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 10:10:00.225 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 10:20:00.124 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 10:30:00.144 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 10:30:00.197 ERROR com.cesc.ewater.biz.oneMapManager.screen.AutoWeatherQueryTask - 获取实时天气信息失败。
java.lang.RuntimeException: yjlist({"江门":[{"bgcolor":"#ffff00","city":"江门市江海区","pic":"SL_3","publishtime":"2025-03-29 19:35:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"江门市蓬江区","pic":"SL_3","publishtime":"2025-03-29 19:35:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"江门","pic":"SL_3","publishtime":"2025-03-29 19:35:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"开平","pic":"SL_3","publishtime":"2025-03-30 08:41:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"台山","pic":"SL_3","publishtime":"2025-03-30 09:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"新会","pic":"SL_3","publishtime":"2025-03-30 09:10:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"鹤山","pic":"SL_3","publishtime":"2025-03-30 09:21:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"恩平","pic":"SL_3","publishtime":"2025-03-30 09:40:00","warn":"森林火险黄色"}],"湛江":[{"bgcolor":"#ffff00","city":"徐闻","pic":"SL_3","publishtime":"2025-04-01 15:01:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"雷州","pic":"SL_3","publishtime":"2025-04-01 15:11:00","warn":"森林火险黄色"},{"bgcolor":"#fd9903","city":"湛江市东海岛经济开发试验区","pic":"SL_4","publishtime":"2025-04-02 15:11:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"湛江市坡头区","pic":"SL_4","publishtime":"2025-04-02 15:11:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"湛江市赤坎区","pic":"SL_4","publishtime":"2025-04-02 15:11:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"湛江市霞山区","pic":"SL_4","publishtime":"2025-04-02 15:11:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"湛江市麻章区","pic":"SL_4","publishtime":"2025-04-02 15:11:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"湛江","pic":"SL_4","publishtime":"2025-04-02 15:11:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"遂溪","pic":"SL_4","publishtime":"2025-04-02 15:12:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"吴川","pic":"SL_4","publishtime":"2025-04-02 15:18:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"廉江","pic":"SL_4","publishtime":"2025-04-02 15:21:00","warn":"森林火险橙色"}],"揭阳":[{"bgcolor":"#ffff00","city":"揭阳惠来县青坑林场","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县鳌江镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县东埔农场","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县侨园镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县葵潭农场","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县葵潭镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县东港镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县惠城镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县华湖镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县仙庵镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县靖海镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县周田镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县前詹镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县神泉镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县东陇镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县岐石镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县隆江镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"},{"bgcolor":"#ffff00","city":"揭阳惠来县溪西镇","pic":"BY_3","publishtime":"2025-03-15 17:47:00","warn":"暴雨黄色"}],"东莞":[{"bgcolor":"#ffff00","city":"东莞","pic":"SL_3","publishtime":"2025-04-07 15:27:00","warn":"森林火险黄色"}],"中山":[{"bgcolor":"#ffff00","city":"南朗街道","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"小榄镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"神湾镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"大涌镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"板芙镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"中山","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"石岐街道","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"东区街道","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"中山港街道","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"西区街道","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"南区街道","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"五桂山街道","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"民众街道","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"翠亨新区","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"黄圃镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"东凤镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"古镇镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"沙溪镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"坦洲镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"港口镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"三角镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"横栏镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"南头镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"阜沙镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"三乡镇","pic":"SL_3","publishtime":"2025-04-01 15:45:00","warn":"森林火险黄色"}],"韶关":[{"bgcolor":"#ffff00","city":"翁源","pic":"SL_3","publishtime":"2025-04-05 11:44:00","warn":"森林火险黄色"}],"肇庆":[{"bgcolor":"#ca0202","city":"肇庆","pic":"SL_5","publishtime":"2025-04-03 10:44:00","warn":"森林火险红色"},{"bgcolor":"#ca0202","city":"怀集","pic":"SL_5","publishtime":"2025-04-03 11:00:00","warn":"森林火险红色"},{"bgcolor":"#ca0202","city":"封开","pic":"SL_5","publishtime":"2025-04-03 11:09:00","warn":"森林火险红色"},{"bgcolor":"#ca0202","city":"高要","pic":"SL_5","publishtime":"2025-04-03 11:10:00","warn":"森林火险红色"},{"bgcolor":"#ca0202","city":"德庆","pic":"SL_5","publishtime":"2025-04-03 11:15:00","warn":"森林火险红色"},{"bgcolor":"#ca0202","city":"四会","pic":"SL_5","publishtime":"2025-04-03 11:18:00","warn":"森林火险红色"},{"bgcolor":"#ca0202","city":"广宁","pic":"SL_5","publishtime":"2025-04-03 11:25:00","warn":"森林火险红色"}],"汕头":[{"bgcolor":"#ffff00","city":"汕头","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"南澳","pic":"SL_3","publishtime":"2025-04-05 10:10:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"澄海","pic":"SL_3","publishtime":"2025-04-05 10:12:00","warn":"森林火险黄色"},{"bgcolor":"#fd9903","city":"潮阳","pic":"SL_4","publishtime":"2025-04-06 15:51:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"汕头市潮南区","pic":"SL_4","publishtime":"2025-04-06 15:56:00","warn":"森林火险橙色"},{"bgcolor":"#ffff00","city":"潮阳","pic":"DW_3","publishtime":"2025-04-11 06:56:00","warn":"大雾黄色"}],"惠州":[{"bgcolor":"#ffff00","city":"惠州市仲恺区","pic":"SL_3","publishtime":"2025-04-09 10:26:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"大亚湾","pic":"SL_3","publishtime":"2025-04-09 10:26:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"惠州市惠城区","pic":"SL_3","publishtime":"2025-04-09 10:26:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"惠州","pic":"SL_3","publishtime":"2025-04-09 10:26:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"惠阳","pic":"SL_3","publishtime":"2025-04-09 10:37:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"惠东","pic":"SL_3","publishtime":"2025-04-09 10:46:00","warn":"森林火险黄色"}],"广州":[{"bgcolor":"#ffff00","city":"广州市南沙区榄核镇","pic":"SL_3","publishtime":"2025-03-29 18:58:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市南沙区大岗镇","pic":"SL_3","publishtime":"2025-03-29 18:58:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市南沙区南沙街","pic":"SL_3","publishtime":"2025-03-29 18:58:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市南沙区东涌镇","pic":"SL_3","publishtime":"2025-03-29 18:58:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"南沙","pic":"SL_3","publishtime":"2025-03-29 18:58:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市南沙区黄阁镇","pic":"SL_3","publishtime":"2025-03-29 18:58:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市南沙区珠江街","pic":"SL_3","publishtime":"2025-03-29 18:58:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市南沙区龙穴街","pic":"SL_3","publishtime":"2025-03-29 18:58:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市南沙区万顷沙镇","pic":"SL_3","publishtime":"2025-03-29 18:58:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市南沙区横沥镇","pic":"SL_3","publishtime":"2025-03-29 18:58:00","warn":"森林火险黄色"},{"bgcolor":"#fd9903","city":"广州市花都区炭步镇","pic":"SL_4","publishtime":"2025-04-03 10:05:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市花都区赤坭镇","pic":"SL_4","publishtime":"2025-04-03 10:05:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市花都区狮岭镇","pic":"SL_4","publishtime":"2025-04-03 10:05:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市花都区花东镇","pic":"SL_4","publishtime":"2025-04-03 10:05:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市花都区花山镇","pic":"SL_4","publishtime":"2025-04-03 10:05:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市花都区梯面镇","pic":"SL_4","publishtime":"2025-04-03 10:05:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市花都区新雅街","pic":"SL_4","publishtime":"2025-04-03 10:05:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市花都区秀全街","pic":"SL_4","publishtime":"2025-04-03 10:05:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市花都区花城街","pic":"SL_4","publishtime":"2025-04-03 10:05:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"花都","pic":"SL_4","publishtime":"2025-04-03 10:05:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市花都区新华街","pic":"SL_4","publishtime":"2025-04-03 10:05:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区小楼镇","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区仙村镇","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"增城","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区荔城街","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区增江街","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区朱村街","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区永宁街","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区荔湖街","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区宁西街","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区新塘镇","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区石滩镇","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区中新镇","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区正果镇","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市增城区派潭镇","pic":"SL_4","publishtime":"2025-04-03 10:15:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"白云","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区三元里街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区松洲街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区景泰街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区同德街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区黄石街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区棠景街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区新市街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区同和街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区京溪街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区永平街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区嘉禾街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区均禾街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区石井街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区金沙街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区云城街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区鹤龙街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区白云湖街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区石门街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区龙归街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区大源街","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区人和镇","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区太和镇","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区钟落潭镇","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市白云区江高镇","pic":"SL_4","publishtime":"2025-04-03 10:16:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市从化区城郊街","pic":"SL_4","publishtime":"2025-04-03 10:20:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"从化","pic":"SL_4","publishtime":"2025-04-03 10:20:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市从化区街口街","pic":"SL_4","publishtime":"2025-04-03 10:20:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市从化区江埔街","pic":"SL_4","publishtime":"2025-04-03 10:20:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市从化区鳌头镇","pic":"SL_4","publishtime":"2025-04-03 10:20:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市从化区温泉镇","pic":"SL_4","publishtime":"2025-04-03 10:20:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市从化区良口镇","pic":"SL_4","publishtime":"2025-04-03 10:20:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市从化区吕田镇","pic":"SL_4","publishtime":"2025-04-03 10:20:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市从化区太平镇","pic":"SL_4","publishtime":"2025-04-03 10:20:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区黄埔街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区新龙镇","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区龙湖街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区九佛街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区长岭街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区永和街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"黄埔","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区红山街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区鱼珠街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区大沙街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区文冲街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区穗东街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区南岗街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区长洲街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区夏港街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区萝岗街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区云埔街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"广州市黄埔区联和街道","pic":"SL_4","publishtime":"2025-04-03 10:50:00","warn":"森林火险橙色"},{"bgcolor":"#ffff00","city":"天河","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区五山街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区员村街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区车陂街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区沙河街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区石牌街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区沙东街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区天河南街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区林和街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区兴华街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区棠下街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区天园街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区猎德街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区冼村街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区元岗街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区黄村街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区长兴街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区龙洞街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区凤凰街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区前进街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区珠吉街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市天河区新塘街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区光塔街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区人民街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区东山街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区农林街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区梅花村街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区黄花岗街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区华乐街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区建设街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区大塘街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区珠光街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区大东街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区白云街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区登峰街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区矿泉街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"越秀","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区洪桥街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区北京街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区六榕街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市越秀区流花街道","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州","pic":"SL_3","publishtime":"2025-04-05 10:15:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区新造镇","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区南村镇","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区石楼镇","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区沙湾街","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区石碁镇","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"番禺","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区市桥街","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区沙头街","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区东环街","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区桥南街","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区小谷围街","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区大石街","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区洛浦街","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区石壁街","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区钟村街","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区大龙街","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市番禺区化龙镇","pic":"SL_3","publishtime":"2025-04-05 10:23:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"海珠","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区官洲街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区新港街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区昌岗街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区江南中街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区滨江街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区素社街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区海幢街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区南华西街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区龙凤街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区沙园街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区南石头街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区凤阳街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区瑞宝街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区江海街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区琶洲街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区南洲街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区华洲街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市海珠区赤岗街道","pic":"SL_3","publishtime":"2025-04-05 10:24:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"荔湾","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区中南街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区岭南街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区华林街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区多宝街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区昌华街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区逢源街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区龙津街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区金花街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区彩虹街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区南源街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区西村街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区站前街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区桥中街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区白鹤洞街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区冲口街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区花地街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区石围塘街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区茶滘街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区东漖街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区海龙街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区东沙街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"广州市荔湾区沙面街道","pic":"SL_3","publishtime":"2025-04-05 10:27:00","warn":"森林火险黄色"}],"云浮":[{"bgcolor":"#fd9903","city":"罗定","pic":"SL_4","publishtime":"2025-04-03 10:00:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"云浮","pic":"SL_4","publishtime":"2025-04-03 10:00:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"新兴","pic":"SL_4","publishtime":"2025-04-03 10:03:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"郁南","pic":"SL_4","publishtime":"2025-04-03 10:08:00","warn":"森林火险橙色"}],"珠海":[{"bgcolor":"#ffff00","city":"珠海市万山区","pic":"SL_3","publishtime":"2025-04-05 10:20:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"斗门","pic":"SL_3","publishtime":"2025-04-05 10:20:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"珠海市横琴粤澳深度合作区","pic":"SL_3","publishtime":"2025-04-05 10:20:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"珠海经济技术开发区","pic":"SL_3","publishtime":"2025-04-05 10:20:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"珠海市金湾区","pic":"SL_3","publishtime":"2025-04-05 10:20:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"珠海市香洲区","pic":"SL_3","publishtime":"2025-04-05 10:20:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"珠海市高新区","pic":"SL_3","publishtime":"2025-04-05 10:20:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"珠海","pic":"SL_3","publishtime":"2025-04-05 10:20:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"珠海市万山区","pic":"DW_3","publishtime":"2025-04-11 04:00:00","warn":"大雾黄色"},{"bgcolor":"#ffff00","city":"珠海","pic":"DW_3","publishtime":"2025-04-11 04:00:00","warn":"大雾黄色"},{"bgcolor":"#ffff00","city":"珠海市高新区","pic":"DW_3","publishtime":"2025-04-11 06:50:00","warn":"大雾黄色"}],"清远":[{"bgcolor":"#fd9903","city":"佛冈","pic":"SL_4","publishtime":"2025-04-05 09:58:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"清远","pic":"SL_4","publishtime":"2025-04-05 10:00:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"清新","pic":"SL_4","publishtime":"2025-04-05 10:20:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"阳山","pic":"SL_4","publishtime":"2025-04-09 16:07:00","warn":"森林火险橙色"},{"bgcolor":"#ffff00","city":"英德","pic":"SL_3","publishtime":"2025-04-10 09:30:00","warn":"森林火险黄色"}],"茂名":[{"bgcolor":"#fd9903","city":"化州","pic":"SL_4","publishtime":"2025-03-21 10:25:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"电白","pic":"SL_4","publishtime":"2025-03-21 11:04:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"茂名","pic":"SL_4","publishtime":"2025-03-21 11:35:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"高州","pic":"SL_4","publishtime":"2025-04-03 09:53:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"信宜","pic":"SL_4","publishtime":"2025-04-06 14:55:00","warn":"森林火险橙色"}],"潮州":[{"bgcolor":"#ffff00","city":"潮州","pic":"SL_3","publishtime":"2025-04-07 15:14:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"饶平","pic":"SL_3","publishtime":"2025-04-07 15:21:00","warn":"森林火险黄色"}],"阳江":[{"bgcolor":"#fd9903","city":"阳江","pic":"SL_4","publishtime":"2025-03-21 10:00:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"阳春","pic":"SL_4","publishtime":"2025-03-21 10:23:00","warn":"森林火险橙色"},{"bgcolor":"#fd9903","city":"阳西","pic":"SL_4","publishtime":"2025-03-21 11:11:00","warn":"森林火险橙色"},{"bgcolor":"#ffff00","city":"阳江","pic":"DW_3","publishtime":"2025-04-11 06:16:00","warn":"大雾黄色"}],"佛山":[{"bgcolor":"#ffff00","city":"佛山市高明区更合镇","pic":"SL_3","publishtime":"2025-04-05 10:00:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"高明","pic":"SL_3","publishtime":"2025-04-05 10:00:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市高明区荷城街道","pic":"SL_3","publishtime":"2025-04-05 10:00:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市高明区杨和镇","pic":"SL_3","publishtime":"2025-04-05 10:00:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市高明区明城镇","pic":"SL_3","publishtime":"2025-04-05 10:00:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市顺德区龙江镇","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市顺德区杏坛镇","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市顺德区均安镇","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市顺德区乐从镇","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市顺德区北滘镇","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市顺德区陈村镇","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市顺德区容桂街道","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市顺德区大良街道","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市顺德区勒流街道","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"顺德","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市顺德区伦教街道","pic":"SL_3","publishtime":"2025-04-05 10:05:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市南海区大沥镇","pic":"SL_3","publishtime":"2025-04-05 10:08:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"南海","pic":"SL_3","publishtime":"2025-04-05 10:08:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市南海区狮山镇","pic":"SL_3","publishtime":"2025-04-05 10:08:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市南海区九江镇","pic":"SL_3","publishtime":"2025-04-05 10:08:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市南海区西樵镇","pic":"SL_3","publishtime":"2025-04-05 10:08:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市南海区丹灶镇","pic":"SL_3","publishtime":"2025-04-05 10:08:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市南海区里水镇","pic":"SL_3","publishtime":"2025-04-05 10:08:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市南海区桂城街道","pic":"SL_3","publishtime":"2025-04-05 10:08:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市三水区芦苞镇","pic":"SL_3","publishtime":"2025-04-05 10:09:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市三水区南山镇","pic":"SL_3","publishtime":"2025-04-05 10:09:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市三水区白坭镇","pic":"SL_3","publishtime":"2025-04-05 10:09:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"三水","pic":"SL_3","publishtime":"2025-04-05 10:09:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市三水区西南街道","pic":"SL_3","publishtime":"2025-04-05 10:09:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市三水区云东海街道","pic":"SL_3","publishtime":"2025-04-05 10:09:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市三水区大塘镇","pic":"SL_3","publishtime":"2025-04-05 10:09:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市三水区乐平镇","pic":"SL_3","publishtime":"2025-04-05 10:09:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市禅城区南庄镇","pic":"SL_3","publishtime":"2025-04-05 10:10:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市禅城区祖庙街道","pic":"SL_3","publishtime":"2025-04-05 10:10:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市禅城区张槎街道","pic":"SL_3","publishtime":"2025-04-05 10:10:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市禅城区石湾镇街道","pic":"SL_3","publishtime":"2025-04-05 10:10:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山市禅城区","pic":"SL_3","publishtime":"2025-04-05 10:10:00","warn":"森林火险黄色"},{"bgcolor":"#ffff00","city":"佛山","pic":"SL_3","publishtime":"2025-04-05 10:10:00","warn":"森林火险黄色"}]});[{"n":"东莞","pm_aqi":"25","pm_pm25":"17","r":"441900","sk_h":"79","sk_i":"http://wap.gz121.com/html/weixinportal/images/weathericon/icon_weather_day_01.png","sk_p":"1004","sk_r1h":"0","sk_s":"多云","sk_t":"25.5","sk_time":"2025-04-11 10:10:00","sk_wd":"181","sk_wp":"3.3","sk_wp_level":"3"}]
	at com.cesc.ewater.util.convert.JsonConvertUtil.fromJsonString(JsonConvertUtil.java:33)
	at com.cesc.ewater.biz.oneMapManager.screen.AutoWeatherQueryTask.autoActualQuery(AutoWeatherQueryTask.java:86)
	at com.cesc.ewater.biz.oneMapManager.screen.AutoWeatherQueryTask$$FastClassBySpringCGLIB$$778952b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.fasterxml.jackson.core.JsonParseException: Unrecognized token 'yjlist': was expecting (JSON String, Number, Array, Object or token 'null', 'true' or 'false')
 at [Source: (StringReader); line: 1, column: 7]
	at com.fasterxml.jackson.core.JsonParser._constructError(JsonParser.java:2337)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._reportError(ParserMinimalBase.java:720)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._reportInvalidToken(ReaderBasedJsonParser.java:2902)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._handleOddValue(ReaderBasedJsonParser.java:1949)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser.nextToken(ReaderBasedJsonParser.java:781)
	at com.fasterxml.jackson.databind.ObjectMapper._initForReading(ObjectMapper.java:4684)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4586)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.cesc.ewater.util.convert.JsonConvertUtil.fromJsonString(JsonConvertUtil.java:31)
	... 16 common frames omitted
2025-04-11 10:40:00.126 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 10:50:00.125 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 11:00:00.126 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 11:10:00.157 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 11:20:00.125 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 11:30:00.095 ERROR com.cesc.ewater.biz.oneMapManager.screen.AutoWeatherQueryTask - 获取实时天气信息失败。
java.lang.RuntimeException: <html>

<head>
  <title>Error</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, minimum-scale=1, maximum-scale=1">
  <style type="text/css">
    /* CSS reset */
    html,
    body,
    div,
    span,
    applet,
    object,
    iframe,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    blockquote,
    pre,
    a,
    abbr,
    acronym,
    address,
    big,
    cite,
    code,
    del,
    dfn,
    em,
    img,
    ins,
    kbd,
    q,
    s,
    samp,
    small,
    strike,
    strong,
    sub,
    sup,
    tt,
    var,
    b,
    u,
    i,
    center,
    dl,
    dt,
    dd,
    ol,
    ul,
    li,
    fieldset,
    form,
    label,
    legend,
    table,
    caption,
    tbody,
    tfoot,
    thead,
    tr,
    th,
    td,
    article,
    aside,
    canvas,
    details,
    embed,
    figure,
    figcaption,
    footer,
    header,
    hgroup,
    menu,
    nav,
    output,
    ruby,
    section,
    summary,
    time,
    mark,
    audio,
    video {
      margin: 0;
      padding: 0;
      border: 0;
      font-size: 100%;
      font: inherit;
      vertical-align: baseline;
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    hgroup,
    menu,
    nav,
    section {
      display: block;
    }

    body {
      line-height: 1;
    }

    ol,
    ul {
      list-style: none;
    }

    blockquote,
    q {
      quotes: none;
    }

    blockquote:before,
    blockquote:after,
    q:before,
    q:after {
      content: '';
      content: none;
    }

    table {
      border-collapse: collapse;
      border-spacing: 0;
    }

    /* Style */
    html,
    body {
      font-family: sans-serif;
      font-size: 16px;
      line-height: 1.4;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
    }

    body {
      height: 100%;
      background: #ffffff;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }

    main {
      height: calc(100% - 50px);
      padding: 25px;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      text-align: center;
    }

    main .main_error-code {
      font-size: calc(4.86188vw + 65.76796px);
      line-height: 1;
      text-transform: uppercase;
      color: #191919;
    }

    @media screen and (max-width: 374px) {
      main .main_error-code {
        font-size: 84px;
      }
    }

    @media screen and (min-width: 1281px) {
      main .main_error-code {
        font-size: 128px;
      }
    }

    main .main_error-title {
      font-size: calc(0.88398vw + 18.68508px);
      text-transform: capitalize;
      color: #191919;
      margin-bottom: calc(0.44199vw + 18.34254px);
    }

    @media screen and (max-width: 374px) {
      main .main_error-title {
        font-size: 22px;
      }
    }

    @media screen and (min-width: 1281px) {
      main .main_error-title {
        font-size: 30px;
      }
    }

    @media screen and (max-width: 374px) {
      main .main_error-title {
        margin-bottom: 20px;
      }
    }

    @media screen and (min-width: 1281px) {
      main .main_error-title {
        margin-bottom: 24px;
      }
    }

    main .main_error-message {
      font-size: calc(0.44199vw + 14.34254px);
      color: #191919;
    }

    @media screen and (max-width: 374px) {
      main .main_error-message {
        font-size: 16px;
      }
    }

    @media screen and (min-width: 1281px) {
      main .main_error-message {
        font-size: 20px;
      }
    }

    main .main_error-message:last-of-type {
      margin-bottom: calc(0.44199vw + 18.34254px);
    }

    @media screen and (max-width: 374px) {
      main .main_error-message:last-of-type {
        margin-bottom: 20px;
      }
    }

    @media screen and (min-width: 1281px) {
      main .main_error-message:last-of-type {
        margin-bottom: 24px;
      }
    }

    /*# sourceMappingURL=style.css.map */
  </style>
</head>

<body>
  <main>
    <h2 class="main_error-code">50X</h2>
    <p class="main_error-message">Sorry, the page you are looking for is currently unavailable.</p>
  </main>
</body>

</html>
	at com.cesc.ewater.util.convert.JsonConvertUtil.fromJsonString(JsonConvertUtil.java:33)
	at com.cesc.ewater.biz.oneMapManager.screen.AutoWeatherQueryTask.autoActualQuery(AutoWeatherQueryTask.java:86)
	at com.cesc.ewater.biz.oneMapManager.screen.AutoWeatherQueryTask$$FastClassBySpringCGLIB$$778952b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.fasterxml.jackson.core.JsonParseException: Unexpected character ('<' (code 60)): expected a valid value (JSON String, Number, Array, Object or token 'null', 'true' or 'false')
 at [Source: (String)"<html>

<head>
  <title>Error</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, minimum-scale=1, maximum-scale=1">
  <style type="text/css">
    /* CSS reset */
    html,
    body,
    div,
    span,
    applet,
    object,
    iframe,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    blockquote,
    pre,
    a,
    abbr,
    acronym,
    address,
    big,
    cite,
    code,
"[truncated 4386 chars]; line: 1, column: 2]
	at com.fasterxml.jackson.core.JsonParser._constructError(JsonParser.java:2337)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._reportError(ParserMinimalBase.java:710)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._reportUnexpectedChar(ParserMinimalBase.java:635)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._handleOddValue(ReaderBasedJsonParser.java:1952)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser.nextToken(ReaderBasedJsonParser.java:781)
	at com.fasterxml.jackson.databind.ObjectMapper._initForReading(ObjectMapper.java:4684)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4586)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.cesc.ewater.util.convert.JsonConvertUtil.fromJsonString(JsonConvertUtil.java:31)
	... 16 common frames omitted
2025-04-11 11:30:00.123 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 11:40:00.122 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 11:50:00.120 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 12:00:00.126 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 12:10:00.123 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 12:20:00.125 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 12:30:00.127 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 12:40:00.129 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 12:50:00.124 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 13:00:00.130 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 13:10:00.128 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 13:20:00.125 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 13:30:00.134 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 13:40:00.130 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 13:50:00.123 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 14:00:00.139 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 14:10:00.129 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 14:20:00.123 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 14:30:00.124 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 14:40:00.122 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 14:50:00.123 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 15:00:00.130 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 15:10:00.125 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 15:20:00.124 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 15:30:00.132 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 15:38:18.781 ERROR com.cesc.ewater.util.web.ResponseResult - StatementCallback; bad SQL grammar [select *,sde.st_astext(t.shape) as shapevaluewkt  from PS_PIPE_ZY t  where objectid = ]; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at end of input
  Position: 87
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [select *,sde.st_astext(t.shape) as shapevaluewkt  from PS_PIPE_ZY t  where objectid = ]; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at end of input
  Position: 87
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1541)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:393)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:465)
	at org.springframework.jdbc.core.JdbcTemplate.queryForRowSet(JdbcTemplate.java:530)
	at com.cesc.ewater.biz.pipe.util.PipeQueryUtil.getQueryFeatureFromResultSet(PipeQueryUtil.java:394)
	at com.cesc.ewater.biz.pipe.service.QueryPipeByWktService.queryLayerFeatureByObjectId(QueryPipeByWktService.java:610)
	at com.cesc.ewater.biz.pipe.controller.PipeQueryController.queryLayerFeatureByObjectId(PipeQueryController.java:287)
	at com.cesc.ewater.biz.pipe.controller.PipeQueryController$$FastClassBySpringCGLIB$$226bef9c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.cesc.ewater.framework.login.LoginInterceptor.Interceptor(LoginInterceptor.java:154)
	at jdk.internal.reflect.GeneratedMethodAccessor309.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)
	at com.cesc.ewater.biz.pipe.controller.PipeQueryController$$EnhancerBySpringCGLIB$$7000eb07.queryLayerFeatureByObjectId(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: org.postgresql.util.PSQLException: ERROR: syntax error at end of input
  Position: 87
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2510)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2245)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:311)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:447)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:368)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:309)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:295)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:272)
	at org.postgresql.jdbc.PgStatement.executeQuery(PgStatement.java:225)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate$1QueryStatementCallback.doInStatement(JdbcTemplate.java:452)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:381)
	... 79 common frames omitted
2025-04-11 15:40:00.126 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 15:50:00.122 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 16:00:00.128 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 16:10:00.124 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 16:20:00.135 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 16:30:00.126 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 16:30:00.177 ERROR com.cesc.ewater.biz.oneMapManager.screen.AutoWeatherQueryTask - 获取实时天气信息失败。
java.lang.RuntimeException: 
	at com.cesc.ewater.util.convert.JsonConvertUtil.fromJsonString(JsonConvertUtil.java:33)
	at com.cesc.ewater.biz.oneMapManager.screen.AutoWeatherQueryTask.autoActualQuery(AutoWeatherQueryTask.java:86)
	at com.cesc.ewater.biz.oneMapManager.screen.AutoWeatherQueryTask$$FastClassBySpringCGLIB$$778952b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.fasterxml.jackson.databind.exc.MismatchedInputException: No content to map due to end-of-input
 at [Source: (String)""; line: 1, column: 0]
	at com.fasterxml.jackson.databind.exc.MismatchedInputException.from(MismatchedInputException.java:59)
	at com.fasterxml.jackson.databind.ObjectMapper._initForReading(ObjectMapper.java:4688)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4586)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3548)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3516)
	at com.cesc.ewater.util.convert.JsonConvertUtil.fromJsonString(JsonConvertUtil.java:31)
	... 16 common frames omitted
2025-04-11 16:40:00.124 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 16:50:00.120 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 17:00:00.130 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 17:10:00.122 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 17:20:00.123 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 17:30:00.123 ERROR com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask - 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
java.lang.Exception: 分析点中有重复的月份数据====PointAnalysisInfo:2;PointAnalysis:2
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask.checkRepeatPoint(checkRepeatPointAnalysisTask.java:65)
	at com.cesc.ewater.biz.improveEfficiency.task.checkRepeatPointAnalysisTask$$FastClassBySpringCGLIB$$b85374a2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
