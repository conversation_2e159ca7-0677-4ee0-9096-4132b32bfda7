10:07:46.536 [main] INFO  com.cesc.ewater.app.Application - Starting Application using Java 11.0.27 on liunanArch with PID 181004 (/home/<USER>/Documents/pipe-network/dgps-gwxj-server/ewater/target/classes started by liunan in /home/<USER>/Documents/pipe-network)
10:07:46.547 [main] INFO  com.cesc.ewater.app.Application - The following 1 profile is active: "dev"
10:07:48.993 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
10:07:48.994 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
10:07:49.683 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 674 ms. Found 104 JPA repository interfaces.
10:07:49.685 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
10:07:49.685 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
10:07:49.799 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 112 ms. Found 6 JPA repository interfaces.
10:07:49.800 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
10:07:49.800 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
10:07:49.835 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.
10:07:50.626 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
10:07:50.627 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
10:07:50.631 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Elasticsearch repository interfaces.
10:07:50.638 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
10:07:50.639 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
10:07:50.642 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 2 ms. Found 0 Reactive Elasticsearch repository interfaces.
10:07:50.669 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
10:07:50.671 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
10:07:50.682 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 Redis repository interfaces.
10:07:51.384 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=06b86885-641c-367f-83b9-ae14167a8323
10:07:51.984 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jedisRedisConfig' of type [com.cesc.ewater.framework.config.JedisRedisConfig$$EnhancerBySpringCGLIB$$c97dac4c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:07:52.041 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jedisConnectionFactory' of type [org.springframework.data.redis.connection.jedis.JedisConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:07:52.061 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.cesc.ewater.framework.config.RedisConfig$$EnhancerBySpringCGLIB$$c693c261] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:07:52.182 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisTemplate' of type [org.springframework.data.redis.core.StringRedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:07:52.248 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'TokenManagerService' of type [com.cesc.ewater.framework.login.service.TokenManagerService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:07:52.253 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loginUserServiceImpl' of type [com.cesc.ewater.common.authority.service.impl.LoginUserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:07:52.651 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 35693 (http)
10:07:52.664 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
10:07:52.665 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
10:07:52.797 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
10:07:52.798 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6088 ms
10:07:53.357 [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
10:07:53.817 [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
10:07:54.034 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
10:07:54.113 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.33
10:07:54.281 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
10:07:54.514 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: com.cesc.ewater.framework.config.MySQL57DialectNotFK
10:07:54.904 [main] INFO  org.hibernate.orm.beans - HHH10005002: No explicit CDI BeanManager reference was passed to Hibernate, but CDI is available on the Hibernate ClassLoader.
10:07:57.160 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
10:07:57.177 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
10:07:57.212 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
10:07:57.237 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: com.cesc.ewater.framework.config.MySQL57DialectNotFK
10:07:57.302 [main] INFO  org.hibernate.orm.beans - HHH10005002: No explicit CDI BeanManager reference was passed to Hibernate, but CDI is available on the Hibernate ClassLoader.
10:07:58.359 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
10:07:58.359 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
10:07:58.376 [main] INFO  com.zaxxer.hikari.HikariDataSource - default - Starting...
10:07:58.438 [main] INFO  com.zaxxer.hikari.HikariDataSource - default - Start completed.
10:07:58.473 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
10:07:58.656 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL94Dialect
10:07:58.705 [main] INFO  org.hibernate.orm.beans - HHH10005002: No explicit CDI BeanManager reference was passed to Hibernate, but CDI is available on the Hibernate ClassLoader.
10:07:58.742 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
10:07:58.743 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
10:07:58.771 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
10:07:58.932 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL94Dialect
10:07:58.955 [main] INFO  org.hibernate.orm.beans - HHH10005002: No explicit CDI BeanManager reference was passed to Hibernate, but CDI is available on the Hibernate ClassLoader.
10:07:58.986 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
10:07:58.986 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
10:07:58.991 [main] INFO  com.zaxxer.hikari.HikariDataSource - default - Starting...
10:07:58.996 [main] INFO  com.zaxxer.hikari.HikariDataSource - default - Start completed.
10:07:59.031 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
10:07:59.221 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL94Dialect
10:07:59.247 [main] INFO  org.hibernate.orm.beans - HHH10005002: No explicit CDI BeanManager reference was passed to Hibernate, but CDI is available on the Hibernate ClassLoader.
10:07:59.262 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
10:07:59.262 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
10:07:59.293 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
10:07:59.450 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL94Dialect
10:07:59.466 [main] INFO  org.hibernate.orm.beans - HHH10005002: No explicit CDI BeanManager reference was passed to Hibernate, but CDI is available on the Hibernate ClassLoader.
10:07:59.476 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
10:07:59.476 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
10:08:01.234 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'ewater-server' URL not provided. Will try picking an instance via load-balancing.
10:08:03.111 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'ewater-server' URL not provided. Will try picking an instance via load-balancing.
10:08:03.319 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'ewater-server' URL not provided. Will try picking an instance via load-balancing.
10:08:03.356 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'ewater-server' URL not provided. Will try picking an instance via load-balancing.
10:08:04.018 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'ewater-server' URL not provided. Will try picking an instance via load-balancing.
10:08:05.367 [main] INFO  o.a.e.c.DefaultActiviti5CompatibilityHandlerFactory - Activiti 5 compatibility handler implementation not found or error during instantiation : org.activiti.compatibility.DefaultActiviti5CompatibilityHandler. Activiti 5 backwards compatibility disabled.
10:08:05.435 [main] INFO  o.a.engine.impl.ProcessEngineImpl - ProcessEngine default created
10:08:05.436 [main] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
10:08:05.436 [Thread-3] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - {} starting to acquire async jobs due
10:08:05.437 [Thread-4] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - {} starting to acquire async jobs due
10:08:05.437 [Thread-5] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - {} starting to reset expired jobs
10:08:06.617 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'psgs-server' URL not provided. Will try picking an instance via load-balancing.
10:08:07.701 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'ewater-server' URL not provided. Will try picking an instance via load-balancing.
10:08:08.031 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'psgs-server' URL not provided. Will try picking an instance via load-balancing.
10:08:08.067 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'ewater-server' URL not provided. Will try picking an instance via load-balancing.
10:08:08.838 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'ewater-server' URL not provided. Will try picking an instance via load-balancing.
10:08:12.498 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'ewater-server' URL not provided. Will try picking an instance via load-balancing.
10:08:13.986 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'psgs-server' URL not provided. Will try picking an instance via load-balancing.
10:08:16.216 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'dgps-safeprod-server' URL not provided. Will try picking an instance via load-balancing.
10:08:21.716 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
10:08:21.731 [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:08:21.732 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.2.3 created.
10:08:21.733 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
10:08:21.734 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.2.3) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:08:21.734 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:08:21.734 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.2.3
10:08:21.734 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@58c868b5
10:08:21.902 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 19 endpoint(s) beneath base path '/cmonprome'
10:08:22.475 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
10:08:23.655 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 35693 (http) with context path ''
10:08:23.660 [main] INFO  o.s.c.c.s.ConsulServiceRegistry - Registering service with consul: NewService{id='gwxj-server', name='gwxj-server', tags=[], address='*************', meta={secure=false}, port=35693, enableTagOverride=null, check=Check{script='null', dockerContainerID='null', shell='null', interval='10s', ttl='null', http='http://*************:35693/actuator/health', method='null', header={}, tcp='null', timeout='null', deregisterCriticalServiceAfter='null', tlsSkipVerify=null, status='null', grpc='null', grpcUseTLS=null}, checks=null}
10:08:23.680 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
10:08:23.699 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
10:08:23.749 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
10:08:23.995 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: qryWithIdUsingPOST_1
10:08:24.237 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getPipeListUsingPOST_1
10:08:24.245 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getWellListUsingPOST_1
10:08:24.301 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchDelUsingPOST_1
10:08:24.302 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_1
10:08:24.303 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
10:08:24.316 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
10:08:24.317 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@14b0d471]]
10:08:24.317 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
10:08:24.317 [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
10:08:24.317 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:08:24.328 [main] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - More than one TaskScheduler bean exists within the context, and none is named 'taskScheduler'. Mark one of them as primary or name it 'taskScheduler' (possibly as an alias); or implement the SchedulingConfigurer interface and call ScheduledTaskRegistrar#setScheduler explicitly within the configureTasks() callback: [messageBrokerTaskScheduler, catalogWatchTaskScheduler]
10:08:24.336 [main] INFO  com.cesc.ewater.app.Application - Started Application in 38.518 seconds (JVM running for 40.45)
10:08:24.341 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
10:08:24.351 [pool-4-thread-1] INFO  c.c.e.b.s.s.StartHandleCompressPic - start check compress picture state:false
10:08:24.390 [main] INFO  com.cesc.ewater.app.Application - the gwxj application is start success.
10:08:24.390 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68392的巡检距离与关联管线的距离
10:08:24.495 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68392的巡检距离:nu巡检数：0,30分钟后尝试
10:08:24.533 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68392的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
10:08:24.534 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68395的巡检距离与关联管线的距离
10:08:24.541 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68395的巡检距离:nu巡检数：0,30分钟后尝试
10:08:24.543 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68395的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
10:08:24.543 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68397的巡检距离与关联管线的距离
10:08:24.550 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68397的巡检距离:nu巡检数：0,30分钟后尝试
10:08:24.553 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68397的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
10:08:24.553 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68402的巡检距离与关联管线的距离
10:08:24.559 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68402的巡检距离:nu巡检数：0,30分钟后尝试
10:08:24.562 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68402的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
10:08:24.562 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68425的巡检距离与关联管线的距离
10:08:24.569 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68425的巡检距离:nu巡检数：0,30分钟后尝试
10:08:24.571 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68425的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
10:08:24.572 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68435的巡检距离与关联管线的距离
10:08:24.579 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68435的巡检距离:nu巡检数：0,30分钟后尝试
10:08:24.581 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68435的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
10:08:24.581 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68451的巡检距离与关联管线的距离
10:08:24.588 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68451的巡检距离:nu巡检数：0,30分钟后尝试
10:08:24.592 [attendCount-1] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68451的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
10:08:29.173 [http-nio-35693-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:08:29.173 [http-nio-35693-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
10:08:29.178 [http-nio-35693-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
10:09:22.342 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 1, active threads = 0, queued tasks = 0, completed tasks = 1], outboundChannel[pool size = 1, active threads = 0, queued tasks = 0, completed tasks = 1], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
10:14:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
10:20:24.336 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
10:26:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
10:32:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
10:38:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
10:39:22.343 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 4], outboundChannel[pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 4], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
10:44:24.336 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
10:50:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
10:56:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
11:00:00.002 [pool-5-thread-1] INFO  c.c.e.b.p.e.s.ExternalPushServiceImpl - ----date:2025-08-11 11:00:00,start check external push----
11:00:01.370 [pool-5-thread-1] INFO  c.c.e.b.p.e.s.ExternalPushServiceImpl - ----date:2025-08-11 11:00:01,end check external push----
11:01:15.415 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
11:01:15.414 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countToBeHandling(Dto)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[Dto(token=72c10039b5e9450e9c377a8f8c5f3dbf)]
11:01:15.415 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectPlanController.getProjectPlanDealWithCount(StringValForm,HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[StringValForm(val=Y), org.apache.catalina.connector.RequestFacade@67d86ae1]
11:01:15.421 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countHomePage(HomePageDto)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"],"upcomingTask":["0"],"pipelineData":["0"],"workOrderData":["0"],"safePro":["0"]}
请求体:[HomePageDto(upcomingTask=0, pipelineData=0, workOrderData=0, safePro=0)]
11:01:16.071 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
11:02:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
11:03:29.432 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
11:03:29.437 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectController.getUserGridStreetDictTree()
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[]
11:03:29.450 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.GridController.currentUserCompanyTownDictTree(HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
11:03:29.467 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV2(RequestParams,HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@40712553, org.apache.catalina.connector.RequestFacade@72f847dc]
11:03:29.467 [http-nio-35693-exec-9] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV3(RequestParams,HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@5c0abc8e, org.apache.catalina.connector.RequestFacade@64fa0f73]
11:03:29.680 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteList(RouteForm)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCountS...
11:03:29.680 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteAreaList(RouteForm)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=null, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCo...
11:03:30.004 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
11:03:30.052 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
11:03:30.649 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserDistrict(HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
11:03:30.655 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.maintUserManger.controller.MaintUserAttendController.checkUserIsAttend(HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
11:06:50.522 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["72c10039b5e9450e9c377a8f8c5f3dbf"],"x":["113.71151588868521"],"y":["22.990679511744723"],"bufferDistance":["0.000053644180305783206"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
11:08:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
11:09:22.343 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 7], outboundChannel[pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 7], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
11:14:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
11:20:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
11:26:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
11:32:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
11:38:24.336 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
11:39:22.344 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 10, active threads = 0, queued tasks = 0, completed tasks = 10], outboundChannel[pool size = 10, active threads = 0, queued tasks = 0, completed tasks = 10], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
11:44:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
11:50:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
11:56:24.336 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
12:00:00.030 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68392的巡检距离与关联管线的距离
12:00:00.031 [pool-5-thread-1] INFO  c.c.e.b.p.e.s.ExternalPushServiceImpl - ----date:2025-08-11 12:00:00,start check external push----
12:00:00.037 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68392的巡检距离:nu巡检数：0,30分钟后尝试
12:00:00.041 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68392的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
12:00:00.041 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68395的巡检距离与关联管线的距离
12:00:00.046 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68395的巡检距离:nu巡检数：0,30分钟后尝试
12:00:00.048 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68395的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
12:00:00.048 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68397的巡检距离与关联管线的距离
12:00:00.053 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68397的巡检距离:nu巡检数：0,30分钟后尝试
12:00:00.055 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68397的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
12:00:00.055 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68402的巡检距离与关联管线的距离
12:00:00.060 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68402的巡检距离:nu巡检数：0,30分钟后尝试
12:00:00.062 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68402的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
12:00:00.062 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68425的巡检距离与关联管线的距离
12:00:00.069 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68425的巡检距离:nu巡检数：0,30分钟后尝试
12:00:00.070 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68425的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
12:00:00.070 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68435的巡检距离与关联管线的距离
12:00:00.075 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68435的巡检距离:nu巡检数：0,30分钟后尝试
12:00:00.078 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68435的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
12:00:00.078 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68451的巡检距离与关联管线的距离
12:00:00.083 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68451的巡检距离:nu巡检数：0,30分钟后尝试
12:00:00.085 [attendCount-2] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68451的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
12:00:01.024 [pool-5-thread-1] INFO  c.c.e.b.p.e.s.ExternalPushServiceImpl - ----date:2025-08-11 12:00:01,end check external push----
12:00:01.027 [pool-5-thread-1] INFO  c.c.e.b.m.task.MaintUserAttendTask - 系统当前时间：2025-08-11 12:00:01  开始更新用户签到日志记录。
12:00:01.057 [pool-5-thread-1] INFO  c.c.e.b.m.s.MaintUserAttendService - 关闭日考勤线程池
12:00:01.057 [pool-5-thread-1] INFO  c.c.e.b.m.s.MaintUserAttendService - -- updateUserAttendDayTask 日考勤线程执行完毕 --
12:02:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
12:08:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
12:09:22.345 [MessageBroker-3] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 13, active threads = 0, queued tasks = 0, completed tasks = 13], outboundChannel[pool size = 13, active threads = 0, queued tasks = 0, completed tasks = 13], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
12:14:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
12:20:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
12:26:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
12:32:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
12:38:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
12:39:22.346 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16], outboundChannel[pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
12:44:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
12:50:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
12:56:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
13:00:00.000 [pool-5-thread-1] INFO  c.c.e.b.p.e.s.ExternalPushServiceImpl - ----date:2025-08-11 13:00:00,start check external push----
13:00:01.147 [pool-5-thread-1] INFO  c.c.e.b.p.e.s.ExternalPushServiceImpl - ----date:2025-08-11 13:00:01,end check external push----
13:02:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
13:08:24.336 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
13:09:22.347 [MessageBroker-4] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 19, active threads = 0, queued tasks = 0, completed tasks = 19], outboundChannel[pool size = 19, active threads = 0, queued tasks = 0, completed tasks = 19], sockJsScheduler[pool size = 7, active threads = 1, queued tasks = 0, completed tasks = 6]
13:14:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
13:20:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
13:26:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
13:32:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
13:38:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
13:39:22.347 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 22], outboundChannel[pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 22], sockJsScheduler[pool size = 8, active threads = 1, queued tasks = 0, completed tasks = 7]
13:44:24.336 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
13:50:24.334 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
13:56:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
14:00:00.000 [pool-5-thread-1] INFO  c.c.e.b.m.task.MaintUserAttendTask - 系统当前时间：2025-08-11 14:00:00  开始更新用户签到日志记录。
14:00:00.033 [pool-5-thread-1] INFO  c.c.e.b.m.s.MaintUserAttendService - 关闭日考勤线程池
14:00:00.033 [pool-5-thread-1] INFO  c.c.e.b.m.s.MaintUserAttendService - -- updateUserAttendDayTask 日考勤线程执行完毕 --
14:00:00.034 [pool-5-thread-1] INFO  c.c.e.b.p.e.s.ExternalPushServiceImpl - ----date:2025-08-11 14:00:00,start check external push----
14:00:01.014 [pool-5-thread-1] INFO  c.c.e.b.p.e.s.ExternalPushServiceImpl - ----date:2025-08-11 14:00:01,end check external push----
14:00:01.042 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68392的巡检距离与关联管线的距离
14:00:01.047 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68392的巡检距离:nu巡检数：0,30分钟后尝试
14:00:01.049 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68392的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
14:00:01.049 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68395的巡检距离与关联管线的距离
14:00:01.054 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68395的巡检距离:nu巡检数：0,30分钟后尝试
14:00:01.057 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68395的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
14:00:01.057 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68397的巡检距离与关联管线的距离
14:00:01.062 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68397的巡检距离:nu巡检数：0,30分钟后尝试
14:00:01.064 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68397的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
14:00:01.064 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68402的巡检距离与关联管线的距离
14:00:01.068 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68402的巡检距离:nu巡检数：0,30分钟后尝试
14:00:01.070 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68402的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
14:00:01.070 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68425的巡检距离与关联管线的距离
14:00:01.074 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68425的巡检距离:nu巡检数：0,30分钟后尝试
14:00:01.076 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68425的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
14:00:01.076 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68435的巡检距离与关联管线的距离
14:00:01.080 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68435的巡检距离:nu巡检数：0,30分钟后尝试
14:00:01.082 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68435的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
14:00:01.082 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 开始计算巡检记录id为68451的巡检距离与关联管线的距离
14:00:01.086 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 计算巡检记录id为68451的巡检距离:nu巡检数：0,30分钟后尝试
14:00:01.088 [attendCount-3] INFO  c.c.e.b.m.s.MaintUserAttendSysStartCheck - 结束计算巡检记录id为68451的巡检距离与关联管线的距离，巡检距离计算结果为null，关联管线的距离计算结果为null
14:02:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
14:08:24.334 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
14:08:37.313 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectPlanController.getProjectPlanDealWithCount(StringValForm,HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[StringValForm(val=Y), org.apache.catalina.connector.RequestFacade@72f847dc]
14:08:37.314 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
14:08:37.315 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countHomePage(HomePageDto)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"],"upcomingTask":["0"],"pipelineData":["0"],"workOrderData":["0"],"safePro":["0"]}
请求体:[HomePageDto(upcomingTask=0, pipelineData=0, workOrderData=0, safePro=0)]
14:08:37.316 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countToBeHandling(Dto)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[Dto(token=86627228416148bda1b29b4947a6c68d)]
14:08:37.836 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
14:08:48.919 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
14:08:48.962 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectController.getUserGridStreetDictTree()
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[]
14:08:48.964 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV3(RequestParams,HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@55dec45, org.apache.catalina.connector.RequestFacade@55b61567]
14:08:49.000 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.GridController.currentUserCompanyTownDictTree(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
14:08:49.003 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV2(RequestParams,HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@199a9601, org.apache.catalina.connector.RequestFacade@72f847dc]
14:08:49.093 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteAreaList(RouteForm)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=null, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCo...
14:08:49.095 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteList(RouteForm)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCountS...
14:08:49.415 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
14:08:49.529 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
14:08:49.955 [http-nio-35693-exec-9] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.maintUserManger.controller.MaintUserAttendController.checkUserIsAttend(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
14:08:49.955 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserDistrict(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
14:08:56.059 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"],"x":["113.70930162691808"],"y":["22.978969081481424"],"bufferDistance":["0.0017166137695286063"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
14:08:58.094 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"],"x":["113.71502367194337"],"y":["22.991387143441887"],"bufferDistance":["0.00042915344239471"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
14:09:00.482 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"],"x":["113.71047342778618"],"y":["22.991067561119028"],"bufferDistance":["0.00010728836061024446"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
14:09:05.160 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"],"x":["113.71107359586794"],"y":["22.99196353283069"],"bufferDistance":["0.000026822090152891603"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
14:09:22.348 [MessageBroker-5] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 25], outboundChannel[pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 25], sockJsScheduler[pool size = 9, active threads = 1, queued tasks = 0, completed tasks = 8]
14:14:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
14:20:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
14:26:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
14:31:45.307 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:Object com.cesc.ewater.biz.layer.controller.OneMapController.findClockByFacilityCuuid(ClockSearchVo)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"],"r":["0.3821929302439927"]}
请求体:[ClockSearchVo(deviceCuuid=c4ebeda3-efcb-4e8b-96bd-498ffd9e51cf, devicePrimaryId=, clockType=, clockTimeStart=null, clockTimeEnd=null, orderOperateType=)]
14:31:45.366 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.sys.controller.UploadFileController.downloadFileById(HttpServletRequest,HttpServletResponse)
请求参数:{"id":["undefined"],"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567, org.apache.catalina.connector.ResponseFacade@3f16fd35]
14:31:46.361 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:Object com.cesc.ewater.biz.layer.controller.OneMapController.findClearByFacilityCuuid(ClearSearchVo)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"],"r":["0.07708986721029021"]}
请求体:[ClearSearchVo(facilityCuuid=c4ebeda3-efcb-4e8b-96bd-498ffd9e51cf, workOrderNo=null, reportDate=[], handleDate=[], taskClassify=)]
14:31:46.392 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.sys.controller.UploadFileController.downloadFileById(HttpServletRequest,HttpServletResponse)
请求参数:{"id":["undefined"],"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567, org.apache.catalina.connector.ResponseFacade@3f16fd35]
14:31:48.609 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:Object com.cesc.ewater.biz.layer.controller.OneMapController.findEngmaiByFacilityCuuid(EngmaiSearchVo)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"],"r":["0.8885218807311586"]}
请求体:[EngmaiSearchVo(facilityCuuid=c4ebeda3-efcb-4e8b-96bd-498ffd9e51cf, workOrderNo=null, reportDate=[], handleDate=[], taskType=null, engMaiType=null)]
14:31:48.629 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.sys.controller.UploadFileController.downloadFileById(HttpServletRequest,HttpServletResponse)
请求参数:{"id":["undefined"],"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567, org.apache.catalina.connector.ResponseFacade@3f16fd35]
14:31:49.622 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:Object com.cesc.ewater.biz.layer.controller.RiskLayerController.queryRiskListByObjectId(Long,RiskPointSearchVo)
请求参数:{"objectId":["1357768"],"token":["86627228416148bda1b29b4947a6c68d"],"r":["0.24247515222491522"]}
请求体:[1357768, RiskPointSearchVo(flawName=null, yhdType=null, flawLevel=null, handleState=null, intoDate=null, operater=null, fixTime=null)]
14:31:49.662 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.sys.controller.UploadFileController.downloadFileById(HttpServletRequest,HttpServletResponse)
请求参数:{"id":["undefined"],"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567, org.apache.catalina.connector.ResponseFacade@3f16fd35]
14:31:50.324 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:Object com.cesc.ewater.biz.layer.controller.OneMapController.findProblemByCuuid(ProblemSearchVo)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"],"r":["0.3812841968358063"]}
请求体:[ProblemSearchVo(devicePrimaryId=null, cuuid=c4ebeda3-efcb-4e8b-96bd-498ffd9e51cf, reportTime=[], handleDate=[], isClosed=)]
14:31:50.347 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.sys.controller.UploadFileController.downloadFileById(HttpServletRequest,HttpServletResponse)
请求参数:{"id":["undefined"],"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567, org.apache.catalina.connector.ResponseFacade@3f16fd35]
14:31:52.354 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.getWorkOrderDetail(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"],"id":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
14:32:02.783 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.GridController.currentUserCompanyTownDictTree(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
14:32:02.784 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV3(RequestParams,HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@2877e4c9, org.apache.catalina.connector.RequestFacade@55b61567]
14:32:02.785 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
14:32:02.785 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV2(RequestParams,HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@2fd2c38e, org.apache.catalina.connector.RequestFacade@67d86ae1]
14:32:02.837 [http-nio-35693-exec-9] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteAreaList(RouteForm)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=null, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCo...
14:32:02.838 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteList(RouteForm)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCountS...
14:32:04.886 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.getWorkOrderDetail(HttpServletRequest)
请求参数:{"token":["86627228416148bda1b29b4947a6c68d"],"id":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
14:32:24.336 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
14:38:24.334 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
14:39:22.348 [MessageBroker-3] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 28], outboundChannel[pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 28], sockJsScheduler[pool size = 10, active threads = 1, queued tasks = 0, completed tasks = 9]
14:44:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
14:50:24.334 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
14:51:00.784 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countHomePage(HomePageDto)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"],"upcomingTask":["0"],"pipelineData":["0"],"workOrderData":["0"],"safePro":["0"]}
请求体:[HomePageDto(upcomingTask=0, pipelineData=0, workOrderData=0, safePro=0)]
14:51:00.801 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectPlanController.getProjectPlanDealWithCount(StringValForm,HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[StringValForm(val=Y), org.apache.catalina.connector.RequestFacade@72f847dc]
14:51:00.803 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
14:51:00.804 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countToBeHandling(Dto)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[Dto(token=d7a891ad284b4b1c9808d1f50f0cd594)]
14:51:01.448 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
14:51:11.743 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
14:51:11.760 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectController.getUserGridStreetDictTree()
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[]
14:51:11.761 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV3(RequestParams,HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@5dcb6c72, org.apache.catalina.connector.RequestFacade@55b61567]
14:51:11.762 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV2(RequestParams,HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@ce456f4, org.apache.catalina.connector.RequestFacade@5bbd7052]
14:51:11.762 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.GridController.currentUserCompanyTownDictTree(HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
14:51:11.914 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteAreaList(RouteForm)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=null, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCo...
14:51:11.917 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteList(RouteForm)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCountS...
14:51:12.218 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
14:51:12.295 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
14:51:12.789 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.maintUserManger.controller.MaintUserAttendController.checkUserIsAttend(HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
14:51:12.791 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserDistrict(HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
14:56:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
15:00:00.000 [pool-5-thread-1] INFO  c.c.e.b.p.e.s.ExternalPushServiceImpl - ----date:2025-08-11 15:00:00,start check external push----
15:00:00.879 [pool-5-thread-1] INFO  c.c.e.b.p.e.s.ExternalPushServiceImpl - ----date:2025-08-11 15:00:00,end check external push----
15:00:16.067 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:Object com.cesc.ewater.biz.layer.controller.OneMapController.findProblemByCuuid(ProblemSearchVo)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"],"r":["0.7160420341258577"]}
请求体:[ProblemSearchVo(devicePrimaryId=null, cuuid=c4ebeda3-efcb-4e8b-96bd-498ffd9e51cf, reportTime=[], handleDate=[], isClosed=)]
15:00:16.090 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.sys.controller.UploadFileController.downloadFileById(HttpServletRequest,HttpServletResponse)
请求参数:{"id":["undefined"],"token":["d7a891ad284b4b1c9808d1f50f0cd594"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567, org.apache.catalina.connector.ResponseFacade@3f16fd35]
15:00:17.263 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.getWorkOrderDetail(HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"],"id":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:00:17.735 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.checkAllAuthority1(HttpServletRequest)
请求参数:{"token":["d7a891ad284b4b1c9808d1f50f0cd594"],"ids":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:02:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
15:04:36.978 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectPlanController.getProjectPlanDealWithCount(StringValForm,HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[StringValForm(val=Y), org.apache.catalina.connector.RequestFacade@55b61567]
15:04:36.980 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countHomePage(HomePageDto)
请求参数:{"token":["16798dab18e247058a3c28771b829178"],"upcomingTask":["0"],"pipelineData":["0"],"workOrderData":["0"],"safePro":["0"]}
请求体:[HomePageDto(upcomingTask=0, pipelineData=0, workOrderData=0, safePro=0)]
15:04:36.982 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
15:04:36.986 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countToBeHandling(Dto)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[Dto(token=16798dab18e247058a3c28771b829178)]
15:04:37.486 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
15:04:40.489 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:04:40.503 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectController.getUserGridStreetDictTree()
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[]
15:04:40.510 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.GridController.currentUserCompanyTownDictTree(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
15:04:40.510 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV2(RequestParams,HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@2134077, org.apache.catalina.connector.RequestFacade@5bbd7052]
15:04:40.511 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV3(RequestParams,HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@6007d8a7, org.apache.catalina.connector.RequestFacade@67d86ae1]
15:04:40.664 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteAreaList(RouteForm)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=null, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCo...
15:04:40.668 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteList(RouteForm)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCountS...
15:04:40.971 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:04:41.046 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:04:41.460 [http-nio-35693-exec-9] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.maintUserManger.controller.MaintUserAttendController.checkUserIsAttend(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:04:41.461 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserDistrict(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:04:47.323 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"],"x":["113.69931850520909"],"y":["22.947558706104413"],"bufferDistance":["0.0017166137695286063"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:04:50.152 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"],"x":["113.69651835495856"],"y":["22.96618579642578"],"bufferDistance":["0.0008583068847880979"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:05:07.084 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"],"x":["113.71163003301689"],"y":["22.991143657013453"],"bufferDistance":["0.00021457672117289908"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:05:07.464 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"],"x":["113.71163003301692"],"y":["22.991143657013453"],"bufferDistance":["0.00010728836061024446"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:05:08.529 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"],"x":["113.71133327795603"],"y":["22.991193116135697"],"bufferDistance":["0.00010728836061024446"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:05:08.893 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"],"x":["113.71084629535714"],"y":["22.99234589532252"],"bufferDistance":["0.00010728836061024446"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:05:11.775 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"],"x":["113.71113353896331"],"y":["22.99189125139931"],"bufferDistance":["0.000026822090152891603"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:05:22.976 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:Object com.cesc.ewater.biz.layer.controller.OneMapController.findProblemByCuuid(ProblemSearchVo)
请求参数:{"token":["16798dab18e247058a3c28771b829178"],"r":["0.14307058257331629"]}
请求体:[ProblemSearchVo(devicePrimaryId=null, cuuid=c4ebeda3-efcb-4e8b-96bd-498ffd9e51cf, reportTime=[], handleDate=[], isClosed=)]
15:05:22.997 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.sys.controller.UploadFileController.downloadFileById(HttpServletRequest,HttpServletResponse)
请求参数:{"id":["undefined"],"token":["16798dab18e247058a3c28771b829178"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567, org.apache.catalina.connector.ResponseFacade@3f16fd35]
15:05:24.771 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.getWorkOrderDetail(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"],"id":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:05:25.226 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.checkAllAuthority1(HttpServletRequest)
请求参数:{"token":["16798dab18e247058a3c28771b829178"],"ids":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:08:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
15:09:22.349 [MessageBroker-6] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 31], outboundChannel[pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 31], sockJsScheduler[pool size = 11, active threads = 1, queued tasks = 0, completed tasks = 10]
15:11:21.582 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countHomePage(HomePageDto)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"upcomingTask":["0"],"pipelineData":["0"],"workOrderData":["0"],"safePro":["0"]}
请求体:[HomePageDto(upcomingTask=0, pipelineData=0, workOrderData=0, safePro=0)]
15:11:21.585 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectPlanController.getProjectPlanDealWithCount(StringValForm,HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[StringValForm(val=Y), org.apache.catalina.connector.RequestFacade@55b61567]
15:11:21.587 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countToBeHandling(Dto)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[Dto(token=597db4ec809c4b3ba751904dfe563308)]
15:11:21.589 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:11:22.058 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:14:02.122 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:14:02.131 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectController.getUserGridStreetDictTree()
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[]
15:14:02.132 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV3(RequestParams,HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@27f7f0b5, org.apache.catalina.connector.RequestFacade@5bbd7052]
15:14:02.135 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.GridController.currentUserCompanyTownDictTree(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
15:14:02.136 [http-nio-35693-exec-9] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV2(RequestParams,HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@486169c8, org.apache.catalina.connector.RequestFacade@72f847dc]
15:14:02.266 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteAreaList(RouteForm)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=null, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCo...
15:14:02.294 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteList(RouteForm)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCountS...
15:14:02.635 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:14:02.688 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
15:14:03.076 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.maintUserManger.controller.MaintUserAttendController.checkUserIsAttend(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
15:14:03.078 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserDistrict(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
15:14:09.561 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"x":["113.71989351839552"],"y":["22.982256207209662"],"bufferDistance":["0.0017166137695286063"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:14:21.889 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"x":["113.71125528843942"],"y":["22.991373813678738"],"bufferDistance":["0.000026822090152891603"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:14:23.811 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"x":["113.71111166655447"],"y":["22.991992053484406"],"bufferDistance":["0.000026822090152891603"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:14:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
15:15:37.380 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:Object com.cesc.ewater.biz.layer.controller.OneMapController.findProblemByCuuid(ProblemSearchVo)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"r":["0.9684668911783172"]}
请求体:[ProblemSearchVo(devicePrimaryId=null, cuuid=c4ebeda3-efcb-4e8b-96bd-498ffd9e51cf, reportTime=[], handleDate=[], isClosed=)]
15:15:37.402 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.sys.controller.UploadFileController.downloadFileById(HttpServletRequest,HttpServletResponse)
请求参数:{"id":["undefined"],"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1, org.apache.catalina.connector.ResponseFacade@5b257d37]
15:15:39.683 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.getWorkOrderDetail(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"id":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:15:40.133 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.checkAllAuthority1(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"ids":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:20:24.334 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
15:26:14.077 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.GridController.currentUserCompanyTownDictTree(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:26:14.078 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV3(RequestParams,HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@3f09aa0, org.apache.catalina.connector.RequestFacade@72f847dc]
15:26:14.078 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV2(RequestParams,HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@15653bfc, org.apache.catalina.connector.RequestFacade@55b61567]
15:26:14.078 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
15:26:14.216 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteAreaList(RouteForm)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=null, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCo...
15:26:14.219 [http-nio-35693-exec-9] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteList(RouteForm)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCountS...
15:26:17.064 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.getWorkOrderDetail(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"id":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
15:26:17.473 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.checkAllAuthority1(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"ids":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@5bbd7052]
15:26:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
15:26:33.919 [http-nio-35693-exec-9] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.GridController.currentUserCompanyTownDictTree(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:26:33.919 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV3(RequestParams,HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@3078d814, org.apache.catalina.connector.RequestFacade@5bbd7052]
15:26:33.919 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
15:26:33.920 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV2(RequestParams,HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@45a6a59f, org.apache.catalina.connector.RequestFacade@64fa0f73]
15:26:33.977 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteAreaList(RouteForm)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=null, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCo...
15:26:33.977 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteList(RouteForm)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCountS...
15:26:35.694 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.getWorkOrderDetail(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"id":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
15:26:36.112 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.checkAllAuthority1(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"ids":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
15:28:20.473 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.GridController.currentUserCompanyTownDictTree(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@72f847dc]
15:28:20.473 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV2(RequestParams,HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@2facfa2e, org.apache.catalina.connector.RequestFacade@67d86ae1]
15:28:20.473 [http-nio-35693-exec-9] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV3(RequestParams,HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@138d0a07, org.apache.catalina.connector.RequestFacade@5bbd7052]
15:28:20.473 [http-nio-35693-exec-5] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
15:28:20.544 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteAreaList(RouteForm)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=null, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCo...
15:28:20.546 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteList(RouteForm)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCountS...
15:30:35.937 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.getWorkOrderDetail(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"id":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
15:30:36.364 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.checkAllAuthority1(HttpServletRequest)
请求参数:{"token":["597db4ec809c4b3ba751904dfe563308"],"ids":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
15:32:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
15:32:54.850 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countHomePage(HomePageDto)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"],"upcomingTask":["0"],"pipelineData":["0"],"workOrderData":["0"],"safePro":["0"]}
请求体:[HomePageDto(upcomingTask=0, pipelineData=0, workOrderData=0, safePro=0)]
15:32:54.852 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectPlanController.getProjectPlanDealWithCount(StringValForm,HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[StringValForm(val=Y), org.apache.catalina.connector.RequestFacade@5bbd7052]
15:32:54.856 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:32:54.857 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderController.countToBeHandling(Dto)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[Dto(token=ebcd5ae3d3404a28ad087e7cfe0985af)]
15:32:55.342 [http-nio-35693-exec-9] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:32:59.927 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
15:32:59.943 [http-nio-35693-exec-4] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.ProjectController.getUserGridStreetDictTree()
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[]
15:32:59.944 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.GridController.currentUserCompanyTownDictTree(HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[org.apache.catalina.connector.RequestFacade@67d86ae1]
15:32:59.945 [http-nio-35693-exec-9] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV2(RequestParams,HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@74c74570, org.apache.catalina.connector.RequestFacade@72f847dc]
15:32:59.945 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.newPumpPatrol.controller.OrgDictController.getUserIdDictV3(RequestParams,HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[com.cesc.ewater.biz.params.params.def.RequestParamsDef@304f020b, org.apache.catalina.connector.RequestFacade@55b61567]
15:33:00.043 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteAreaList(RouteForm)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=null, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCo...
15:33:00.052 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:ResponseMsg com.cesc.ewater.biz.antoco.controller.RouteController.getRouteList(RouteForm)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[RouteForm(id=null, planId=null, companyName=一公司, companyNameList=null, teamId=null, teamName=null, townName=莞城街道, townNameList=null, townNameSort=null, areaName=, areaNameSort=null, areaNameList=null, routeName=, routeNameSort=null, projectCategory=null, projectCategorySort=null, projectCategoryList=null, routeLength=null, routeLengthSort=null, importance=null, importanceSort=null, importanceList=null, monthPatrolCount=null, frequency=null, frequencySort=null, openWellCount=null, openWellCountS...
15:33:00.431 [http-nio-35693-exec-2] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getJHSysDict(HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
15:33:00.520 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserTownStreet(HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:33:00.922 [http-nio-35693-exec-8] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.maintUserManger.controller.MaintUserAttendController.checkUserIsAttend(HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[org.apache.catalina.connector.RequestFacade@64fa0f73]
15:33:00.922 [http-nio-35693-exec-6] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.problemImport.controller.WorkOrderHelperController.getUserDistrict(HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:33:10.587 [http-nio-35693-exec-1] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"],"x":["113.7107017138303"],"y":["22.988708740335234"],"bufferDistance":["0.0008583068847880979"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:33:17.606 [http-nio-35693-exec-10] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.mark.controller.Pipe2UpdateController.queryMarkInChart(HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"],"x":["113.7111420907352"],"y":["22.991906466395005"],"bufferDistance":["0.000026822090152891603"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:33:52.018 [http-nio-35693-exec-3] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:POST，请求地址:Object com.cesc.ewater.biz.layer.controller.OneMapController.findProblemByCuuid(ProblemSearchVo)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"],"r":["0.18850472061721602"]}
请求体:[ProblemSearchVo(devicePrimaryId=null, cuuid=c4ebeda3-efcb-4e8b-96bd-498ffd9e51cf, reportTime=[], handleDate=[], isClosed=)]
15:33:52.041 [http-nio-35693-exec-9] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Object com.cesc.ewater.biz.sys.controller.UploadFileController.downloadFileById(HttpServletRequest,HttpServletResponse)
请求参数:{"id":["undefined"],"token":["ebcd5ae3d3404a28ad087e7cfe0985af"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567, org.apache.catalina.connector.ResponseFacade@3f16fd35]
15:33:53.524 [http-nio-35693-exec-7] INFO  c.c.ewater.biz.aopLog.RequestAopLog - 请求信息=>IP地址:************，请求方式:GET，请求地址:Map com.cesc.ewater.biz.mark.controller.ProblemImportComtroller.getWorkOrderById(HttpServletRequest)
请求参数:{"token":["ebcd5ae3d3404a28ad087e7cfe0985af"],"id":["1114"]}
请求体:[org.apache.catalina.connector.RequestFacade@55b61567]
15:38:24.335 [pool-5-thread-1] INFO  c.c.e.b.p.service.SewUserOutTask - 当前是否开启外部数据接入: false
15:39:22.350 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 34], outboundChannel[pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 34], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 11]
