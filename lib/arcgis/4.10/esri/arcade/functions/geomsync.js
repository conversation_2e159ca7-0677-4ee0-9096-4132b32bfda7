// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define("require exports ../../kernel ../kernel ../languageUtils ../featureset/support/shared ./centroid ../../geometry/Extent ../../geometry/Geometry ../../geometry/Multipoint ../../geometry/Point ../../geometry/Polygon ../../geometry/Polyline ../../geometry/support/jsonUtils".split(" "),function(A,t,z,l,d,m,v,q,h,w,u,p,r,x){function y(d){return 0===z.version.indexOf("4.")?p.fromExtent(d):new p({spatialReference:d.spatialReference,rings:[[[d.xmin,d.ymin],[d.xmin,d.ymax],[d.xmax,d.ymax],[d.xmax,d.ymin],
[d.xmin,d.ymin]]]})}Object.defineProperty(t,"__esModule",{value:!0});var g=null;t.setGeometryEngine=function(d){g=d};t.registerFunctions=function(e,f){function n(b){d.pcCheck(b,2,2);if(!(b[0]instanceof h&&b[1]instanceof h||b[0]instanceof h&&null===b[1]||b[1]instanceof h&&null===b[0]||null===b[0]&&null===b[1]))throw Error("Illegal Argument");}e.disjoint=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);n(a);return null===a[0]||null===a[1]?!0:g.disjoint(a[0],a[1])})};e.intersects=
function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);n(a);return null===a[0]||null===a[1]?!1:g.intersects(a[0],a[1])})};e.touches=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);n(a);return null===a[0]||null===a[1]?!1:g.touches(a[0],a[1])})};e.crosses=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);n(a);return null===a[0]||null===a[1]?!1:g.crosses(a[0],a[1])})};e.within=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);
n(a);return null===a[0]||null===a[1]?!1:g.within(a[0],a[1])})};e.contains=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);n(a);return null===a[0]||null===a[1]?!1:g.contains(a[0],a[1])})};e.overlaps=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);n(a);return null===a[0]||null===a[1]?!1:g.overlaps(a[0],a[1])})};e.equals=function(b,c){return f(b,c,function(k,b,a){d.pcCheck(a,2,2);return a[0]===a[1]?!0:a[0]instanceof h&&a[1]instanceof h?g.equals(a[0],
a[1]):d.isDate(a[0])&&d.isDate(a[1])?a[0].getTime()===a[1].getTime():!1})};e.relate=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,3,3);if(a[0]instanceof h&&a[1]instanceof h)return g.relate(a[0],a[1],d.toString(a[2]));if(a[0]instanceof h&&null===a[1]||a[1]instanceof h&&null===a[0]||null===a[0]&&null===a[1])return!1;throw Error("Illegal Argument");})};e.intersection=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);n(a);return null===
a[0]||null===a[1]?null:g.intersect(a[0],a[1])})};e.union=function(b,c){return f(b,c,function(k,c,a){a=d.autoCastFeatureToGeometry(a);k=[];if(0===a.length)throw Error("Function called with wrong number of Parameters");if(1===a.length)if(d.isArray(a[0]))for(a=d.autoCastFeatureToGeometry(a[0]),c=0;c<a.length;c++){if(null!==a[c])if(a[c]instanceof h)k.push(a[c]);else throw Error("Illegal Argument");}else if(d.isImmutableArray(a[0]))for(a=d.autoCastFeatureToGeometry(a[0].toArray()),c=0;c<a.length;c++){if(null!==
a[c])if(a[c]instanceof h)k.push(a[c]);else throw Error("Illegal Argument");}else{if(a[0]instanceof h)return d.fixSpatialReference(m.cloneGeometry(a[0]),b.spatialReference);if(null===a[0])return null;throw Error("Illegal Argument");}else for(c=0;c<a.length;c++)if(null!==a[c])if(a[c]instanceof h)k.push(a[c]);else throw Error("Illegal Argument");return 0===k.length?null:g.union(k)})};e.difference=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);n(a);return null!==a[0]&&null===
a[1]?m.cloneGeometry(a[0]):null===a[0]?null:g.difference(a[0],a[1])})};e.symmetricdifference=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);n(a);return null===a[0]&&null===a[1]?null:null===a[0]?m.cloneGeometry(a[1]):null===a[1]?m.cloneGeometry(a[0]):g.symmetricDifference(a[0],a[1])})};e.clip=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,2,2);if(!(a[1]instanceof q)&&null!==a[1])throw Error("Illegal Argument");if(null===a[0])return null;
if(!(a[0]instanceof h))throw Error("Illegal Argument");return null===a[1]?null:g.clip(a[0],a[1])})};e.cut=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,2,2);if(!(a[1]instanceof r)&&null!==a[1])throw Error("Illegal Argument");if(null===a[0])return[];if(!(a[0]instanceof h))throw Error("Illegal Argument");return null===a[1]?[m.cloneGeometry(a[0])]:g.cut(a[0],a[1])})};e.area=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,
1,2);if(null===a[0])return 0;if(!(a[0]instanceof h))throw Error("Illegal Argument");return g.planarArea(a[0],l.convertSquareUnitsToCode(d.defaultUndefined(a[1],-1)))})};e.areageodetic=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,1,2);if(null===a[0])return 0;if(!(a[0]instanceof h))throw Error("Illegal Argument");return g.geodesicArea(a[0],l.convertSquareUnitsToCode(d.defaultUndefined(a[1],-1)))})};e.length=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);
d.pcCheck(a,1,2);if(null===a[0])return 0;if(!(a[0]instanceof h))throw Error("Illegal Argument");return g.planarLength(a[0],l.convertLinearUnitsToCode(d.defaultUndefined(a[1],-1)))})};e.lengthgeodetic=function(b,c){return f(b,c,function(k,b,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,1,2);if(null===a[0])return 0;if(!(a[0]instanceof h))throw Error("Illegal Argument");return g.geodesicLength(a[0],l.convertLinearUnitsToCode(d.defaultUndefined(a[1],-1)))})};e.distance=function(b,c){return f(b,c,function(b,
c,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,2,3);if(!(a[0]instanceof h))throw Error("Illegal Argument");if(!(a[1]instanceof h))throw Error("Illegal Argument");return g.distance(a[0],a[1],l.convertLinearUnitsToCode(d.defaultUndefined(a[2],-1)))})};e.densify=function(b,c){return f(b,c,function(b,c,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,2,3);if(null===a[0])return null;if(!(a[0]instanceof h))throw Error("Illegal Argument");b=d.toNumber(a[1]);if(isNaN(b))throw Error("Illegal Argument");
if(0>=b)throw Error("Illegal Argument");return a[0]instanceof p||a[0]instanceof r?g.densify(a[0],b,l.convertLinearUnitsToCode(d.defaultUndefined(a[2],-1))):a[0]instanceof q?g.densify(y(a[0]),b,l.convertLinearUnitsToCode(d.defaultUndefined(a[2],-1))):a[0]})};e.densifygeodetic=function(b,c){return f(b,c,function(b,c,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,2,3);if(null===a[0])return null;if(!(a[0]instanceof h))throw Error("Illegal Argument");b=d.toNumber(a[1]);if(isNaN(b))throw Error("Illegal Argument");
if(0>=b)throw Error("Illegal Argument");return a[0]instanceof p||a[0]instanceof r?g.geodesicDensify(a[0],b,l.convertLinearUnitsToCode(d.defaultUndefined(a[2],-1))):a[0]instanceof q?g.geodesicDensify(y(a[0]),b,l.convertLinearUnitsToCode(d.defaultUndefined(a[2],-1))):a[0]})};e.generalize=function(b,c){return f(b,c,function(b,c,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,2,4);if(null===a[0])return null;if(!(a[0]instanceof h))throw Error("Illegal Argument");b=d.toNumber(a[1]);if(isNaN(b))throw Error("Illegal Argument");
return g.generalize(a[0],b,d.toBoolean(d.defaultUndefined(a[2],!0)),l.convertLinearUnitsToCode(d.defaultUndefined(a[3],-1)))})};e.buffer=function(b,c){return f(b,c,function(b,c,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,2,3);if(null===a[0])return null;if(!(a[0]instanceof h))throw Error("Illegal Argument");b=d.toNumber(a[1]);if(isNaN(b))throw Error("Illegal Argument");return 0===b?m.cloneGeometry(a[0]):g.buffer(a[0],b,l.convertLinearUnitsToCode(d.defaultUndefined(a[2],-1)))})};e.buffergeodetic=
function(b,c){return f(b,c,function(b,c,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,2,3);if(null===a[0])return null;if(!(a[0]instanceof h))throw Error("Illegal Argument");b=d.toNumber(a[1]);if(isNaN(b))throw Error("Illegal Argument");return 0===b?m.cloneGeometry(a[0]):g.geodesicBuffer(a[0],b,l.convertLinearUnitsToCode(d.defaultUndefined(a[2],-1)))})};e.offset=function(b,c){return f(b,c,function(b,c,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,2,6);if(null===a[0])return null;if(!(a[0]instanceof
p||a[0]instanceof r))throw Error("Illegal Argument");b=d.toNumber(a[1]);if(isNaN(b))throw Error("Illegal Argument");c=d.toNumber(d.defaultUndefined(a[4],10));if(isNaN(c))throw Error("Illegal Argument");var k=d.toNumber(d.defaultUndefined(a[5],0));if(isNaN(k))throw Error("Illegal Argument");return g.offset(a[0],b,l.convertLinearUnitsToCode(d.defaultUndefined(a[2],-1)),d.toString(d.defaultUndefined(a[3],"round")).toLowerCase(),c,k)})};e.rotate=function(b,c){return f(b,c,function(b,c,a){a=d.autoCastFeatureToGeometry(a);
d.pcCheck(a,2,3);b=a[0];if(null===b)return null;if(!(b instanceof h))throw Error("Illegal Argument");b instanceof q&&(b=p.fromExtent(b));c=d.toNumber(a[1]);if(isNaN(c))throw Error("Illegal Argument");a=d.defaultUndefined(a[2],null);if(null===a)return g.rotate(b,c);if(a instanceof u)return g.rotate(b,c,a);throw Error("Illegal Argument");})};e.centroid=function(b,c){return f(b,c,function(c,g,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,1,1);if(null===a[0])return null;if(!(a[0]instanceof h))throw Error("Illegal Argument");
return a[0]instanceof u?d.fixSpatialReference(m.cloneGeometry(a[0]),b.spatialReference):a[0]instanceof p?a[0].centroid:a[0]instanceof r?v.centroidPolyline(a[0]):a[0]instanceof w?v.centroidMultiPoint(a[0]):a[0]instanceof q?a[0].center:null})};e.multiparttosinglepart=function(b,c){return f(b,c,function(c,e,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,1,1);e=[];if(null===a[0])return null;if(!(a[0]instanceof h))throw Error("Illegal Argument");if(a[0]instanceof u||a[0]instanceof q)return[d.fixSpatialReference(m.cloneGeometry(a[0]),
b.spatialReference)];c=g.simplify(a[0]);if(c instanceof p){e=[];var f=[];for(a=0;a<c.rings.length;a++)if(c.isClockwise(c.rings[a])){var k=x.fromJSON({rings:[c.rings[a]],hasZ:!0===c.hasZ,hasM:!0===c.hasM,spatialReference:c.spatialReference.toJSON()});e.push(k)}else f.push({ring:c.rings[a],pt:c.getPoint(a,0)});for(c=0;c<f.length;c++)for(a=0;a<e.length;a++)if(e[a].contains(f[c].pt)){e[a].addRing(f[c].ring);break}return e}if(c instanceof r){e=[];for(a=0;a<c.paths.length;a++)f=x.fromJSON({paths:[c.paths[a]],
hasZ:!0===c.hasZ,hasM:!0===c.hasM,spatialReference:c.spatialReference.toJSON()}),e.push(f);return e}if(a[0]instanceof w){c=d.fixSpatialReference(m.cloneGeometry(a[0]),b.spatialReference);for(a=0;a<c.points.length;a++)e.push(c.getPoint(a));return e}return null})};e.issimple=function(b,c){return f(b,c,function(b,c,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,1,1);if(null===a[0])return!0;if(a[0]instanceof h)return g.isSimple(a[0]);throw Error("Illegal Argument");})};e.simplify=function(b,c){return f(b,
c,function(b,c,a){a=d.autoCastFeatureToGeometry(a);d.pcCheck(a,1,1);if(null===a[0])return null;if(a[0]instanceof h)return g.simplify(a[0]);throw Error("Illegal Argument");})}}});