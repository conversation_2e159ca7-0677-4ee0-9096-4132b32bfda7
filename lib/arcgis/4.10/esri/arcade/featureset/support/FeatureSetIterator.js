// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define(["require","exports","dojo/Deferred","../support/shared"],function(g,h,f,d){return function(){function e(a,c){this._lastId=-1;this._progress=c;this._parent=a}e.prototype.reset=function(){this._lastId=-1};e.prototype.next=function(){var a=this,c=new f;if(null!==this._parent._mainSetInUse)return this._parent._mainSetInUse.then(d.callback(function(b){a.next().then(d.callback(function(a){c.resolve(a)},c),d.errback(c))},c),function(b){a.next().then(d.callback(function(a){c.resolve(a)},c),d.errback(c))}),
c.promise;this._parent._mainSetInUse=c.promise;this._parent._getSet(this._progress).then(function(b){a._lastId<b._known.length-1?"GETPAGES"===b._known[a._lastId+1]?a._parent._expandPagedSet(b,a._parent._maxQueryRate(),0,0,a._progress).then(d.callback(function(b){a._parent._mainSetInUse=null;a.next().then(d.callback(function(a){c.resolve(a)},c),d.errback(c))},c),d.errback(c)):(a._lastId+=1,a._parent._getFeature(b,b._known[a._lastId],a._progress).then(d.callback(function(b){a._parent._mainSetInUse=
null;c.resolve(b)},c),function(b){a._parent._mainSetInUse=null;c.reject(b)})):0<b._candidates.length?a._parent._refineSetBlock(b,a._parent._maxProcessingRate(),a._progress).then(d.callback(function(){a._parent._mainSetInUse=null;a.next().then(d.callback(function(a){c.resolve(a)},c),d.errback(c))},c),function(b){a._parent._mainSetInUse=null;c.reject(b)}):(a._parent._mainSetInUse=null,c.resolve(null))},function(b){a._parent._mainSetInUse=null;c.reject(b)});return c.promise};e.prototype.count=function(){var a=
this,c=new f;-1!==this._parent._totalCount?c.resolve(this._parent._totalCount):this._parent._getSet(this._progress).then(d.callback(function(b){a._refineAllSets(b).then(d.callback(function(b){a._parent._totalCount=b._known.length;c.resolve(a._parent._totalCount)},c),d.errback(c))},c),d.errback(c));return c.promise};e.prototype._refineAllSets=function(a){var c=this,b=new f;if(0<a._known.length&&"GETPAGES"===a._known[a._known.length-1])return this._parent._expandPagedSet(a,this._parent._maxQueryRate(),
0,1,this._progress).then(d.callback(function(e){c._refineAllSets(a).then(d.callback(function(a){b.resolve(a)},b),d.errback(b))},b),d.errback(b)),b.promise;0<a._candidates.length?"GETPAGES"===a._known[a._candidates.length-1]?this._parent._expandPagedSet(a,this._parent._maxQueryRate(),0,2,this._progress).then(d.callback(function(e){c._refineAllSets(a).then(d.callback(function(a){b.resolve(a)},b),d.errback(b))},b),d.errback(b)):this._parent._refineSetBlock(a,this._parent._maxProcessingRate(),this._progress).then(d.callback(function(a){0<
a._candidates.length?c._refineAllSets(a).then(d.callback(function(a){b.resolve(a)},b),d.errback(b)):b.resolve(a)},b),d.errback(b)):b.resolve(a);return b.promise};return e}()});