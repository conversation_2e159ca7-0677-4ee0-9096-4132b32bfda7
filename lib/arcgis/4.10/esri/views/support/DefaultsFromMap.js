// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define("require exports ../../core/tsSupport/declareExtendsHelper ../../core/tsSupport/decorateHelper ../../core/Accessor ../../core/arrayUtils ../../core/Handles ../../core/Logger ../../core/watchUtils ../../core/accessorSupport/decorators ../../geometry/support/heightModelInfoUtils ../../geometry/support/webMercatorUtils ../../portal/support/geometryServiceUtils".split(" "),function(A,B,t,g,u,h,v,w,x,e,n,p,y){function k(e){return e?JSON.stringify(e.toJSON()):"undefined"}function q(e){switch(e){case 0:return"Waiting";
case 1:return"Found";case 2:return"Exhausted"}return"Unknown: "+e}var z=w.getLogger("esri.views.support.DefaultsFromMap");return function(l){function b(){var a=null!==l&&l.apply(this,arguments)||this;a._handles=new v;a._waitTask=null;a._isStarted=!1;a._spatialReferenceCandidates=null;a._extentCandidates=null;a.logDebugInformation=!1;a.isSpatialReferenceDone=!1;a.isTileInfoDone=!1;a.isHeightModelInfoSearching=!1;a.spatialReference=null;a.extent=null;a.heightModelInfo=null;a.vcsWkid=null;a.latestVcsWkid=
null;a.mapCollectionPaths=m.DefaultMapCollectionPaths.slice();a.tileInfo=null;return a}t(b,l);m=b;b.prototype.initialize=function(){var a=this;this.watch("mapCollectionPaths",function(){a._isStarted&&(a.reset(),a.start())})};b.prototype.destroy=function(){this._set("view",null);this._handles&&(this._handles.destroy(),this._handles=null,this._isStarted=!1);this._cancelLoading()};b.prototype.reset=function(){this._handles.removeAll();this._isStarted=!1;this._set("isSpatialReferenceDone",!1);this._set("isTileInfoDone",
!1);this._set("isHeightModelInfoSearching",!1);this._set("spatialReference",null);this._set("extent",null);this._set("heightModelInfo",null);this._set("vcsWkid",null);this._set("latestVcsWkid",null);this._set("tileInfo",null);this._extentCandidates=this._spatialReferenceCandidates=null};b.prototype.start=function(){this._handles.removeAll();this._isStarted=!0;for(var a=this._updateLayerChange.bind(this),c=0,d=this.mapCollectionPaths;c<d.length;c++)this._handles.add(x.on(this.view,"map."+d[c],"change",
a,a,a,!0))};b.prototype._ownerNameFromCollectionName=function(a){var c=a.lastIndexOf(".");return-1===c?"view":"view."+a.slice(0,c)};b.prototype._ensureLoadedOwnersFromCollectionName=function(a){for(var c=this._ownerNameFromCollectionName(a).split("."),d,f=0;f<c.length;f++){d=this.get(c.slice(0,f+1).join("."));if(!d)break;if(d.load&&!d.isFulfilled())return{collectionName:a,owner:null,loading:d.load()}}return{collectionName:a,owner:d}};b.prototype._cancelLoading=function(){this._waitTask=null;this._extentProjectTask&&
(this._extentProjectTask.cancel(),this._extentProjectTask=null)};b.prototype._updateWhen=function(a){var c=this,d=!0,f=!1,r=a.catch(function(a){}).then(function(){d?f=!0:r===c._waitTask&&c._update()}),d=!1;f||(this._waitTask=r);return f};b.prototype._updateLayerChange=function(){this.isSpatialReferenceDone&&!this.spatialReference&&this._set("isSpatialReferenceDone",!1);this._update()};b.prototype._update=function(){var a=this;this._cancelLoading();if(this.view){if(!this.isSpatialReferenceDone){this._debugLog("Starting search for spatial reference...");
var c=this._processMapCollections(function(c){return a._processSpatialReferenceSource(c)});this._debugLog("Search ended with status '"+q(c)+"'");if(0!==c){var d=null,c=this._spatialReferenceCandidates;!c||1>c.length?(d=this.defaultSpatialReference,this._debugLog("No spatial reference found, locking to default ("+k(d)+")")):(this.defaultSpatialReference&&1<c.length&&-1<h.findIndex(c,function(c){return c.equals(a.defaultSpatialReference)})&&(c=[this.defaultSpatialReference]),d=c[0],this._debugLog("Locking to "+
k(d)));this._set("spatialReference",d);this._set("isSpatialReferenceDone",!0);d&&(c=this.logDebugInformation,this.logDebugInformation=!1,this._processMapCollections(function(c){return a._findExtent(c,d)}),this.extent||this._projectExtentCandidate(),this.logDebugInformation=c)}}null==this.heightModelInfo&&this.view.isHeightModelInfoRequired&&(this._debugLog("Starting search for height model info..."),c=this._processMapCollections(function(c){return a._processHeightModelInfoSource(c)},function(a){return n.mayHaveHeightModelInfo(a)}),
this._debugLog("Search ended with status "+q(c)),this._set("isHeightModelInfoSearching",0===c));null==this.tileInfo&&(c=!1,this.view.isTileInfoRequired()&&(c=this._deriveTileInfo()),c||this._set("isTileInfoDone",!0))}};b.prototype._processMapCollections=function(a,c){var d=this;this._preloadMapCollections(c);var f=2;this._forAllMapCollectionSources(function(a){if(2!==f)return!1;d._debugLog("Processing collection "+a.collectionName+"...");if(a.loading&&!d._updateWhen(a.loading))return d._debugLog("Collection "+
a.collectionName+" owner is loading -\x3e wait"),f=0,!1},function(b){if(2!==f)return!1;if(null==c||c(b)){if(b.load&&!b.isFulfilled()&&!d._updateWhen(b.load()))return d._debugLog("Source "+b.id+" is loading -\x3e wait"),f=0,!1;if((!b.load||b.isResolved())&&a(b))return f=1,!1}else return d._debugLog("Source "+b.id+" is skipped due to predicate"),!1});return f};b.prototype._preloadMapCollections=function(a){var c=this,d=10,b=this.logDebugInformation;this.logDebugInformation=!1;this._forAllMapCollectionSources(function(a){return!0},
function(f){if(0===d||null!=a&&!a(f))return!1;f.load&&!f.isFulfilled()&&(c.logDebugInformation=b,c._debugLog("Pre-loading source "+f.id),c.logDebugInformation=!1,f.load(),d--)});this.logDebugInformation=b};b.prototype._forAllMapCollectionSources=function(a,c){for(var d=0,b=this.mapCollectionPaths;d<b.length;d++){var e="map."+b[d],g=this._ensureLoadedOwnersFromCollectionName(e);!1!==a(g)&&(g=g.owner,!g||g.isRejected&&g.isRejected()?this._debugLog("Collection "+e+" owner is invalid or rejected -\x3e skip"):
(g=this.view.get(e))?this._forEachSource(g,c):this._debugLog("Collection "+e+" does not exist -\x3e skip"))}};b.prototype._forEachSource=function(a,c){var d=0;for(a=a.items;d<a.length;d++){var b=a[d];!1!==c(b)&&b.layers&&this._forEachSource(b.layers,c)}};b.prototype._processSpatialReferenceSource=function(a){var c=this._getSupportedSpatialReferences(a);if(0===c.length)return!1;this._spatialReferenceCandidates?(c=h.intersect(c,this._spatialReferenceCandidates,function(a,c){return a.equals(c)}),0<c.length?
this._spatialReferenceCandidates=c:this._debugLog("Layer "+a.id+" is ignored because its supported spatial\n          references are not compatible with the previous candidates")):this._spatialReferenceCandidates=c;return 1===this._spatialReferenceCandidates.length};b.prototype._findExtent=function(a,c){var d=a.fullExtents||(a.fullExtent?[a.fullExtent]:[]),b=h.find(d,function(a){return a.spatialReference.equals(c)});if(b)return this._set("extent",b),!0;0<this._getSupportedSpatialReferences(a).length&&
(d=d.map(function(c){return{extent:c,layer:a}}),this._extentCandidates=(this._extentCandidates||[]).concat(d));return!1};b.prototype._projectExtentCandidate=function(){var a=this;if(this._extentCandidates&&this._extentCandidates.length){var c=this.spatialReference,b=h.find(this._extentCandidates,function(a){return p.canProject(a.extent.spatialReference,c)});b?this._set("extent",p.project(b.extent,c)):(b=this._extentCandidates[0],this._extentProjectTask=y.projectGeometry(b.extent,c,b.layer.portalItem).then(function(c){a._set("extent",
c)}))}};b.prototype._getSupportedSpatialReferences=function(a){var c=this,b=a.supportedSpatialReferences||(a.spatialReference?[a.spatialReference]:[]);if(0===b.length)return this._debugLog("Layer "+a.id+" is ignored because it does not have any spatial references"),[];b=b.filter(function(b){return c.view.isSpatialReferenceSupported(b,a,function(a){return c._debugLog(a)})});0===b.length?this._debugLog("Layer "+a.id+" has spatial references but none of them are supported (or layer doesn't require locking)"):
this._debugLog("Layer "+a.id+" has spatial references. Resulting candidate set: "+b.map(k).join(", "));return b};b.prototype._processHeightModelInfoSource=function(a){var c=n.deriveHeightModelInfoFromLayer(a);return c?(this._set("heightModelInfo",c),this._set("isHeightModelInfoSearching",!1),a.spatialReference&&(this._set("vcsWkid",a.spatialReference.vcsWkid),this._set("latestVcsWkid",a.spatialReference.latestVcsWkid)),!0):!1};b.prototype._deriveTileInfo=function(){if(!this.isSpatialReferenceDone)return!0;
var a=this.get("view.map");if(!a)return!0;var c=a.basemap,b=c&&c.get("baseLayers.0"),a=a.get("layers.0"),f=!1,e=null;c&&"failed"!==c.loadStatus?c.loaded?b&&"failed"!==b.loadStatus?b.loaded?e=b.tileInfo:(this._updateWhen(b.load()),f=!0):a&&"failed"!==a.loadStatus?a.loaded?e=a.tileInfo:(this._updateWhen(a.load()),f=!0):f=!0:(this._updateWhen(c.load()),f=!0):a&&"failed"!==a.loadStatus&&(a.loaded?e=a.tileInfo:(this._updateWhen(a.load()),f=!0));e&&!e.spatialReference.equals(this.spatialReference)&&(e=
null);f||this._set("tileInfo",e);return f};b.prototype._debugLog=function(a){this.logDebugInformation&&z.info(a)};var m;b.DefaultMapCollectionPaths=["basemap.baseLayers","layers","ground.layers","basemap.referenceLayers"];g([e.property()],b.prototype,"logDebugInformation",void 0);g([e.property({readOnly:!0})],b.prototype,"isSpatialReferenceDone",void 0);g([e.property({readOnly:!0})],b.prototype,"isTileInfoDone",void 0);g([e.property({readOnly:!0})],b.prototype,"isHeightModelInfoSearching",void 0);
g([e.property({constructOnly:!0})],b.prototype,"view",void 0);g([e.property({readOnly:!0})],b.prototype,"spatialReference",void 0);g([e.property({readOnly:!0})],b.prototype,"extent",void 0);g([e.property({readOnly:!0})],b.prototype,"heightModelInfo",void 0);g([e.property({readOnly:!0})],b.prototype,"vcsWkid",void 0);g([e.property({readOnly:!0})],b.prototype,"latestVcsWkid",void 0);g([e.property()],b.prototype,"mapCollectionPaths",void 0);g([e.property()],b.prototype,"defaultSpatialReference",void 0);
g([e.property({readOnly:!0})],b.prototype,"tileInfo",void 0);return b=m=g([e.subclass("esri.views.support.DefaultsFromMap")],b)}(e.declared(u))});