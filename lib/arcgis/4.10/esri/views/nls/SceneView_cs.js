// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define({"esri/widgets/Popup/nls/Popup":{zoom:"P\u0159ibl\u00ed\u017eit na",next:"Dal\u0161\u00ed prvek",previous:"P\u0159edchoz\u00ed prvek",dock:"Ukotvit",undock:"Zru\u0161it ukotven\u00ed",pageText:"{index} z {total}",selectedFeature:"Vybran\u00fd prvek",selectedFeatures:"{total} v\u00fdsledk\u016f",_localized:{}},"esri/widgets/Feature/nls/Feature":{attach:"P\u0159\u00edlohy",fields:"Pole",fieldsSummary:"Seznam atribut\u016f a hodnot",media:"M\u00e9dia",next:"Dal\u0161\u00ed",numCharts:"Po\u010det graf\u016f",
numImages:"Po\u010det obr\u00e1zk\u016f",noTitle:"Bez n\u00e1zvu",previous:"P\u0159edchoz\u00ed",lastEdited:"Datum posledn\u00ed \u00fapravy {date}.",lastCreated:"Datum vytvo\u0159en\u00ed {date}.",lastEditedByUser:"Posledn\u00ed \u00fapravu provedl(a) {user} dne {date}.",lastCreatedByUser:"Vytvo\u0159il(a) {user} dne {date}.",_localized:{}},"esri/widgets/support/nls/uriUtils":{openInApp:"Otev\u0159\u00edt v {appName}",view:"Zobrazit",_localized:{}},"esri/widgets/Attribution/nls/Attribution":{widgetLabel:"Ozna\u010den\u00ed",
_localized:{}},"esri/widgets/Compass/nls/Compass":{widgetLabel:"Kompas",reset:"Obnovit orientaci kompasu",_localized:{}},"esri/widgets/NavigationToggle/nls/NavigationToggle":{widgetLabel:"P\u0159ep\u00edn\u00e1n\u00ed navigace",toggle:"P\u0159epnout na posun nebo ot\u00e1\u010den\u00ed ve 3D",_localized:{}},"esri/widgets/Zoom/nls/Zoom":{widgetLabel:"Zv\u011bt\u0161en\u00ed",zoomIn:"P\u0159ibl\u00ed\u017eit",zoomOut:"Odd\u00e1lit",_localized:{}}});