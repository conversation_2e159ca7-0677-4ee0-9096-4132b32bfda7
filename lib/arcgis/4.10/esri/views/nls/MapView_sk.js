// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define({"esri/widgets/Popup/nls/Popup":{zoom:"Zoom to",next:"Next feature",previous:"Previous feature",dock:"Dock",undock:"Undock",pageText:"{index} of {total}",selectedFeature:"Selected feature",selectedFeatures:"{total} results",_localized:{}},"esri/widgets/Feature/nls/Feature":{attach:"Attachments",fields:"Fields",fieldsSummary:"List of attributes and values",media:"Media",next:"Next",numCharts:"Number of charts",numImages:"Number of images",noTitle:"Untitled",previous:"Previous",lastEdited:"Last edited on {date}.",
lastCreated:"Created on {date}.",lastEditedByUser:"Last edited by {user} on {date}.",lastCreatedByUser:"Created by {user} on {date}.",_localized:{}},"esri/widgets/support/nls/uriUtils":{openInApp:"Open in {appName}",view:"View",_localized:{}},"esri/widgets/Attribution/nls/Attribution":{widgetLabel:"Attribution",_localized:{}},"esri/widgets/Compass/nls/Compass":{widgetLabel:"Compass",reset:"Reset compass orientation",_localized:{}},"esri/widgets/NavigationToggle/nls/NavigationToggle":{widgetLabel:"Navigation Toggle",
toggle:"Toggle to pan or rotate in 3D",_localized:{}},"esri/widgets/Zoom/nls/Zoom":{widgetLabel:"Zoom",zoomIn:"Zoom In",zoomOut:"Zoom Out",_localized:{}}});