// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define({"esri/widgets/Popup/nls/Popup":{zoom:"\u30ba\u30fc\u30e0",next:"\u6b21\u306e\u30d5\u30a3\u30fc\u30c1\u30e3",previous:"\u524d\u306e\u30d5\u30a3\u30fc\u30c1\u30e3",dock:"\u30c9\u30c3\u30ad\u30f3\u30b0",undock:"\u30c9\u30c3\u30ad\u30f3\u30b0\u89e3\u9664",pageText:"{index} / {total}",selectedFeature:"\u9078\u629e\u30d5\u30a3\u30fc\u30c1\u30e3",selectedFeatures:"{total} \u4ef6\u306e\u7d50\u679c",_localized:{}},"esri/widgets/Feature/nls/Feature":{attach:"\u6dfb\u4ed8\u30d5\u30a1\u30a4\u30eb",fields:"\u30d5\u30a3\u30fc\u30eb\u30c9",
fieldsSummary:"\u5c5e\u6027\u304a\u3088\u3073\u5024\u306e\u30ea\u30b9\u30c8",media:"\u30e1\u30c7\u30a3\u30a2",next:"\u6b21\u3078",numCharts:"\u30c1\u30e3\u30fc\u30c8\u6570",numImages:"\u30a4\u30e1\u30fc\u30b8\u6570",noTitle:"\u7121\u984c",previous:"\u524d\u3078",lastEdited:"{date} \u306b\u6700\u5f8c\u306b\u7de8\u96c6\u3055\u308c\u307e\u3057\u305f\u3002",lastCreated:"{date} \u306b\u4f5c\u6210\u3055\u308c\u307e\u3057\u305f\u3002",lastEditedByUser:"{user} \u306b\u3088\u3063\u3066 {date} \u306b\u6700\u5f8c\u306b\u7de8\u96c6\u3055\u308c\u307e\u3057\u305f\u3002",
lastCreatedByUser:"{user} \u306b\u3088\u3063\u3066 {date} \u306b\u4f5c\u6210\u3055\u308c\u307e\u3057\u305f\u3002",_localized:{}},"esri/widgets/support/nls/uriUtils":{openInApp:"{appName} \u3067\u958b\u304f",view:"\u8868\u793a",_localized:{}},"esri/widgets/Attribution/nls/Attribution":{widgetLabel:"\u8457\u4f5c\u6a29",_localized:{}},"esri/widgets/Compass/nls/Compass":{widgetLabel:"\u30b3\u30f3\u30d1\u30b9",reset:"\u30b3\u30f3\u30d1\u30b9\u65b9\u4f4d\u306e\u30ea\u30bb\u30c3\u30c8",_localized:{}},"esri/widgets/NavigationToggle/nls/NavigationToggle":{widgetLabel:"\u30ca\u30d3\u30b2\u30fc\u30b7\u30e7\u30f3\u306e\u5207\u308a\u66ff\u3048",
toggle:"3D \u306e\u753b\u9762\u79fb\u52d5\u307e\u305f\u306f\u56de\u8ee2\u306b\u5207\u308a\u66ff\u3048",_localized:{}},"esri/widgets/Zoom/nls/Zoom":{widgetLabel:"\u30ba\u30fc\u30e0",zoomIn:"\u62e1\u5927",zoomOut:"\u7e2e\u5c0f",_localized:{}}});