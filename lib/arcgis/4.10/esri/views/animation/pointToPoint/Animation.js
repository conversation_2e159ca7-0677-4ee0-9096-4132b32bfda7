// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define("require exports ../easing ./Definition ./Settings ./apex/Path".split(" "),function(e,f,g,k,h,l){Object.defineProperty(f,"__esModule",{value:!0});var m={zoom:0,pan:0,rotate:0};e=function(){function d(a){this.createCamera=a;this.time=0;this.definition=new k.Definition(a);this.path=new l.Path}d.prototype.update=function(a,b,c){this.definition.update(a,b,c);this.path.update(this.definition,c);this.time=this._applyTimeSettings(this.path.time,c);this.settings=c};d.prototype.cameraAt=function(a,
b){b=b||this.createCamera();a=Math.min(Math.max(0,a),1);a=this.settings.easing?this.normalizedEasing(this.settings.easing,a,1E3*this.time):1<=this.time?this.normalizedEasing(g.inOutCoastQuad,a):this.normalizedEasing(g.outExpo,a);a=this.path.interpolateComponentsAt(a,m);b.interpolate(this.definition.source,this.definition.target,a);return b};d.prototype.normalizedEasing=function(a,b,c){c=a(0);var d=a(1);return(a(b)-c)/(d-c)};d.prototype._applyTimeSettings=function(a,b){var c=null!=b.speedFactor?b.speedFactor:
1;null!=b.duration?a=b.duration:null!=b.speedFactor&&(a/=c);return a=Math.min(Math.max(null!=b.minDuration?b.minDuration:h.defaultSettings.minDuration/c,a),null!=b.maxDuration?b.maxDuration:h.defaultSettings.maxDuration/c)};return d}();f.Animation=e;f.default=e});