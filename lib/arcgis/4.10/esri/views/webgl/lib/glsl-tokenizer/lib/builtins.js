// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define([],function(){return"abs acos all any asin atan ceil clamp cos cross dFdx dFdy degrees distance dot equal exp exp2 faceforward floor fract gl_BackColor gl_BackLightModelProduct gl_BackLightProduct gl_BackMaterial gl_BackSecondaryColor gl_ClipPlane gl_ClipVertex gl_Color gl_DepthRange gl_DepthRangeParameters gl_EyePlaneQ gl_EyePlaneR gl_EyePlaneS gl_EyePlaneT gl_Fog gl_FogCoord gl_FogFragCoord gl_FogParameters gl_FragColor gl_FragCoord gl_FragData gl_FragDepth gl_FragDepthEXT gl_FrontColor gl_FrontFacing gl_FrontLightModelProduct gl_FrontLightProduct gl_FrontMaterial gl_FrontSecondaryColor gl_LightModel gl_LightModelParameters gl_LightModelProducts gl_LightProducts gl_LightSource gl_LightSourceParameters gl_MaterialParameters gl_MaxClipPlanes gl_MaxCombinedTextureImageUnits gl_MaxDrawBuffers gl_MaxFragmentUniformComponents gl_MaxLights gl_MaxTextureCoords gl_MaxTextureImageUnits gl_MaxTextureUnits gl_MaxVaryingFloats gl_MaxVertexAttribs gl_MaxVertexTextureImageUnits gl_MaxVertexUniformComponents gl_ModelViewMatrix gl_ModelViewMatrixInverse gl_ModelViewMatrixInverseTranspose gl_ModelViewMatrixTranspose gl_ModelViewProjectionMatrix gl_ModelViewProjectionMatrixInverse gl_ModelViewProjectionMatrixInverseTranspose gl_ModelViewProjectionMatrixTranspose gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 gl_Normal gl_NormalMatrix gl_NormalScale gl_ObjectPlaneQ gl_ObjectPlaneR gl_ObjectPlaneS gl_ObjectPlaneT gl_Point gl_PointCoord gl_PointParameters gl_PointSize gl_Position gl_ProjectionMatrix gl_ProjectionMatrixInverse gl_ProjectionMatrixInverseTranspose gl_ProjectionMatrixTranspose gl_SecondaryColor gl_TexCoord gl_TextureEnvColor gl_TextureMatrix gl_TextureMatrixInverse gl_TextureMatrixInverseTranspose gl_TextureMatrixTranspose gl_Vertex greaterThan greaterThanEqual inversesqrt length lessThan lessThanEqual log log2 matrixCompMult max min mix mod normalize not notEqual pow radians reflect refract sign sin smoothstep sqrt step tan texture2D texture2DLod texture2DProj texture2DProjLod textureCube textureCubeLod texture2DLodEXT texture2DProjLodEXT textureCubeLodEXT texture2DGradEXT texture2DProjGradEXT textureCubeGradEXT".split(" ")});