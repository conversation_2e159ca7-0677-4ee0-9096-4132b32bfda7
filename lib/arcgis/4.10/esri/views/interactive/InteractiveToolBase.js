// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define("require exports ../../core/tsSupport/declareExtendsHelper ../../core/tsSupport/decorateHelper ../../core/Accessor ../../core/accessorSupport/decorators ./interactiveToolUtils".split(" "),function(h,k,e,c,f,b,g){return function(d){function a(a){a=d.call(this)||this;a._attached=!1;return a}e(a,d);Object.defineProperty(a.prototype,"active",{get:function(){return this.view.activeTool===this},enumerable:!0,configurable:!0});Object.defineProperty(a.prototype,"isSupported",{get:function(){return!0},
enumerable:!0,configurable:!0});Object.defineProperty(a.prototype,"visible",{set:function(a){this._attached&&(a?this.show():(this.hide(),g.setActive(this,!1)));this._set("visible",a)},enumerable:!0,configurable:!0});a.prototype.attach=function(){!this._attached&&this.isSupported&&(this.visible&&this.show(),this._attached=!0)};a.prototype.detach=function(){this._attached&&(this.reset(),this.hide(),this._attached=!1)};c([b.property({constructOnly:!0})],a.prototype,"view",void 0);c([b.property({dependsOn:["view.activeTool"],
readOnly:!0})],a.prototype,"active",null);c([b.property({value:!0})],a.prototype,"visible",null);return a=c([b.subclass("esri.views.interactive.InteractiveToolBase")],a)}(b.declared(f))});