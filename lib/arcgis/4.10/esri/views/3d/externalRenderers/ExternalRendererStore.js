// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define("require exports ../../../core/Collection ../../../core/Logger ../../../core/watchUtils ../../../core/libs/gl-matrix-2/gl-matrix ./RenderContext".split(" "),function(f,p,g,h,k,e,l){function d(c,a,b){if("function"===typeof c[a])c[a](b)}var m=h.getLogger("esri.views.3d.externalRenderers.ExternalRendererStore");f=function(){function c(){this._renderers=new g}c.prototype.add=function(a,b){this._findOrCreateStageRenderer(a).add(b)};c.prototype.remove=function(a,b){(a=this._findStageRenderer(a))&&
a.remove(b);0===a.renderers.length&&(a.destroy(),this._renderers.remove(a))};c.prototype._findStageRenderer=function(a){return this._renderers.find(function(b){return b.view===a})};c.prototype._findOrCreateStageRenderer=function(a){var b=this._findStageRenderer(a);b||(b=new n(a),this._renderers.add(b));return b};return c}();var n=function(){function c(a){var b=this;this.view=a;this.didRender=!0;this.needsRender=!1;this.renderers=new g;this._readyWatchHandle=k.init(a,"ready",function(a){a?(b.context=
new l(b.view),b.view._stage.addRenderPlugin([5,7],b)):(b.renderers.forEach(function(a){return d(a,"dispose",b.context)}),b.context=null)})}c.prototype.destroy=function(){var a=this;this.renderers.drain(function(b){a.context&&d(b,"dispose",a.context)});this.view._stage&&this.view._stage.removeRenderPlugin(this);this._readyWatchHandle&&(this._readyWatchHandle.remove(),this._readyWatchHandle=null);this.context=null};c.prototype.add=function(a){-1!==this.renderers.indexOf(a)?m.warn("add(): Cannot add external renderer: renderer has already been added"):
(this.renderers.add(a),this.context&&(this._webglStateReset(),d(a,"setup",this.context),this.view._stage.setNeedsRender()))};c.prototype.remove=function(a){var b=this.renderers.indexOf(a);-1!==b&&(this.renderers.removeAt(b),this.context&&(d(a,"dispose",this.context),this.view._stage.setNeedsRender()))};c.prototype.initializeRenderContext=function(a){var b=this;this.context.gl=a.rctx.gl;this.context.rctx=a.rctx;0<this.renderers.length&&this._webglStateReset();this.renderers.forEach(function(a){return d(a,
"setup",b.context)})};c.prototype.uninitializeRenderContext=function(a){};c.prototype.render=function(a){var b=this;if(0!==a.pass)return!0;this._updateContext(a);0<this.renderers.length&&this._webglStateReset();this.renderers.forEach(function(c){switch(a.slot){case 5:d(c,"render",b.context);break;case 7:d(c,"renderTransparent",b.context)}});return!0};c.prototype.resetNeedsRender=function(){};c.prototype._updateContext=function(a){this.context.camera.copyFrom(a.camera);e.vec3.copy(this.context.sunLight.direction,
a.lightingData.direction);e.vec3.copy(this.context.sunLight.diffuse.color,a.lightingData.diffuse);e.vec3.copy(this.context.sunLight.ambient.color,a.lightingData.ambient);this.context.sunLight.diffuse.intensity=a.lightingData.diffuse[3];this.context.sunLight.ambient.intensity=a.lightingData.ambient[3];this.context._renderTargetHelper=a.offscreenRenderingHelper};c.prototype._webglStateReset=function(){this.context.rctx.resetState();this.context._renderTargetHelper&&this.context._renderTargetHelper.bindFramebuffer()};
return c}();return f});