// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define("require exports ../../../../core/Logger ../../../../core/libs/gl-matrix-2/gl-matrix ../../support/imageUtils ./DefaultVertexAttributeLocations ./DefaultVertexBufferLayouts ./glUtil3D ./Util ../shaders/MiscPrograms ../shaders/SSAOPrograms ../../../webgl/BufferObject ../../../webgl/FramebufferObject ../../../webgl/Texture ../../../webgl/Util ../../../webgl/VertexArrayObject".split(" "),function(x,u,y,n,z,A,B,v,r,C,w,D,t,E,q,F){var G=y.getLogger("esri.views.3d.webgl-engine.lib.SSAOHelper");u=
function(){function d(a,c,b){this._enabled=!1;this._BLUR_F=2;this._attenuation=.5;this._radius=3;this._samples=16;this._viewportToRestore=n.vec4f64.create();this._rctx=c;this._programRep=a;this._requestRender=b;this._emptyTexture=v.createEmptyTexture(c)}d.prototype.dispose=function(){this._emptyTexture.dispose();this._emptyTexture=null};Object.defineProperty(d.prototype,"isSupported",{get:function(){var a=this._rctx,c=-1!==a.parameters.versionString.indexOf("WebGL 0.93"),a=-1!==a.parameters.versionString.indexOf("WebGL 0.94");
return!(c||a)},enumerable:!0,configurable:!0});Object.defineProperty(d.prototype,"enabled",{get:function(){return this._enabled},set:function(a){a?this.enable():this.disable()},enumerable:!0,configurable:!0});Object.defineProperty(d.prototype,"attenuation",{get:function(){return this._attenuation},set:function(a){this._attenuation=a},enumerable:!0,configurable:!0});Object.defineProperty(d.prototype,"radius",{get:function(){return this._radius},set:function(a){this._radius=a},enumerable:!0,configurable:!0});
Object.defineProperty(d.prototype,"filterRadius",{get:function(){return 4},enumerable:!0,configurable:!0});Object.defineProperty(d.prototype,"samples",{get:function(){return this._samples},set:function(a){this._samples=a;this._enabled&&this.selectPrograms()},enumerable:!0,configurable:!0});d.prototype.computeSSAO=function(a,c,b,d){if(this._noiseTexture){r.assert(this.enabled);var e=this._rctx,f=b.width,h=b.height,l=f/this._BLUR_F,m=h/this._BLUR_F;this._ssaoFBO.resize(f,h);this._blur0FBO.resize(l,
m);this._blur1FBO.resize(l,m);l=1*f;m=1*h;e.bindFramebuffer(this._ssaoFBO);n.vec4.copy(this._viewportToRestore,a.fullViewport);e.setViewport(0,0,f,h);var g=this._ssaoProgram,k=this._blurProgram;g.setUniform2f("rnmScale",f/this._noiseTexture.descriptor.width,h/this._noiseTexture.descriptor.height);g.setUniform3fv("pSphere",8>=this._samples?this._data.random8:16>=this._samples?this._data.random16:32>=this._samples?this._data.random32:this._data.random64);e.bindProgram(g);f=this._data.minDiscrepancy;
g.setUniform1f("numSpiralTurns",this._samples<f.length?f[this._samples]:5779);f=H;h=I;r.inverseProjectionInfo(a.projectionMatrix,a.fullWidth,a.fullHeight,f,h);g.setUniform4fv("projInfo",f);g.setUniform2fv("zScale",h);g.setUniform2f("nearFar",a.near,a.far);f=1/a.computePixelSizeAtDist(1);g.setUniform1f("projScale",1*f);g.setUniform2f("screenDimensions",l,m);var p=2*this._radius,h=n.vec3.distance(a.eye,a.center),p=20*a.computePixelSizeAtDist(h),p=Math.max(.1,p);g.setUniform1f("radius",p);g.setUniform1f("intensity",
4*this._attenuation/Math.pow(p,6));g.setUniform1i("rnm",0);g.setUniform1i("normalMap",1);g.setUniform1i("depthMap",2);e.bindTexture(this._noiseTexture,0);e.bindTexture(d.colorTexture,1);e.bindTexture(b.colorTexture,2);b=v.createQuadVAO(this._rctx);e.bindVAO(b);e.drawArrays(5,0,q.vertexCount(b,"geometry"));e.bindTexture(this._ssaoFBO.colorTexture,0);e.setViewport(0,0,l/this._BLUR_F,m/this._BLUR_F);e.bindFramebuffer(this._blur0FBO);k.setUniform2f("screenDimensions",l,m);k.setUniform1i("tex",0);k.setUniform1i("normalMap",
1);k.setUniform1i("depthMap",2);k.setUniform2f("blurSize",0,1*this._BLUR_F/m);k.setUniform1i("radius",4);k.setUniform1f("g_BlurFalloff",.08);k.setUniform2f("nearFar",a.near,a.far);5E4<h&&(f=Math.max(0,f-(h-5E4)));k.setUniform1f("projScale",f);k.setUniform2f("zScale",1,0);e.drawArrays(5,0,q.vertexCount(b,"geometry"));k.setUniform2f("blurSize",1*this._BLUR_F/l,0);e.bindFramebuffer(this._blur1FBO);e.bindTexture(this._blur0FBO.colorTexture,0);e.drawArrays(5,0,q.vertexCount(b,"geometry"));e.bindFramebuffer(c);
e.setViewport(this._viewportToRestore[0],this._viewportToRestore[1],this._viewportToRestore[2],this._viewportToRestore[3])}};d.prototype.setUniforms=function(a){var c=this.enabled&&this._noiseTexture,b=this._rctx;b.bindTexture(c?this._blur1FBO.colorTexture:this._emptyTexture,6);b.setActiveTexture(0);a.setUniform1i("ssaoTex",6);c?a.setUniform4f("viewportPixelSz",this._viewportToRestore[0],this._viewportToRestore[1],1/this._ssaoFBO.width,1/this._ssaoFBO.height):a.setUniform4f("viewportPixelSz",-1,-1,
-1,-1)};d.prototype.bindAll=function(a){a=a.getProgramsUsingUniform("viewportPixelSz");for(var c=0;c<a.length;c++)this.setUniforms(a[c])};d.prototype.drawQuad=function(a){r.assert(this.enabled);var c=this._showDepthProgram;this._debugQuadVAO||(this._debugQuadVAO=new F(this._rctx,A.Default3D,{geometry:B.Pos2Tex},{geometry:D.createVertex(this._rctx,35044,J)}));var b=this._rctx;b.setDepthTestEnabled(!1);c.setUniformMatrix4fv("proj",new Float32Array(a));c.setUniform1i("depthTex",0);b.bindTexture(this._ssaoFBO.colorTexture,
0);b.bindVAO(this._debugQuadVAO);b.drawArrays(5,0,q.vertexCount(this._debugQuadVAO,"geometry"));b.setDepthTestEnabled(!0)};d.prototype.selectPrograms=function(){this._ssaoProgram=this._programRep.getProgram(w.ssaoPass,{samples:8>=this._samples?8:16>=this._samples?16:32>=this._samples?32:64});this._blurProgram=this._programRep.getProgram(w.blurPass,{radius:4});this._showDepthProgram=this._programRep.getProgram(C.showDepth)};d.prototype.enable=function(){var a=this;this.enabled||(this.isSupported?(this._enabled=
!0,this.loadResources(function(){a._enabled&&a.initialize()})):G.warn("SSAO is not supported for this browser or hardware"))};d.prototype.loadResources=function(a){var c=this;this._data?a():x(["./SSAOHelperData"],function(b){c._data=b;a()})};d.prototype.initialize=function(){var a=this,c={target:3553,pixelFormat:6408,dataType:5121,samplingMode:9729,wrapMode:33071,width:0,height:0},b={colorTarget:0,depthStencilTarget:0};this._ssaoFBO=t.createWithAttachments(this._rctx,c,b);this._blur0FBO=t.createWithAttachments(this._rctx,
c,b);this._blur1FBO=t.createWithAttachments(this._rctx,c,b);z.requestImage(this._data.noiseTexture).then(function(b){a._enabled&&(a._noiseTexture=new E(a._rctx,{target:3553,pixelFormat:6408,dataType:5121,hasMipmap:!0,width:b.width,height:b.height},b),a._requestRender())});this.selectPrograms()};d.prototype.disable=function(){this.enabled&&(this._enabled=!1,this._quadVAO&&(this._quadVAO.dispose(!0),this._quadVAO=null),this._noiseTexture&&(this._noiseTexture.dispose(),this._noiseTexture=null),this._blur1FBO&&
(this._blur1FBO.dispose(),this._blur1FBO=null),this._blur0FBO&&(this._blur0FBO.dispose(),this._blur0FBO=null),this._ssaoFBO&&(this._ssaoFBO.dispose(),this._ssaoFBO=null))};return d}();var I=n.vec2f64.create(),H=n.vec4f64.create(),J=new Float32Array([0,0,0,0,512,0,1,0,0,512,0,1,512,512,1,1]);return u});