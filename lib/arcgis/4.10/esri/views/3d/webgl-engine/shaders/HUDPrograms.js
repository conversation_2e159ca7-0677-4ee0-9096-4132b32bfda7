// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define(["require","exports","../lib/DefaultVertexAttributeLocations","./sources/resolver","../../../webgl/programUtils"],function(g,c,e,b,f){Object.defineProperty(c,"__esModule",{value:!0});var d=function(a){return f.glslifyDefineMap({OCCL_TEST:a.occlTest,SIGNED_DISTANCE_FIELD:a.sdf,VV_SIZE:a.vvSize,VV_COLOR:a.vvColor,VERTICAL_OFFSET:a.verticalOffset,SCREEN_SIZE_PERSPECTIVE:a.screenSizePerspective,CENTER_OFFSET_UNITS_SCREEN:a.centerOffsetUnitsScreen,DEBUG_DRAW_BORDER:a.debug<PERSON><PERSON>order,BINARY_HIGHLIGHT_OCCLUSION:a.binaryHighlightOcclusion,
SLICE:a.slice})};c.colorPass={name:"hud-color",shaders:function(a){return{vertexShader:d(a)+b.resolveIncludes("materials/hud/hud.vert"),fragmentShader:d(a)+b.resolveIncludes("materials/hud/colorPass.frag")}},attributes:e.Default3D};c.highlightPass={name:"hud-highlight",shaders:function(a){return{vertexShader:d(a)+b.resolveIncludes("materials/hud/hud.vert"),fragmentShader:d(a)+b.resolveIncludes("materials/hud/highlightPass.frag")}},attributes:e.Default3D};c.occlusionPass={name:"hud-occlusion",shaders:function(a){return{vertexShader:f.glslifyDefineMap({VERTICAL_OFFSET:a.verticalOffset,
SCREEN_SIZE_PERSPECTIVE:a.screenSizePerspective,CENTER_OFFSET_UNITS_SCREEN:a.centerOffsetUnitsScreen,SLICE:a.slice})+b.resolveIncludes("materials/hud/occlusionTest.vert"),fragmentShader:b.resolveIncludes("materials/hud/occlusionTest.frag")}},attributes:e.Default3D}});