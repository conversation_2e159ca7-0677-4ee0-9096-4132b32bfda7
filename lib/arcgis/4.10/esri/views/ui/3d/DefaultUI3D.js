// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.10/esri/copyright.txt for details.
//>>built
define("require exports ../../../core/tsSupport/declareExtendsHelper ../../../core/tsSupport/decorateHelper ../../../core/accessorSupport/decorators ../DefaultUI".split(" "),function(g,h,e,c,b,f){return function(d){function a(a){a=d.call(this)||this;a.components=["attribution","zoom","navigation-toggle","compass"];return a}e(a,d);c([b.property()],a.prototype,"components",void 0);return a=c([b.subclass("esri.views.ui.3d.DefaultUI3D")],a)}(b.declared(f))});