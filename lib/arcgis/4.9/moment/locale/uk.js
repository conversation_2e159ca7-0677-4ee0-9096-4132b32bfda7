//>>built
(function(e,a){"object"===typeof exports&&"undefined"!==typeof module&&"function"===typeof require?a(require("../moment")):"function"===typeof define&&define.amd?define(["../moment"],a):a(e.moment)})(this,function(e){function a(d,b,a){if("m"===a)return b?"\u0445\u0432\u0438\u043b\u0438\u043d\u0430":"\u0445\u0432\u0438\u043b\u0438\u043d\u0443";if("h"===a)return b?"\u0433\u043e\u0434\u0438\u043d\u0430":"\u0433\u043e\u0434\u0438\u043d\u0443";var c=+d;b={ss:b?"\u0441\u0435\u043a\u0443\u043d\u0434\u0430_\u0441\u0435\u043a\u0443\u043d\u0434\u0438_\u0441\u0435\u043a\u0443\u043d\u0434":
"\u0441\u0435\u043a\u0443\u043d\u0434\u0443_\u0441\u0435\u043a\u0443\u043d\u0434\u0438_\u0441\u0435\u043a\u0443\u043d\u0434",mm:b?"\u0445\u0432\u0438\u043b\u0438\u043d\u0430_\u0445\u0432\u0438\u043b\u0438\u043d\u0438_\u0445\u0432\u0438\u043b\u0438\u043d":"\u0445\u0432\u0438\u043b\u0438\u043d\u0443_\u0445\u0432\u0438\u043b\u0438\u043d\u0438_\u0445\u0432\u0438\u043b\u0438\u043d",hh:b?"\u0433\u043e\u0434\u0438\u043d\u0430_\u0433\u043e\u0434\u0438\u043d\u0438_\u0433\u043e\u0434\u0438\u043d":"\u0433\u043e\u0434\u0438\u043d\u0443_\u0433\u043e\u0434\u0438\u043d\u0438_\u0433\u043e\u0434\u0438\u043d",
dd:"\u0434\u0435\u043d\u044c_\u0434\u043d\u0456_\u0434\u043d\u0456\u0432",MM:"\u043c\u0456\u0441\u044f\u0446\u044c_\u043c\u0456\u0441\u044f\u0446\u0456_\u043c\u0456\u0441\u044f\u0446\u0456\u0432",yy:"\u0440\u0456\u043a_\u0440\u043e\u043a\u0438_\u0440\u043e\u043a\u0456\u0432"}[a].split("_");return d+" "+(1===c%10&&11!==c%100?b[0]:2<=c%10&&4>=c%10&&(10>c%100||20<=c%100)?b[1]:b[2])}function c(d){return function(){return d+"\u043e"+(11===this.hours()?"\u0431":"")+"] LT"}}return e.defineLocale("uk",{months:{format:"\u0441\u0456\u0447\u043d\u044f \u043b\u044e\u0442\u043e\u0433\u043e \u0431\u0435\u0440\u0435\u0437\u043d\u044f \u043a\u0432\u0456\u0442\u043d\u044f \u0442\u0440\u0430\u0432\u043d\u044f \u0447\u0435\u0440\u0432\u043d\u044f \u043b\u0438\u043f\u043d\u044f \u0441\u0435\u0440\u043f\u043d\u044f \u0432\u0435\u0440\u0435\u0441\u043d\u044f \u0436\u043e\u0432\u0442\u043d\u044f \u043b\u0438\u0441\u0442\u043e\u043f\u0430\u0434\u0430 \u0433\u0440\u0443\u0434\u043d\u044f".split(" "),
standalone:"\u0441\u0456\u0447\u0435\u043d\u044c \u043b\u044e\u0442\u0438\u0439 \u0431\u0435\u0440\u0435\u0437\u0435\u043d\u044c \u043a\u0432\u0456\u0442\u0435\u043d\u044c \u0442\u0440\u0430\u0432\u0435\u043d\u044c \u0447\u0435\u0440\u0432\u0435\u043d\u044c \u043b\u0438\u043f\u0435\u043d\u044c \u0441\u0435\u0440\u043f\u0435\u043d\u044c \u0432\u0435\u0440\u0435\u0441\u0435\u043d\u044c \u0436\u043e\u0432\u0442\u0435\u043d\u044c \u043b\u0438\u0441\u0442\u043e\u043f\u0430\u0434 \u0433\u0440\u0443\u0434\u0435\u043d\u044c".split(" ")},
monthsShort:"\u0441\u0456\u0447 \u043b\u044e\u0442 \u0431\u0435\u0440 \u043a\u0432\u0456\u0442 \u0442\u0440\u0430\u0432 \u0447\u0435\u0440\u0432 \u043b\u0438\u043f \u0441\u0435\u0440\u043f \u0432\u0435\u0440 \u0436\u043e\u0432\u0442 \u043b\u0438\u0441\u0442 \u0433\u0440\u0443\u0434".split(" "),weekdays:function(d,b){var a={nominative:"\u043d\u0435\u0434\u0456\u043b\u044f \u043f\u043e\u043d\u0435\u0434\u0456\u043b\u043e\u043a \u0432\u0456\u0432\u0442\u043e\u0440\u043e\u043a \u0441\u0435\u0440\u0435\u0434\u0430 \u0447\u0435\u0442\u0432\u0435\u0440 \u043f\u2019\u044f\u0442\u043d\u0438\u0446\u044f \u0441\u0443\u0431\u043e\u0442\u0430".split(" "),
accusative:"\u043d\u0435\u0434\u0456\u043b\u044e \u043f\u043e\u043d\u0435\u0434\u0456\u043b\u043e\u043a \u0432\u0456\u0432\u0442\u043e\u0440\u043e\u043a \u0441\u0435\u0440\u0435\u0434\u0443 \u0447\u0435\u0442\u0432\u0435\u0440 \u043f\u2019\u044f\u0442\u043d\u0438\u0446\u044e \u0441\u0443\u0431\u043e\u0442\u0443".split(" "),genitive:"\u043d\u0435\u0434\u0456\u043b\u0456 \u043f\u043e\u043d\u0435\u0434\u0456\u043b\u043a\u0430 \u0432\u0456\u0432\u0442\u043e\u0440\u043a\u0430 \u0441\u0435\u0440\u0435\u0434\u0438 \u0447\u0435\u0442\u0432\u0435\u0440\u0433\u0430 \u043f\u2019\u044f\u0442\u043d\u0438\u0446\u0456 \u0441\u0443\u0431\u043e\u0442\u0438".split(" ")};
if(!d)return a.nominative;b=/(\[[\u0412\u0432\u0423\u0443]\]) ?dddd/.test(b)?"accusative":/\[?(?:\u043c\u0438\u043d\u0443\u043b\u043e\u0457|\u043d\u0430\u0441\u0442\u0443\u043f\u043d\u043e\u0457)? ?\] ?dddd/.test(b)?"genitive":"nominative";return a[b][d.day()]},weekdaysShort:"\u043d\u0434 \u043f\u043d \u0432\u0442 \u0441\u0440 \u0447\u0442 \u043f\u0442 \u0441\u0431".split(" "),weekdaysMin:"\u043d\u0434 \u043f\u043d \u0432\u0442 \u0441\u0440 \u0447\u0442 \u043f\u0442 \u0441\u0431".split(" "),longDateFormat:{LT:"HH:mm",
LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY \u0440.",LLL:"D MMMM YYYY \u0440., HH:mm",LLLL:"dddd, D MMMM YYYY \u0440., HH:mm"},calendar:{sameDay:c("[\u0421\u044c\u043e\u0433\u043e\u0434\u043d\u0456 "),nextDay:c("[\u0417\u0430\u0432\u0442\u0440\u0430 "),lastDay:c("[\u0412\u0447\u043e\u0440\u0430 "),nextWeek:c("[\u0423] dddd ["),lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return c("[\u041c\u0438\u043d\u0443\u043b\u043e\u0457] dddd [").call(this);case 1:case 2:case 4:return c("[\u041c\u0438\u043d\u0443\u043b\u043e\u0433\u043e] dddd [").call(this)}},
sameElse:"L"},relativeTime:{future:"\u0437\u0430 %s",past:"%s \u0442\u043e\u043c\u0443",s:"\u0434\u0435\u043a\u0456\u043b\u044c\u043a\u0430 \u0441\u0435\u043a\u0443\u043d\u0434",ss:a,m:a,mm:a,h:"\u0433\u043e\u0434\u0438\u043d\u0443",hh:a,d:"\u0434\u0435\u043d\u044c",dd:a,M:"\u043c\u0456\u0441\u044f\u0446\u044c",MM:a,y:"\u0440\u0456\u043a",yy:a},meridiemParse:/\u043d\u043e\u0447\u0456|\u0440\u0430\u043d\u043a\u0443|\u0434\u043d\u044f|\u0432\u0435\u0447\u043e\u0440\u0430/,isPM:function(a){return/^(\u0434\u043d\u044f|\u0432\u0435\u0447\u043e\u0440\u0430)$/.test(a)},
meridiem:function(a,b,c){return 4>a?"\u043d\u043e\u0447\u0456":12>a?"\u0440\u0430\u043d\u043a\u0443":17>a?"\u0434\u043d\u044f":"\u0432\u0435\u0447\u043e\u0440\u0430"},dayOfMonthOrdinalParse:/\d{1,2}-(\u0439|\u0433\u043e)/,ordinal:function(a,b){switch(b){case "M":case "d":case "DDD":case "w":case "W":return a+"-\u0439";case "D":return a+"-\u0433\u043e";default:return a}},week:{dow:1,doy:7}})});