.mblSpinWheel {position: relative; overflow: hidden; background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #313137), color-stop(0.05, #73747d), color-stop(0.07, #92939b), color-stop(0.09, #ababb5), color-stop(0.12, #c5c6ce), color-stop(0.16, #dfe0e4), color-stop(0.22, #f4f5f6), color-stop(0.35, #fbfcfc), color-stop(0.5, #fbfcfc), color-stop(0.61, #fbfcfc), color-stop(0.61, #b4c1c7), color-stop(0.65, #fbfcfc), color-stop(0.78, #f4f5f6), color-stop(0.84, #dfe0e4), color-stop(0.88, #c5c6ce), color-stop(0.91, #ababb5), color-stop(0.93, #92939b), color-stop(0.95, #73747d), color-stop(1, #313137)); background: linear-gradient(to bottom, #313137 0%, #73747d 5%, #92939b 7%, #ababb5 9%, #c5c6ce 12%, #dfe0e4 16%, #f4f5f6 22%, #fbfcfc 35%, #fbfcfc 50%, #fbfcfc 61%, #b4c1c7 61%, #fbfcfc 65%, #f4f5f6 78%, #dfe0e4 84%, #c5c6ce 88%, #ababb5 91%, #92939b 93%, #73747d 95%, #313137 100%); height: 200px; border-left: solid 3px #000000; border-right: solid 3px #000000; color: #000000; border-radius: 3px;}.mblSpinWheelBar {position: absolute; top: 79px; background: -webkit-gradient(linear, left top, left bottom, from(#edeef2), to(#a7adca), color-stop(0, #edeef2), color-stop(0.25, #c8cadd), color-stop(0.49, #bbbfd4), color-stop(0.51, #9fa8c6), color-stop(0.81, #a2a9c7), color-stop(0.82, #a6abc9), color-stop(1, #a7adca)); background: linear-gradient(to bottom, #edeef2 0%, #c8cadd 25%, #bbbfd4 49%, #9fa8c6 51%, #a2a9c7 81%, #a6abc9 82%, #a7adca 100%); border: solid 1px #7b8497; height: 42px; width: 100%; clear: both; opacity: 0.6;}.mblSpinWheelDatePicker {width: 312px;}.mblSpinWheelTimePicker {width: 98px;}.mblSpinWheelSlot {position: relative; top: 0px; float: left; width: 100px; height: 100%; border-left: solid 2px #000000; border-right: solid 2px #000000; -webkit-tap-highlight-color: rgba(255, 255, 255, 0);}.mblSpinWheelSlotLabel {padding: 0 8px; height: 44px; overflow: hidden; font: bold 24px/44px Helvetica, sans-serif;}.mblSpinWheelSlotLabel img {vertical-align: middle; opacity: 0.7;}.mblSpinWheelSlotLabelGray {color: #cccccc;}.mblSpinWheelSlotLabelBlue {color: #0959d2;}.mblSpinWheelSlotContainer {position: relative;}.mblSpinWheelSlotPanel {position: absolute; top: 0px; width: 100%;}.mblSpinWheelSlotTouch {position: absolute; top: 0px; width: 100%; height: 100%; z-index: 1;}.dj_ie .mblSpinWheelSlotTouch {background-color: rgba(255, 255, 255, 0.01);}.dj_ie8 .mblSpinWheelSlotTouch {background: white; filter: alpha(opacity=0);}.mblSpinWheel {background-image: none; border: none;}.mblSpinWheelBar {background-image: none; background-color: rgba(0, 0, 0, 0); border: none; border-top: solid 1px #929292; border-bottom: solid 1px #929292;}.mblSpinWheelSlot {border: none; color: #929292;}