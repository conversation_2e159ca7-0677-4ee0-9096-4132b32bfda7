.mblTabBarButtonRtl {float: right;}.mblTabBarButtonIconAreaRtl {margin: 0 auto; width: 29px;}.mblTabBarTabBar .mblTabBarButtonIconAreaRtl {padding-top: 4px;}.mblTabBarSegmentedControl.mblTabBarNoText .mblTabBarButtonIconAreaRtl {position: relative;}.mblTabBarSegmentedControl .mblTabBarButtonRtl {border-width: 1px 0px 1px 1px !important;}.mblTabBarSegmentedControl .mblTabBarButtonRtl:first-child {border-right-width: 1px !important; border-left-width: 0px; border-top-right-radius: 5px; border-top-left-radius: 0px; border-bottom-right-radius: 5px; border-bottom-left-radius: 0px;}.mblTabBarSegmentedControl .mblTabBarButtonRtl:last-child {border-top-left-radius: 5px; border-top-right-radius: 0px; border-bottom-left-radius: 5px; border-bottom-right-radius: 0px; border-left-width: 1px !important;}.mblTabBarSegmentedControl .mblTabBarButtonIconAreaRtl {position: absolute; top: 0px; right: 0px; left: auto;}.mblHeading .mblTabBarSegmentedControl.mblTabBarRtl {float: right;}.mblTabBarStandardTab .mblTabBarButtonIconAreaRtl {position: absolute; top: 3px; right: 0px; left: auto;}.mblTabBarStandardTab .mblTabBarButtonRtl.mblTabBarButtonHasIcon .mblTabBarButtonLabel {margin-right: 20px; margin-left: 0px;}.mblTabBarSlimTab .mblTabBarButtonRtl {border-left: 1px solid #4e4e4e;}.mblTabBarSlimTab .mblTabBarButtonRtl:first-child {border-right: 1px solid #4e4e4e;}.mblTabBarSlimTab .mblTabBarButtonIconAreaRtl {position: absolute; top: 0px; right: 0px; left: auto;}.mblTabBarSlimTab .mblTabBarButtonRtl.mblTabBarButtonHasIcon .mblTabBarButtonLabel {margin-right: 20px; margin-left: 0px;}.mblTabBarFlatTab .mblTabBarButtonIconAreaRtl {position: absolute; top: 0px; right: 0px; left: auto;}.mblTabBarFlatTab .mblTabBarButtonRtl.mblTabBarButtonHasIcon .mblTabBarButtonLabel {margin-right: 20px; margin-left: 0px;}.mblTabBarTallTab .mblTabBarButtonRtl {margin-left: 2px;}.mblTabBarTallTab .mblTabBarButtonIconAreaRtl {margin-top: 8px;}