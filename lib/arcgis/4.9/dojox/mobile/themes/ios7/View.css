.mblSlide {-webkit-transition-property: none; transition-property: none; -webkit-transition-duration: 0s; transition-duration: 0s;}.mblSlide.mblTransition {-webkit-transition-property: -webkit-transform; transition-property: transform; -webkit-transition-duration: 0.3s; transition-duration: 0.3s;}.mblSlide.mblOut.mblReverse.mblTransition,.mblSlide.mblIn {-webkit-transform: translate3d(100%, 0px, 0px) !important; transform: translate3d(100%, 0px, 0px) !important;}.mblSlide.mblOut.mblTransition,.mblSlide.mblIn.mblReverse {-webkit-transform: translate3d(-100%, 0px, 0px) !important; transform: translate3d(-100%, 0px, 0px) !important;}.mblSlide.mblOut,.mblSlide.mblIn.mblTransition {-webkit-transform: translate3d(0%, 0px, 0px) !important; transform: translate3d(0%, 0px, 0px) !important;}.dj_android.dj_tablet .mblSlide.mblTransition {-webkit-transition-duration: 0.6s; transition-duration: 0.6s; -webkit-transition-timing-function: linear; transition-timing-function: linear;}.mblFlip {-webkit-transition-property: none; transition-property: none; -webkit-transition-duration: 0s; transition-duration: 0s;}.mblFlip.mblTransition {-webkit-transition-property: all; transition-property: all; -webkit-transition-duration: 0.2s; transition-duration: 0.2s; -webkit-transition-timing-function: linear; transition-timing-function: linear;}.mblFlip.mblOut {opacity: 1; -webkit-transform: scale(1, 1) skew(0, 0) !important; transform: scale(1, 1) skew(0, 0) !important;}.mblFlip.mblOut.mblTransition {opacity: 0; -webkit-transform: scale(0, 0.8) skew(0, 30deg) !important; transform: scale(0, 0.8) skew(0, 30deg) !important;}.mblFlip.mblIn {opacity: 0; -webkit-transform: scale(0, 0.8) skew(0, -30deg) !important; transform: scale(0, 0.8) skew(0, -30deg) !important;}.mblFlip.mblIn.mblTransition {-webkit-transition-delay: 0.2s; transition-delay: 0.2s; opacity: 1; -webkit-transform: scale(1, 1) skew(0, 0) !important; transform: scale(1, 1) skew(0, 0) !important;}.dj_android.dj_tablet .mblFlip.mblTransition {-webkit-transition-duration: 0.4s; transition-duration: 0.4s;}.dj_android.dj_tablet .mblFlip.mblIn.mblTransition {-webkit-transition-delay: 0.4s; transition-delay: 0.4s;}.mblFade {-webkit-transition-property: none; transition-property: none; -webkit-transition-duration: 0s; transition-duration: 0s;}.mblFade.mblTransition {-webkit-transition-property: opacity; transition-property: opacity; -webkit-transition-duration: 0.6s; transition-duration: 0.6s;}.mblFade.mblOut {opacity: 1;}.mblFade.mblOut.mblTransition {-webkit-transition-timing-function: ease-out; transition-timing-function: ease-out; opacity: 0;}.mblFade.mblIn {opacity: 0;}.mblFade.mblIn.mblTransition {-webkit-transition-timing-function: ease-in; transition-timing-function: ease-in; opacity: 1;}.mblView {position: relative; top: 0px; left: 0px; width: 100%; color: #000000;}.mblView.mblIn {position: absolute;}.mblFixedHeaderBar {z-index: 1;}.mblFixedBottomBar {position: absolute !important; width: 100%; z-index: 1;}