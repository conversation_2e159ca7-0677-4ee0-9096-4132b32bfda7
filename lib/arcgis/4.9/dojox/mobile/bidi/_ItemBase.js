//>>built
define(["dojo/_base/declare","./common"],function(c,b){return c(null,{_setLabelAttr:function(a){this._set("label",a);this.labelNode.innerHTML=this._cv?this._cv(a):a;this.textDir||(this.textDir=(a=this.getParent())&&a.get("textDir")?a.get("textDir"):"");this.labelNode.innerHTML=b.enforceTextDirWithUcc(this.labelNode.innerHTML,this.textDir)},_setTextDirAttr:function(a){this._created&&this.textDir===a||(this._set("textDir",a),this.labelNode.innerHTML=b.enforceTextDirWithUcc(b.removeUCCFromText(this.labelNode.innerHTML),
this.textDir),this.badgeObj&&this.badgeObj.setTextDir&&this.badgeObj.setTextDir(a))},getTransOpts:function(){var a=this.inherited(arguments);this.isLeftToRight()||(a.transitionDir*=-1);return a}})});