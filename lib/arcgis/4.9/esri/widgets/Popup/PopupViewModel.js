// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.9/esri/copyright.txt for details.
//>>built
define("require exports ../../core/tsSupport/declareExtendsHelper ../../core/tsSupport/decorateHelper ../../core/tsSupport/assignHelper ../../core/Collection ../../core/Error ../../core/Handles ../../core/Logger ../../core/promiseUtils ../../core/watchUtils ../../core/accessorSupport/decorators ../../geometry/Point ../../geometry/support/webMercatorUtils ../../support/actions/ActionBase ../../support/actions/ActionButton ../../support/actions/ActionToggle ../../views/support/layerViewUtils ../support/AnchorElementViewModel ../support/GoTo".split(" "),
function(H,I,t,d,u,v,m,w,x,n,k,c,y,z,A,g,B,C,D,E){var f=new g({id:"zoom-to"}),l=v.ofType({key:"type",defaultKeyValue:"button",base:A,typeMap:{button:g,toggle:B}}),F=new l([f]),p=x.getLogger("esri.widgets.Popup.PopupViewModel");return function(g){function b(a){a=g.call(this)||this;a._handles=new w;a._pendingPromises=new Set;a._zoomToLocation=null;a.actions=F;a.autoCloseEnabled=!1;a.content=null;a.highlightEnabled=!0;a.title=null;a.updateLocationEnabled=!1;a.view=null;a.visible=!1;a.zoomFactor=4;return a}
t(b,g);b.prototype.initialize=function(){var a=this;this._handles.add([this.on("view-change",this._autoClose),k.watch(this,["highlightEnabled","selectedFeature","visible","view"],this._highlightFeature),k.watch(this,"view.animation.state",function(b){a._zoomToLocation||(f.disabled="waiting-for-target"===b)})])};b.prototype.destroy=function(){this._handles.destroy();this._handles=null;this._pendingPromises.clear();this.view=null};Object.defineProperty(b.prototype,"allActions",{get:function(){var a=
this._get("allActions")||new l;a.removeAll();var b=this.selectedFeature&&("function"===typeof this.selectedFeature.getEffectivePopupTemplate&&this.selectedFeature.getEffectivePopupTemplate()||this.selectedFeature.popupTemplate),e=b&&b.actions;(b=b&&b.overwriteActions?e:this.actions.concat(e))&&b.filter(Boolean).forEach(function(b){return a.add(b)});return a},enumerable:!0,configurable:!0});Object.defineProperty(b.prototype,"featureCount",{get:function(){return this.features.length},enumerable:!0,
configurable:!0});Object.defineProperty(b.prototype,"features",{get:function(){return this._get("features")||[]},set:function(a){a=a||[];this._set("features",a);var b=this.pendingPromisesCount,e=this.selectedFeatureIndex,q=this.promiseCount&&a.length;q&&b&&-1===e?this.selectedFeatureIndex=0:q&&-1!==e||(this.selectedFeatureIndex=a.length?0:-1)},enumerable:!0,configurable:!0});Object.defineProperty(b.prototype,"location",{get:function(){return this._get("location")||null},set:function(a){var b=this.get("location"),
e=this.get("view.spatialReference.isWebMercator");a&&a.get("spatialReference.isWGS84")&&e&&(a=z.geographicToWebMercator(a));this._set("location",a);a!==b&&this._centerAtLocation()},enumerable:!0,configurable:!0});Object.defineProperty(b.prototype,"pendingPromisesCount",{get:function(){return this._pendingPromises.size},enumerable:!0,configurable:!0});Object.defineProperty(b.prototype,"waitingForResult",{get:function(){return 0<this.pendingPromisesCount&&0===this.featureCount},enumerable:!0,configurable:!0});
Object.defineProperty(b.prototype,"promiseCount",{get:function(){return this.promises.length},enumerable:!0,configurable:!0});Object.defineProperty(b.prototype,"promises",{get:function(){return this._get("promises")||[]},set:function(a){var b=this,e=this._get("promises");e&&e.forEach(function(a){a&&"function"===typeof a.cancel&&a.cancel()});this._pendingPromises.clear();this.features=[];Array.isArray(a)&&a.length?(this._set("promises",a),a=a.slice(0),a.forEach(function(a){b._pendingPromises.add(a);
a.then(function(G){b._updatePendingPromises(a);b._updateFeatures(G)},function(){return b._updatePendingPromises(a)})})):this._set("promises",[]);this.notifyChange("pendingPromisesCount")},enumerable:!0,configurable:!0});Object.defineProperty(b.prototype,"selectedFeature",{get:function(){var a=this.selectedFeatureIndex;if(-1===a)return null;a=this.features[a];if(!a)return null;this.updateLocationEnabled&&(this.location=this._getPointFromGeometry(a.geometry));return a},enumerable:!0,configurable:!0});
Object.defineProperty(b.prototype,"selectedFeatureIndex",{get:function(){var a=this._get("selectedFeatureIndex");return"number"===typeof a?a:-1},set:function(a){var b=this.featureCount;a=isNaN(a)||-1>a||!b?-1:(a+b)%b;this._set("selectedFeatureIndex",a)},enumerable:!0,configurable:!0});Object.defineProperty(b.prototype,"state",{get:function(){return this.get("view.ready")?"ready":"disabled"},enumerable:!0,configurable:!0});b.prototype.centerAtLocation=function(){var a=this.location,b=this.view;return a&&
b?this.callGoTo({target:{target:a,scale:b.scale}}):(a=new m("center-at-location:invalid-location-or-view","Cannot center at a location without a location and view.",{location:a,view:b}),p.error(a),n.reject(a))};b.prototype.clear=function(){this.set({promises:[],features:[],content:null,title:null,location:null})};b.prototype.open=function(a){this.set(u({visible:!1},{updateLocationEnabled:!1,promises:[]},a));this._setVisibleWhenContentExists()};b.prototype.triggerAction=function(a){(a=this.allActions.getItemAt(a))&&
this.emit("trigger-action",{action:a})};b.prototype.next=function(){this.selectedFeatureIndex+=1;return this};b.prototype.previous=function(){--this.selectedFeatureIndex;return this};b.prototype.zoomToLocation=function(){var a=this,b=this.location,e=this.selectedFeature,c=this.view,d=this.zoomFactor;if(!b||!c)return e=new m("zoom-to:invalid-location-or-view","Cannot zoom to location without a location and view.",{location:b,view:c}),p.error(e),n.reject(e);var d=c.scale/d,h=this.get("selectedFeature.geometry"),
c=e&&"3d"===c.type,b=(c=h||c)?e:b,r=h&&"point"===h.type,g=c&&r&&this._isScreenSize(e),e={target:b,scale:g?d:void 0};f.active=!0;f.disabled=!0;return this._zoomToLocation=e=this.callGoTo({target:e}).then(function(){g&&r&&(a.location=h);f.active=!1;f.disabled=!1;a._zoomToLocation=null}).catch(function(){f.active=!1;f.disabled=!1;a._zoomToLocation=null})};b.prototype._updatePendingPromises=function(a){a&&this._pendingPromises.has(a)&&(this._pendingPromises.delete(a),this.notifyChange("pendingPromisesCount"))};
b.prototype._setVisibleWhenContentExists=function(){var a=this,b=this._handles,c=this.promiseCount;b.remove("pendingVisible");c?(c=k.init(this,"pendingPromisesCount",function(c){a.featureCount&&(a.set("visible",!0),b.remove("pendingVisible"));c||b.remove("pendingVisible")}),b.add(c,"pendingVisible")):this.set("visible",!0)};b.prototype._autoClose=function(){this.autoCloseEnabled&&(this.visible=!1)};b.prototype._isScreenSize=function(a){var b=this.view;if("3d"!==b.type||"esri.Graphic"!==a.declaredClass)return!0;
if((b=b.getViewForGraphic(a))&&b.whenGraphicBounds){var c=!1;b.whenGraphicBounds(a,{useViewElevation:!0}).then(function(a){c=!a||!a.boundingBox||a.boundingBox[0]===a.boundingBox[3]&&a.boundingBox[1]===a.boundingBox[4]&&a.boundingBox[2]===a.boundingBox[5]});return c}return!0};b.prototype._getPointFromGeometry=function(a){return a?"point"===a.type?a:"extent"===a.type?a.center:"polygon"===a.type?a.centroid:"multipoint"===a.type||"polyline"===a.type?a.extent.center:null:null};b.prototype._centerAtLocation=
function(){var a=this.location,b=this.updateLocationEnabled,c=this.get("view.extent");b&&c&&a&&!c.contains(a)&&this.centerAtLocation()};b.prototype._highlightFeature=function(){var a=this;this._handles.remove("highlight");var b=this.selectedFeature,c=this.highlightEnabled,d=this.view,g=this.visible;if(b&&d&&c&&g){var f=b.layer;f&&d.when(function(){d.whenLayerView(f).then(function(c){if(c&&C.hasHighlight(c)){var d=f.objectIdField,e=b.attributes;c=c.highlight(e&&e[d]||b,{});a._handles.add(c,"highlight")}})})}};
b.prototype._updateFeatures=function(a){var b=this.features;a&&a.length&&(b.length?(a=a.filter(function(a){return-1===b.indexOf(a)}),this.features=b.concat(a)):this.features=a)};d([c.property({type:l})],b.prototype,"actions",void 0);d([c.property({dependsOn:["actions.length","selectedFeature.sourceLayer.popupTemplate.actions.length","selectedFeature.sourceLayer.popupTemplate.overwriteActions","selectedFeature.popupTemplate.actions.length","selectedFeature.popupTemplate.overwriteActions"],readOnly:!0})],
b.prototype,"allActions",null);d([c.property()],b.prototype,"autoCloseEnabled",void 0);d([c.property()],b.prototype,"content",void 0);d([c.property({readOnly:!0,dependsOn:["features"]})],b.prototype,"featureCount",null);d([c.property()],b.prototype,"features",null);d([c.property()],b.prototype,"highlightEnabled",void 0);d([c.property({type:y})],b.prototype,"location",null);d([c.property({readOnly:!0,dependsOn:["promises"]})],b.prototype,"pendingPromisesCount",null);d([c.property({readOnly:!0,dependsOn:["featureCount",
"pendingPromisesCount"]})],b.prototype,"waitingForResult",null);d([c.property({readOnly:!0,dependsOn:["promises"]})],b.prototype,"promiseCount",null);d([c.property()],b.prototype,"promises",null);d([c.property({value:null,readOnly:!0,dependsOn:["features","selectedFeatureIndex","updateLocationEnabled"]})],b.prototype,"selectedFeature",null);d([c.property({value:-1})],b.prototype,"selectedFeatureIndex",null);d([c.property({readOnly:!0,dependsOn:["view.ready"]})],b.prototype,"state",null);d([c.property()],
b.prototype,"title",void 0);d([c.property()],b.prototype,"updateLocationEnabled",void 0);d([c.property()],b.prototype,"view",void 0);d([c.property()],b.prototype,"visible",void 0);d([c.property()],b.prototype,"zoomFactor",void 0);d([c.property()],b.prototype,"centerAtLocation",null);d([c.property()],b.prototype,"clear",null);d([c.property()],b.prototype,"triggerAction",null);d([c.property()],b.prototype,"next",null);d([c.property()],b.prototype,"previous",null);d([c.property()],b.prototype,"zoomToLocation",
null);return b=d([c.subclass("esri.widgets.Popup.PopupViewModel")],b)}(c.declared(D,E))});