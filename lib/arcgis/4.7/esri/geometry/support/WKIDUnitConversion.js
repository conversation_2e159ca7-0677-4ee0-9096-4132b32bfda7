// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define([],function(){var a,b={values:[1,.3048,.3048006096012192,.3047972654,.9143917962,.************,.9143984146160287,.3047994715386762,20.11676512155263,20.11678249437587,.9143985307444408,.91439523,.3047997101815088,20.116756,5E4,15E4],units:"Meter Foot Foot_US Foot_Clarke Yard_Clarke Link_Clarke Yard_Sears Foot_Sears Chain_Sears Chain_Benoit_1895_B Yard_Indian Yard_Indian_1937 Foot_Gold_Coast Chain_Sears_1922_Truncated 50_Kilometers 150_Kilometers".split(" "),2066:5,2136:12,2155:2,2157:0,2158:0,
2159:12,2160:12,2204:2,2219:0,2220:0,2254:2,2255:2,2256:1,2265:1,2266:1,2267:2,2268:2,2269:1,2270:1,2271:2,2272:2,2273:1,2294:0,2295:0,2314:3,2899:2,2900:2,2901:1,2909:1,2910:1,2911:2,2912:2,2913:1,2914:1,2992:1,2993:0,2994:1,3080:1,3089:2,3090:0,3091:2,3102:2,3141:0,3142:0,3167:13,3359:2,3360:0,3361:1,3362:0,3363:2,3364:0,3365:2,3366:3,3404:2,3405:0,3406:0,3407:3,3439:0,3440:0,3479:1,3480:0,3481:1,3482:0,3483:1,3484:0,3485:2,3486:0,3487:2,3488:0,3489:0,3490:2,3491:0,3492:2,3493:0,3494:2,3495:0,3496:2,
3497:0,3498:2,3499:0,3500:2,3501:0,3502:2,3503:0,3504:2,3505:0,3506:2,3507:0,3508:2,3509:0,3510:2,3511:0,3512:2,3513:0,3514:0,3515:2,3516:0,3517:2,3518:0,3519:2,3520:0,3521:2,3522:0,3523:2,3524:0,3525:2,3526:0,3527:2,3528:0,3529:2,3530:0,3531:2,3532:0,3533:2,3534:0,3535:2,3536:0,3537:2,3538:0,3539:2,3540:0,3541:2,3542:0,3543:2,3544:0,3545:2,3546:0,3547:2,3548:0,3549:2,3550:0,3551:2,3552:0,3553:2,3582:2,3583:0,3584:2,3585:0,3586:2,3587:0,3588:1,3589:0,3590:1,3591:0,3592:0,3593:1,3598:2,3599:0,3600:2,
3605:1,3606:0,3607:0,3608:2,3609:0,3610:2,3611:0,3612:2,3613:0,3614:2,3615:0,3616:2,3617:0,3618:2,3619:0,3620:2,3621:0,3622:2,3623:0,3624:2,3625:0,3626:2,3627:0,3628:2,3629:0,3630:2,3631:0,3632:2,3633:0,3634:1,3635:0,3636:1,3640:2,3641:0,3642:2,3643:0,3644:1,3645:0,3646:1,3647:0,3648:1,3649:0,3650:2,3651:0,3652:2,3653:0,3654:2,3655:0,3656:1,3657:0,3658:2,3659:0,3660:2,3661:0,3662:2,3663:0,3664:2,3668:2,3669:0,3670:2,3671:0,3672:2,3673:0,3674:2,3675:0,3676:1,3677:2,3678:0,3679:1,3680:2,3681:0,3682:1,
3683:2,3684:0,3685:0,3686:2,3687:0,3688:2,3689:0,3690:2,3691:0,3692:2,3696:2,3697:0,3698:2,3699:0,3700:2,3793:0,3794:0,3812:0,3854:0,3857:0,3920:0,3978:0,3979:0,3991:2,3992:2,4026:0,4037:0,4038:0,4071:0,4082:0,4083:0,4087:0,4088:0,4217:2,4414:0,4415:0,4417:0,4434:0,4437:0,4438:2,4439:2,4462:0,4467:0,4471:0,4474:0,4559:0,4647:0,4822:0,4826:0,4839:0,5018:0,5048:0,5167:0,5168:0,5221:0,5223:0,5234:0,5235:0,5243:0,5247:0,5266:0,5316:0,5320:0,5321:0,5325:0,5337:0,5361:0,5362:0,5367:0,5382:0,5383:0,5396:0,
5456:0,5457:0,5469:0,5472:4,5490:0,5513:0,5514:0,5523:0,5559:0,5588:1,5589:3,5596:0,5627:0,5629:0,5641:0,5643:0,5644:0,5646:2,5654:2,5655:2,5659:0,5700:0,5825:0,5836:0,5837:0,5839:0,5842:0,5844:0,5858:0,5879:0,5880:0,5887:0,5890:0,6128:1,6129:1,6141:1,6204:0,6210:0,6211:0,6307:0,6312:0,6316:0,6362:0,6391:1,6405:1,6406:0,6407:1,6408:0,6409:1,6410:0,6411:2,6412:0,6413:2,6414:0,6415:0,6416:2,6417:0,6418:2,6419:0,6420:2,6421:0,6422:2,6423:0,6424:2,6425:0,6426:2,6427:0,6428:2,6429:0,6430:2,6431:0,6432:2,
6433:0,6434:2,6435:0,6436:2,6437:0,6438:2,6439:0,6440:0,6441:2,6442:0,6443:2,6444:0,6445:2,6446:0,6447:2,6448:0,6449:2,6450:0,6451:2,6452:0,6453:2,6454:0,6455:2,6456:0,6457:2,6458:0,6459:2,6460:0,6461:2,6462:0,6463:2,6464:0,6465:2,6466:0,6467:2,6468:0,6469:2,6470:0,6471:2,6472:0,6473:2,6474:0,6475:2,6476:0,6477:2,6478:0,6479:2,6484:2,6485:0,6486:2,6487:0,6488:2,6489:0,6490:2,6491:0,6492:2,6493:0,6494:1,6495:0,6496:1,6497:0,6498:0,6499:1,6500:0,6501:2,6502:0,6503:2,6504:0,6505:2,6506:0,6507:2,6508:0,
6509:0,6510:2,6515:1,6516:0,6518:0,6519:2,6520:0,6521:2,6522:0,6523:2,6524:0,6525:2,6526:0,6527:2,6528:0,6529:2,6530:0,6531:2,6532:0,6533:2,6534:0,6535:2,6536:0,6537:2,6538:0,6539:2,6540:0,6541:2,6542:0,6543:2,6544:0,6545:1,6546:0,6547:1,6548:0,6549:2,6550:0,6551:2,6552:0,6553:2,6554:0,6555:2,6556:0,6557:1,6558:0,6559:1,6560:0,6561:1,6562:0,6563:2,6564:0,6565:2,6566:0,6567:0,6568:2,6569:0,6570:1,6571:0,6572:2,6573:0,6574:2,6575:0,6576:2,6577:0,6578:2,6582:2,6583:0,6584:2,6585:0,6586:2,6587:0,6588:2,
6589:0,6590:2,6591:0,6592:0,6593:2,6594:0,6595:2,6596:0,6597:2,6598:0,6599:2,6600:0,6601:2,6602:0,6603:2,6605:2,6606:0,6607:2,6608:0,6609:2,6610:0,6611:0,6612:2,6613:0,6614:2,6615:0,6616:2,6617:0,6618:2,6633:2,6646:0,6703:0,6784:0,6785:1,6786:0,6787:1,6788:0,6789:1,6790:0,6791:1,6792:0,6793:1,6794:0,6795:1,6796:0,6797:1,6798:0,6799:1,6800:0,6801:1,6802:0,6803:1,6804:0,6805:1,6806:0,6807:1,6808:0,6809:1,6810:0,6811:1,6812:0,6813:1,6814:0,6815:1,6816:0,6817:1,6818:0,6819:1,6820:0,6821:1,6822:0,6823:1,
6824:0,6825:1,6826:0,6827:1,6828:0,6829:1,6830:0,6831:1,6832:0,6833:1,6834:0,6835:1,6836:0,6837:1,6838:0,6839:1,6840:0,6841:1,6842:0,6843:1,6844:0,6845:1,6846:0,6847:1,6848:0,6849:1,6850:0,6851:1,6852:0,6853:1,6854:0,6855:1,6856:0,6857:1,6858:0,6859:1,6860:0,6861:1,6862:0,6863:1,6867:0,6868:1,6870:0,6875:0,6876:0,6879:0,6880:2,6884:0,6885:1,6886:0,6887:1,6915:0,6922:0,6923:2,6924:0,6925:2,6962:0,6984:0,6991:0,7128:2,7131:0,7132:2,7142:0,7257:0,7258:2,7259:0,7260:2,7261:0,7262:2,7263:0,7264:2,7265:0,
7266:2,7267:0,7268:2,7269:0,7270:2,7271:0,7272:2,7273:0,7274:2,7275:0,7276:2,7277:0,7278:2,7279:0,7280:2,7281:0,7282:2,7283:0,7284:2,7285:0,7286:2,7287:0,7288:2,7289:0,7290:2,7291:0,7292:2,7293:0,7294:2,7295:0,7296:2,7297:0,7298:2,7299:0,7300:2,7301:0,7302:2,7303:0,7304:2,7305:0,7306:2,7307:0,7308:2,7309:0,7310:2,7311:0,7312:2,7313:0,7314:2,7315:0,7316:2,7317:0,7318:2,7319:0,7320:2,7321:0,7322:2,7323:0,7324:2,7325:0,7326:2,7327:0,7328:2,7329:0,7330:2,7331:0,7332:2,7333:0,7334:2,7335:0,7336:2,7337:0,
7338:2,7339:0,7340:2,7341:0,7342:2,7343:0,7344:2,7345:0,7346:2,7347:0,7348:2,7349:0,7350:2,7351:0,7352:2,7353:0,7354:2,7355:0,7356:2,7357:0,7358:2,7359:0,7360:2,7361:0,7362:2,7363:0,7364:2,7365:0,7366:2,7367:0,7368:2,7369:0,7370:2,7877:0,7878:0,7882:0,7883:0,7887:0,7899:0,7991:0,7992:0,8058:0,8059:0,8311:0,8312:1,8313:0,8314:1,8315:0,8316:1,8317:0,8318:1,8319:0,8320:1,8321:0,8322:1,8323:0,8324:1,8325:0,8326:1,8327:0,8328:1,8329:0,8330:1,8331:0,8332:1,8333:0,8334:1,8335:0,8336:1,8337:0,8338:1,8339:0,
8340:1,8341:0,8342:1,8343:0,8344:1,8345:0,8346:1,8347:0,8348:1,20499:0,20538:0,20539:0,20790:0,20791:0,21291:0,21292:0,21500:0,21817:0,21818:0,22032:0,22033:0,22091:0,22092:0,22332:0,22391:0,22392:0,22700:0,22770:0,22780:0,22832:0,23090:0,23095:0,23239:0,23240:0,23433:0,23700:0,24047:0,24048:0,24100:3,24200:0,24305:0,24306:0,24382:10,24383:0,24500:0,24547:0,24548:0,24571:9,24600:0,25E3:0,25231:0,25884:0,25932:0,26237:0,26331:0,26332:0,26432:0,26591:0,26592:0,26632:0,26692:0,27120:0,27200:0,27291:6,
27292:6,27429:0,27492:0,27493:0,27500:0,27700:0,28232:0,28600:0,28991:0,28992:0,29100:0,29101:0,29220:0,29221:0,29333:0,29635:0,29636:0,29701:0,29738:0,29739:0,29849:0,29850:0,29871:8,29872:7,29873:0,30200:5,30339:0,30340:0,30591:0,30592:0,30791:0,30792:0,30800:0,31028:0,31121:0,31154:0,31170:0,31171:0,31370:0,31528:0,31529:0,31600:0,31700:0,31838:0,31839:0,31900:0,31901:0,32061:0,32062:0,32098:0,32099:2,32100:0,32104:0,32161:0,32766:0,53034:0,53048:0,53049:0,54034:0,65061:2,65062:2,65161:0,65163:0,
102041:2,102064:11,102068:14,102069:15,102118:2,102119:1,102120:2,102121:2,102217:2,102218:0,102219:2,102220:2,102378:1,102379:1,102380:0,102381:1,102589:2,102599:2,102600:2,102604:2,102647:0,102704:2,102705:2,102706:0,102761:2,102762:0,102763:2,102764:0,102765:0,102766:2,102962:0,102963:0,102970:1,102974:2,102993:0,102994:0,102995:2,102996:2,103015:0,103016:2,103017:0,103018:2,103025:0,103026:0,103027:2,103028:2,103035:0,103036:0,103037:2,103038:2,103039:0,103040:0,103041:2,103042:2,103043:0,103044:0,
103045:2,103046:2,103047:0,103048:0,103049:2,103050:2,103051:0,103052:2,103053:0,103054:2,103055:0,103056:2,103057:0,103058:0,103059:2,103060:2,103061:0,103062:0,103063:2,103064:2,103069:2,103070:0,103071:0,103072:2,103073:2,103086:0,103087:0,103088:2,103089:2,103094:1,103095:0,103096:2,103103:0,103104:2,103105:0,103106:2,103121:0,103122:2,103123:0,103124:0,103125:1,103126:1,103127:0,103128:0,103129:2,103130:2,103131:0,103132:0,103133:2,103134:2,103135:0,103136:0,103137:1,103138:1,103139:0,103140:2,
103141:0,103142:2,103143:0,103144:2,103145:0,103146:1,103147:0,103148:0,103149:2,103150:2,103151:0,103152:2,103172:0,103173:2,103174:0,103175:0,103176:2,103177:2,103178:0,103179:0,103180:2,103181:2,103182:0,103183:0,103184:2,103185:2,103228:0,103229:0,103230:2,103231:2,103250:0,103251:2,103252:0,103253:2,103260:0,103261:0,103262:2,103263:2,103270:0,103271:0,103272:2,103273:2,103274:0,103275:0,103276:2,103277:2,103278:0,103279:0,103280:2,103281:2,103282:0,103283:0,103284:2,103285:2,103286:0,103287:2,
103288:0,103289:2,103290:0,103291:2,103292:0,103293:0,103294:2,103295:2,103296:0,103297:0,103298:2,103299:2,103376:2,103377:0,103378:0,103379:2,103380:2,103393:0,103394:0,103395:2,103396:2,103472:0,103473:1,103474:0,103475:2,103482:0,103483:2,103484:0,103485:2,103500:0,103501:2,103502:0,103503:0,103504:1,103505:1,103506:0,103507:0,103508:2,103509:2,103510:0,103511:0,103512:2,103513:2,103514:0,103515:2,103516:0,103517:2,103518:0,103519:2,103520:0,103521:1,103522:0,103523:0,103524:2,103525:2,103526:0,
103527:2,103561:2,103562:2,103563:0,103564:0,103565:2,103566:2,103567:0,103568:0,103569:2,103570:2,103584:0,103585:2,103695:2};for(a=2E3;2045>=a;a++)b[a]=0;for(a=2056;2065>=a;a++)b[a]=0;for(a=2067;2135>=a;a++)b[a]=0;for(a=2137;2154>=a;a++)b[a]=0;for(a=2161;2170>=a;a++)b[a]=0;for(a=2172;2193>=a;a++)b[a]=0;for(a=2195;2198>=a;a++)b[a]=0;for(a=2200;2203>=a;a++)b[a]=0;for(a=2205;2217>=a;a++)b[a]=0;for(a=2222;2224>=a;a++)b[a]=1;for(a=2225;2250>=a;a++)b[a]=2;for(a=2251;2253>=a;a++)b[a]=1;for(a=2257;2264>=
a;a++)b[a]=2;for(a=2274;2279>=a;a++)b[a]=2;for(a=2280;2282>=a;a++)b[a]=1;for(a=2283;2289>=a;a++)b[a]=2;for(a=2290;2292>=a;a++)b[a]=0;for(a=2308;2313>=a;a++)b[a]=0;for(a=2315;2491>=a;a++)b[a]=0;for(a=2494;2866>=a;a++)b[a]=0;for(a=2867;2869>=a;a++)b[a]=1;for(a=2870;2888>=a;a++)b[a]=2;for(a=2891;2895>=a;a++)b[a]=2;for(a=2896;2898>=a;a++)b[a]=1;for(a=2902;2908>=a;a++)b[a]=2;for(a=2915;2920>=a;a++)b[a]=2;for(a=2921;2923>=a;a++)b[a]=1;for(a=2924;2930>=a;a++)b[a]=2;for(a=2931;2962>=a;a++)b[a]=0;for(a=2964;2968>=
a;a++)b[a]=2;for(a=2969;2973>=a;a++)b[a]=0;for(a=2975;2991>=a;a++)b[a]=0;for(a=2995;3051>=a;a++)b[a]=0;for(a=3054;3079>=a;a++)b[a]=0;for(a=3081;3088>=a;a++)b[a]=0;for(a=3092;3101>=a;a++)b[a]=0;for(a=3106;3138>=a;a++)b[a]=0;for(a=3146;3151>=a;a++)b[a]=0;for(a=3153;3166>=a;a++)b[a]=0;for(a=3168;3172>=a;a++)b[a]=0;for(a=3174;3203>=a;a++)b[a]=0;for(a=3294;3358>=a;a++)b[a]=0;for(a=3367;3403>=a;a++)b[a]=0;for(a=3408;3416>=a;a++)b[a]=0;for(a=3417;3438>=a;a++)b[a]=2;for(a=3441;3446>=a;a++)b[a]=2;for(a=3447;3450>=
a;a++)b[a]=0;for(a=3451;3459>=a;a++)b[a]=2;for(a=3460;3478>=a;a++)b[a]=0;for(a=3554;3559>=a;a++)b[a]=0;for(a=3560;3570>=a;a++)b[a]=2;for(a=3571;3581>=a;a++)b[a]=0;for(a=3594;3597>=a;a++)b[a]=0;for(a=3601;3604>=a;a++)b[a]=0;for(a=3637;3639>=a;a++)b[a]=0;for(a=3665;3667>=a;a++)b[a]=0;for(a=3693;3695>=a;a++)b[a]=0;for(a=3701;3727>=a;a++)b[a]=0;for(a=3728;3739>=a;a++)b[a]=2;for(a=3740;3751>=a;a++)b[a]=0;for(a=3753;3760>=a;a++)b[a]=2;for(a=3761;3773>=a;a++)b[a]=0;for(a=3775;3777>=a;a++)b[a]=0;for(a=3779;3781>=
a;a++)b[a]=0;for(a=3783;3785>=a;a++)b[a]=0;for(a=3788;3791>=a;a++)b[a]=0;for(a=3797;3802>=a;a++)b[a]=0;for(a=3814;3816>=a;a++)b[a]=0;for(a=3825;3829>=a;a++)b[a]=0;for(a=3832;3841>=a;a++)b[a]=0;for(a=3844;3852>=a;a++)b[a]=0;for(a=3873;3885>=a;a++)b[a]=0;for(a=3890;3893>=a;a++)b[a]=0;for(a=3907;3912>=a;a++)b[a]=0;for(a=3942;3950>=a;a++)b[a]=0;for(a=3968;3970>=a;a++)b[a]=0;for(a=3973;3976>=a;a++)b[a]=0;for(a=3986;3989>=a;a++)b[a]=0;for(a=3994;3997>=a;a++)b[a]=0;for(a=4048;4051>=a;a++)b[a]=0;for(a=4056;4063>=
a;a++)b[a]=0;for(a=4093;4096>=a;a++)b[a]=0;for(a=4390;4398>=a;a++)b[a]=0;for(a=4399;4413>=a;a++)b[a]=2;for(a=4418;4433>=a;a++)b[a]=2;for(a=4455;4457>=a;a++)b[a]=2;for(a=4484;4489>=a;a++)b[a]=0;for(a=4491;4554>=a;a++)b[a]=0;for(a=4568;4589>=a;a++)b[a]=0;for(a=4652;4656>=a;a++)b[a]=0;for(a=4766;4800>=a;a++)b[a]=0;for(a=5014;5016>=a;a++)b[a]=0;for(a=5069;5072>=a;a++)b[a]=0;for(a=5105;5130>=a;a++)b[a]=0;for(a=5173;5188>=a;a++)b[a]=0;for(a=5253;5259>=a;a++)b[a]=0;for(a=5269;5275>=a;a++)b[a]=0;for(a=5292;5311>=
a;a++)b[a]=0;for(a=5329;5331>=a;a++)b[a]=0;for(a=5343;5349>=a;a++)b[a]=0;for(a=5355;5357>=a;a++)b[a]=0;for(a=5387;5389>=a;a++)b[a]=0;for(a=5459;5463>=a;a++)b[a]=0;for(a=5479;5482>=a;a++)b[a]=0;for(a=5518;5520>=a;a++)b[a]=0;for(a=5530;5539>=a;a++)b[a]=0;for(a=5550;5552>=a;a++)b[a]=0;for(a=5562;5583>=a;a++)b[a]=0;for(a=5623;5625>=a;a++)b[a]=2;for(a=5631;5639>=a;a++)b[a]=0;for(a=5649;5653>=a;a++)b[a]=0;for(a=5663;5680>=a;a++)b[a]=0;for(a=5682;5685>=a;a++)b[a]=0;for(a=5875;5877>=a;a++)b[a]=0;for(a=5921;5940>=
a;a++)b[a]=0;for(a=6050;6125>=a;a++)b[a]=0;for(a=6244;6275>=a;a++)b[a]=0;for(a=6328;6348>=a;a++)b[a]=0;for(a=6350;6356>=a;a++)b[a]=0;for(a=6366;6372>=a;a++)b[a]=0;for(a=6381;6387>=a;a++)b[a]=0;for(a=6393;6404>=a;a++)b[a]=0;for(a=6480;6483>=a;a++)b[a]=0;for(a=6511;6514>=a;a++)b[a]=0;for(a=6579;6581>=a;a++)b[a]=0;for(a=6619;6624>=a;a++)b[a]=0;for(a=6625;6627>=a;a++)b[a]=2;for(a=6628;6632>=a;a++)b[a]=0;for(a=6634;6637>=a;a++)b[a]=0;for(a=6669;6692>=a;a++)b[a]=0;for(a=6707;6709>=a;a++)b[a]=0;for(a=6720;6723>=
a;a++)b[a]=0;for(a=6732;6738>=a;a++)b[a]=0;for(a=6931;6933>=a;a++)b[a]=0;for(a=6956;6959>=a;a++)b[a]=0;for(a=7005;7007>=a;a++)b[a]=0;for(a=7057;7070>=a;a++)b[a]=2;for(a=7074;7082>=a;a++)b[a]=0;for(a=7109;7118>=a;a++)b[a]=0;for(a=7119;7127>=a;a++)b[a]=1;for(a=7374;7376>=a;a++)b[a]=0;for(a=7528;7586>=a;a++)b[a]=0;for(a=7587;7645>=a;a++)b[a]=2;for(a=7755;7787>=a;a++)b[a]=0;for(a=7791;7795>=a;a++)b[a]=0;for(a=7799;7801>=a;a++)b[a]=0;for(a=7803;7805>=a;a++)b[a]=0;for(a=7825;7831>=a;a++)b[a]=0;for(a=7845;7859>=
a;a++)b[a]=0;for(a=8013;8032>=a;a++)b[a]=0;for(a=20002;20032>=a;a++)b[a]=0;for(a=20062;20092>=a;a++)b[a]=0;for(a=20135;20138>=a;a++)b[a]=0;for(a=20248;20258>=a;a++)b[a]=0;for(a=20348;20358>=a;a++)b[a]=0;for(a=20436;20440>=a;a++)b[a]=0;for(a=20822;20824>=a;a++)b[a]=0;for(a=20934;20936>=a;a++)b[a]=0;for(a=21035;21037>=a;a++)b[a]=0;for(a=21095;21097>=a;a++)b[a]=0;for(a=21148;21150>=a;a++)b[a]=0;for(a=21413;21423>=a;a++)b[a]=0;for(a=21473;21483>=a;a++)b[a]=0;for(a=21780;21782>=a;a++)b[a]=0;for(a=21891;21894>=
a;a++)b[a]=0;for(a=21896;21899>=a;a++)b[a]=0;for(a=22171;22177>=a;a++)b[a]=0;for(a=22181;22187>=a;a++)b[a]=0;for(a=22191;22197>=a;a++)b[a]=0;for(a=22234;22236>=a;a++)b[a]=0;for(a=22521;22525>=a;a++)b[a]=0;for(a=22991;22994>=a;a++)b[a]=0;for(a=23028;23038>=a;a++)b[a]=0;for(a=23830;23853>=a;a++)b[a]=0;for(a=23866;23872>=a;a++)b[a]=0;for(a=23877;23884>=a;a++)b[a]=0;for(a=23886;23894>=a;a++)b[a]=0;for(a=23946;23948>=a;a++)b[a]=0;for(a=24311;24313>=a;a++)b[a]=0;for(a=24342;24347>=a;a++)b[a]=0;for(a=24370;24374>=
a;a++)b[a]=10;for(a=24375;24381>=a;a++)b[a]=0;for(a=24718;24721>=a;a++)b[a]=0;for(a=24817;24821>=a;a++)b[a]=0;for(a=24877;24882>=a;a++)b[a]=0;for(a=24891;24893>=a;a++)b[a]=0;for(a=25391;25395>=a;a++)b[a]=0;for(a=25828;25838>=a;a++)b[a]=0;for(a=26191;26195>=a;a++)b[a]=0;for(a=26391;26393>=a;a++)b[a]=0;for(a=26701;26722>=a;a++)b[a]=0;for(a=26729;26799>=a;a++)b[a]=2;for(a=26801;26803>=a;a++)b[a]=2;for(a=26811;26813>=a;a++)b[a]=2;for(a=26847;26870>=a;a++)b[a]=2;for(a=26891;26899>=a;a++)b[a]=0;for(a=26901;26923>=
a;a++)b[a]=0;for(a=26929;26946>=a;a++)b[a]=0;for(a=26948;26998>=a;a++)b[a]=0;for(a=27037;27040>=a;a++)b[a]=0;for(a=27205;27232>=a;a++)b[a]=0;for(a=27258;27260>=a;a++)b[a]=0;for(a=27391;27398>=a;a++)b[a]=0;for(a=27561;27564>=a;a++)b[a]=0;for(a=27571;27574>=a;a++)b[a]=0;for(a=27581;27584>=a;a++)b[a]=0;for(a=27591;27594>=a;a++)b[a]=0;for(a=28191;28193>=a;a++)b[a]=0;for(a=28348;28358>=a;a++)b[a]=0;for(a=28402;28432>=a;a++)b[a]=0;for(a=28462;28492>=a;a++)b[a]=0;for(a=29118;29122>=a;a++)b[a]=0;for(a=29168;29172>=
a;a++)b[a]=0;for(a=29177;29185>=a;a++)b[a]=0;for(a=29187;29195>=a;a++)b[a]=0;for(a=29900;29903>=a;a++)b[a]=0;for(a=30161;30179>=a;a++)b[a]=0;for(a=30491;30494>=a;a++)b[a]=0;for(a=30729;30732>=a;a++)b[a]=0;for(a=31251;31259>=a;a++)b[a]=0;for(a=31265;31268>=a;a++)b[a]=0;for(a=31275;31279>=a;a++)b[a]=0;for(a=31281;31297>=a;a++)b[a]=0;for(a=31461;31469>=a;a++)b[a]=0;for(a=31491;31495>=a;a++)b[a]=0;for(a=31917;31922>=a;a++)b[a]=0;for(a=31965;32E3>=a;a++)b[a]=0;for(a=32001;32003>=a;a++)b[a]=2;for(a=32005;32031>=
a;a++)b[a]=2;for(a=32033;32060>=a;a++)b[a]=2;for(a=32064;32067>=a;a++)b[a]=2;for(a=32074;32077>=a;a++)b[a]=2;for(a=32081;32086>=a;a++)b[a]=0;for(a=32107;32130>=a;a++)b[a]=0;for(a=32133;32158>=a;a++)b[a]=0;for(a=32164;32167>=a;a++)b[a]=2;for(a=32180;32199>=a;a++)b[a]=0;for(a=32201;32260>=a;a++)b[a]=0;for(a=32301;32360>=a;a++)b[a]=0;for(a=32601;32662>=a;a++)b[a]=0;for(a=32664;32667>=a;a++)b[a]=2;for(a=32701;32761>=a;a++)b[a]=0;for(a=53001;53004>=a;a++)b[a]=0;for(a=53008;53019>=a;a++)b[a]=0;for(a=53021;53032>=
a;a++)b[a]=0;for(a=53042;53046>=a;a++)b[a]=0;for(a=53074;53080>=a;a++)b[a]=0;for(a=54001;54004>=a;a++)b[a]=0;for(a=54008;54019>=a;a++)b[a]=0;for(a=54021;54032>=a;a++)b[a]=0;for(a=54042;54046>=a;a++)b[a]=0;for(a=54048;54053>=a;a++)b[a]=0;for(a=54074;54080>=a;a++)b[a]=0;for(a=102001;102040>=a;a++)b[a]=0;for(a=102042;102063>=a;a++)b[a]=0;for(a=102065;102067>=a;a++)b[a]=0;for(a=102070;102117>=a;a++)b[a]=0;for(a=102122;102216>=a;a++)b[a]=0;for(a=102221;102377>=a;a++)b[a]=0;for(a=102382;102388>=a;a++)b[a]=
0;for(a=102389;102398>=a;a++)b[a]=2;for(a=102399;102444>=a;a++)b[a]=0;for(a=102445;102447>=a;a++)b[a]=2;for(a=102448;102458>=a;a++)b[a]=0;for(a=102459;102468>=a;a++)b[a]=2;for(a=102469;102496>=a;a++)b[a]=0;for(a=102500;102519>=a;a++)b[a]=1;for(a=102520;102524>=a;a++)b[a]=0;for(a=102525;102529>=a;a++)b[a]=2;for(a=102530;102568>=a;a++)b[a]=0;for(a=102570;102588>=a;a++)b[a]=0;for(a=102590;102598>=a;a++)b[a]=0;for(a=102601;102603>=a;a++)b[a]=0;for(a=102605;102628>=a;a++)b[a]=0;for(a=102629;102646>=a;a++)b[a]=
2;for(a=102648;102700>=a;a++)b[a]=2;for(a=102701;102703>=a;a++)b[a]=0;for(a=102707;102730>=a;a++)b[a]=2;for(a=102733;102758>=a;a++)b[a]=2;for(a=102767;102900>=a;a++)b[a]=0;for(a=102965;102969>=a;a++)b[a]=0;for(a=102971;102973>=a;a++)b[a]=0;for(a=102975;102989>=a;a++)b[a]=0;for(a=102990;102992>=a;a++)b[a]=1;for(a=102997;103002>=a;a++)b[a]=0;for(a=103003;103008>=a;a++)b[a]=2;for(a=103009;103011>=a;a++)b[a]=0;for(a=103012;103014>=a;a++)b[a]=2;for(a=103019;103021>=a;a++)b[a]=0;for(a=103022;103024>=a;a++)b[a]=
2;for(a=103029;103031>=a;a++)b[a]=0;for(a=103032;103034>=a;a++)b[a]=2;for(a=103065;103068>=a;a++)b[a]=0;for(a=103074;103076>=a;a++)b[a]=0;for(a=103077;103079>=a;a++)b[a]=1;for(a=103080;103082>=a;a++)b[a]=0;for(a=103083;103085>=a;a++)b[a]=2;for(a=103090;103093>=a;a++)b[a]=0;for(a=103097;103099>=a;a++)b[a]=0;for(a=103100;103102>=a;a++)b[a]=2;for(a=103107;103109>=a;a++)b[a]=0;for(a=103110;103112>=a;a++)b[a]=2;for(a=103113;103116>=a;a++)b[a]=0;for(a=103117;103120>=a;a++)b[a]=2;for(a=103153;103157>=a;a++)b[a]=
0;for(a=103158;103162>=a;a++)b[a]=2;for(a=103163;103165>=a;a++)b[a]=0;for(a=103166;103168>=a;a++)b[a]=1;for(a=103169;103171>=a;a++)b[a]=2;for(a=103186;103188>=a;a++)b[a]=0;for(a=103189;103191>=a;a++)b[a]=2;for(a=103192;103195>=a;a++)b[a]=0;for(a=103196;103199>=a;a++)b[a]=2;for(a=103200;103224>=a;a++)b[a]=0;for(a=103225;103227>=a;a++)b[a]=1;for(a=103232;103237>=a;a++)b[a]=0;for(a=103238;103243>=a;a++)b[a]=2;for(a=103244;103246>=a;a++)b[a]=0;for(a=103247;103249>=a;a++)b[a]=2;for(a=103254;103256>=a;a++)b[a]=
0;for(a=103257;103259>=a;a++)b[a]=2;for(a=103264;103266>=a;a++)b[a]=0;for(a=103267;103269>=a;a++)b[a]=2;for(a=103300;103375>=a;a++)b[a]=0;for(a=103381;103383>=a;a++)b[a]=0;for(a=103384;103386>=a;a++)b[a]=1;for(a=103387;103389>=a;a++)b[a]=0;for(a=103390;103392>=a;a++)b[a]=2;for(a=103397;103399>=a;a++)b[a]=0;for(a=103400;103471>=a;a++)b[a]=2;for(a=103476;103478>=a;a++)b[a]=0;for(a=103479;103481>=a;a++)b[a]=2;for(a=103486;103488>=a;a++)b[a]=0;for(a=103489;103491>=a;a++)b[a]=2;for(a=103492;103495>=a;a++)b[a]=
0;for(a=103496;103499>=a;a++)b[a]=2;for(a=103528;103543>=a;a++)b[a]=0;for(a=103544;103548>=a;a++)b[a]=2;for(a=103549;103551>=a;a++)b[a]=0;for(a=103552;103554>=a;a++)b[a]=1;for(a=103555;103557>=a;a++)b[a]=2;for(a=103558;103560>=a;a++)b[a]=0;for(a=103571;103573>=a;a++)b[a]=0;for(a=103574;103576>=a;a++)b[a]=2;for(a=103577;103580>=a;a++)b[a]=0;for(a=103581;103583>=a;a++)b[a]=2;for(a=103600;103694>=a;a++)b[a]=0;for(a=103700;103793>=a;a++)b[a]=2;for(a=103794;103871>=a;a++)b[a]=0;for(a=103900;103971>=a;a++)b[a]=
2;return b});