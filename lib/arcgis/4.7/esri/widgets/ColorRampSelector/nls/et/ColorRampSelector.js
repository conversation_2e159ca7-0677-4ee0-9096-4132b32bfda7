// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define({colorRamps:{none:"Puudub",blackToWhite_predefined:"Mustast valgeni",yellowToRed_predefined:"Kollasest punaseni",slope_predefined:"Kalle",aspect_predefined:"Aspekt",errors_predefined:"Vead",heatmap1_predefined:"M\u00f5juala kaart #1",elevation1_predefined:"K\u00f5rgus #1",elevation2_predefined:"K\u00f5rgus #2",blueBright_predefined:"Eresinine",blueLightToDark_predefined:"Helesinisest tumesiniseni",blueGreenBright_predefined:"Eresinakasroheline",blueGreenLightToDark_predefined:"Helesinakasrohelisest tumeda sinakasroheliseni",
brownLightToDark_predefined:"Helepruunist tumepruunini",brownToBlueGreenDivergingBright_predefined:"Pruunist sinakasroheliseni hajuv, ere",brownToBlueGreenDivergingDark_predefined:"Pruunist sinakasroheliseni hajuv, tume",coefficientBias_predefined:"Koefitsendi nihe",coldToHotDiverging_predefined:"K\u00fclmadest toonidest soojade toonideni hajuv",conditionNumber_predefined:"Tingimuse arv",cyanToPurple_predefined:"Ts\u00fcaansinisest lillani",cyanLightToBlueDark_predefined:"Heledast ts\u00fcaansinisest tumesiniseni",
distance_predefined:"Vahemaa",grayLightToDark_predefined:"Helehallist tumehallini",greenBright_predefined:"Ereroheline",greenLightToDark_predefined:"Helerohelisest tumerohelisenii",greenToBlue_predefined:"Rohelisest siniseni",orangeBright_predefined:"Erkoran\u017e",orangeLightToDark_predefined:"Heleoran\u017eist tumeoran\u017eini",partialSpectrum_predefined:"Osaline spekter",partialSpectrum1Diverging_predefined:"Osaline spekter 1, lahknev",partialSpectrum2Diverging_predefined:"Osaline spekter 2, lahknev",
pinkToYellowGreenDivergingBright_predefined:"Roosast kollakasroheliseni hajuv, ere",pinkToYellowGreenDivergingDark_predefined:"Roosast kollakasroheliseni hajuv, tume",precipitation_predefined:"Sadestumine",prediction_predefined:"Ennustus",purpleBright_predefined:"Erklilla",purpleToGreenDivergingBright_predefined:"Lillast roheliseni hajuv, ere",purpleToGreenDivergingDark_predefined:"Lillast roheliseni hajuv, tume",purpleBlueBright_predefined:"Erk lillakassinine",purpleBlueLightToDark_predefined:"Heledast lillakassinisest tumeda lillakassiniseni",
purpleRedBright_predefined:"Ere purpurpunane",purpleRedLightToDark_predefined:"Heledast purpurpunasest tumeda purpurpunaseni",redBright_predefined:"Erepunane",redLightToDark_predefined:"Helepunasest tumepunaseni",redToBlueDivergingBright_predefined:"Punasest siniseni hajuv, ere",redToBlueDivergingDark_predefined:"Punasest siniseni hajuv, tume",redToGreen_predefined:"Punasest roheliseni",redToGreenDivergingBright_predefined:"Punasest roheliseni hajuv, ere",redToGreenDivergingDark_predefined:"Punasest roheliseni hajuv, tume",
spectrumFullBright_predefined:"Ere t\u00e4isspekter",spectrumFullDark_predefined:"Tume t\u00e4isspekter",spectrumFullLight_predefined:"Hele t\u00e4isspekter",surface_predefined:"Pind",temperature_predefined:"Temperatuur",whiteToBlack_predefined:"Valgest mustani",yellowToDarkRed_predefined:"Kollasest tumepunaseni",yellowToGreenToDarkBlue_predefined:"Kollasest roheliseni ja tumesiniseni",yellowGreenBright_predefined:"Ere kollakasroheline",yellowGreenLightToDark_predefined:"Hele kollakasrohelisest tumeda kollakasroheliseni"}});