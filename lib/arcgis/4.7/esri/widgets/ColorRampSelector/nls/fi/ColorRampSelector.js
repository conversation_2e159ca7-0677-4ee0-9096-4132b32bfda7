// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define({colorRamps:{none:"Ei mit\u00e4\u00e4n",blackToWhite_predefined:"Mustasta valkoiseen",yellowToRed_predefined:"Keltaisesta punaiseen",slope_predefined:"Kaltevuus",aspect_predefined:"Ulottuvuus",errors_predefined:"Virheet",heatmap1_predefined:"Heat map -kartta 1",elevation1_predefined:"Korkeus #1",elevation2_predefined:"Korkeussuunta nro 2",blueBright_predefined:"Kirkkaansininen",blueLightToDark_predefined:"Sininen vaaleasta tummaan",blueGreenBright_predefined:"Kirkkaansinisenvihre\u00e4",blueGreenLightToDark_predefined:"Sinivihre\u00e4 vaaleasta tummaan",
brownLightToDark_predefined:"Ruskea vaaleasta tummaan",brownToBlueGreenDivergingBright_predefined:"Ruskeasta sinisenvihre\u00e4\u00e4n hajaantuen, kirkas",brownToBlueGreenDivergingDark_predefined:"Ruskeasta sinisenvihre\u00e4\u00e4n hajaantuen, tumma",coefficientBias_predefined:"Kertoimen esiasetukset",coldToHotDiverging_predefined:"Kylm\u00e4st\u00e4 kuumaan hajaantuen",conditionNumber_predefined:"Ehtoluku",cyanToPurple_predefined:"Syaanista purppuraan",cyanLightToBlueDark_predefined:"Vaaleansyaanista tummansiniseen",
distance_predefined:"Et\u00e4isyys",grayLightToDark_predefined:"Harmaa vaaleasta tummaan",greenBright_predefined:"Kirkkaanvihre\u00e4",greenLightToDark_predefined:"Vihre\u00e4 vaaleasta tummaan",greenToBlue_predefined:"Vihre\u00e4st\u00e4 siniseen",orangeBright_predefined:"Kirkkaanoranssi",orangeLightToDark_predefined:"Oranssi vaaleasta tummaan",partialSpectrum_predefined:"Osittainen spektri",partialSpectrum1Diverging_predefined:"Osittainen spektri 1 hajaantuen",partialSpectrum2Diverging_predefined:"Osittainen spektri 2 hajaantuen",
pinkToYellowGreenDivergingBright_predefined:"Pinkist\u00e4 keltaisenvihre\u00e4\u00e4n hajaantuen, kirkas",pinkToYellowGreenDivergingDark_predefined:"Pinkist\u00e4 keltaisenvihre\u00e4\u00e4n hajaantuen, tumma",precipitation_predefined:"Sadem\u00e4\u00e4r\u00e4",prediction_predefined:"Ennuste",purpleBright_predefined:"Kirkkaanpurppura",purpleToGreenDivergingBright_predefined:"Purppurasta vihre\u00e4\u00e4n hajaantuen, kirkas",purpleToGreenDivergingDark_predefined:"Purppurasta vihre\u00e4\u00e4n hajaantuen, tumma",
purpleBlueBright_predefined:"Kirkkaanpurppuransininen",purpleBlueLightToDark_predefined:"Purppuran sininen vaaleasta tummaan",purpleRedBright_predefined:"Kirkkaanpurppuranpunainen",purpleRedLightToDark_predefined:"Purppuranpunainen vaaleasta tummaan",redBright_predefined:"Kirkkaanpunainen",redLightToDark_predefined:"Punainen vaaleasta tummaan",redToBlueDivergingBright_predefined:"Punaisesta siniseen hajaantuen, kirkas",redToBlueDivergingDark_predefined:"Punaisesta siniseen hajaantuen, tumma",redToGreen_predefined:"Punaisesta vihre\u00e4\u00e4n",
redToGreenDivergingBright_predefined:"Punaisesta vihre\u00e4\u00e4n hajaantuen, kirkas",redToGreenDivergingDark_predefined:"Punaisesta vihre\u00e4\u00e4n hajaantuen, tumma",spectrumFullBright_predefined:"Spektri - t\u00e4ysin kirkas",spectrumFullDark_predefined:"Spektri \u2013 t\u00e4ysin tumma",spectrumFullLight_predefined:"Spektri \u2013 t\u00e4ysin vaalea",surface_predefined:"Pinta",temperature_predefined:"L\u00e4mp\u00f6tila",whiteToBlack_predefined:"Valkoisesta mustaan",yellowToDarkRed_predefined:"Keltaisesta tummanpunaiseen",
yellowToGreenToDarkBlue_predefined:"Keltaisesta vihre\u00e4\u00e4n, vihre\u00e4st\u00e4 tummansiniseen",yellowGreenBright_predefined:"Kirkkaankeltaisenvihre\u00e4",yellowGreenLightToDark_predefined:"Keltavihre\u00e4 vaaleasta tummaan"}});