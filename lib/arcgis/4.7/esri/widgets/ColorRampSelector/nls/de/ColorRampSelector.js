// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define({colorRamps:{none:"Kein",blackToWhite_predefined:"<PERSON><PERSON><PERSON> zu <PERSON>\u00df",yellowToRed_predefined:"Gelb zu Rot",slope_predefined:"Neigung",aspect_predefined:"Ausrichtung",errors_predefined:"<PERSON><PERSON>",heatmap1_predefined:"Heatmap #1",elevation1_predefined:"H\u00f6he #1",elevation2_predefined:"H\u00f6he #2",blueBright_predefined:"Hellblau",blueLightToDark_predefined:"Blau, hell zu dunkel",blueGreenBright_predefined:"Blau-Gr\u00fcn, hell",blueGreenLightToDark_predefined:"Blau-Gr\u00fcn, hell zu dunkel",
brownLightToDark_predefined:"<PERSON>, hell zu dunkel",brownToBlueGreenDivergingBright_predefined:"\u00dc<PERSON><PERSON> von <PERSON> zu <PERSON>lau<PERSON>r\u00fcn, hell",brownToBlueGreenDivergingDark_predefined:"\u00dcbergang von Braun zu Blaugr\u00fcn, dunkel",coefficientBias_predefined:"Koeffizientenverzerrung",coldToHotDiverging_predefined:"\u00dcbergang von Cold zu Hot",conditionNumber_predefined:"Condition Number",cyanToPurple_predefined:"Cyan zu Violett",cyanLightToBlueDark_predefined:"Cyan, hell, zu Blau, dunkel",distance_predefined:"Entfernung",
grayLightToDark_predefined:"Grau, hell zu dunkel",greenBright_predefined:"Gr\u00fcn, hell",greenLightToDark_predefined:"Gr\u00fcn, hell zu dunkel",greenToBlue_predefined:"Gr\u00fcn zu Blau",orangeBright_predefined:"Orange, hell",orangeLightToDark_predefined:"Orange, hell zu dunkel",partialSpectrum_predefined:"Teilspektrum",partialSpectrum1Diverging_predefined:"Teilspektrum 1, \u00dcbergang",partialSpectrum2Diverging_predefined:"Teilspektrum 2, \u00dcbergang",pinkToYellowGreenDivergingBright_predefined:"\u00dcbergang von Rosa zu YellowGreen, hell",
pinkToYellowGreenDivergingDark_predefined:"\u00dcbergang von Rosa zu YellowGreen, dunkel",precipitation_predefined:"Niederschlag",prediction_predefined:"Prognose",purpleBright_predefined:"Violett, hell",purpleToGreenDivergingBright_predefined:"\u00dcbergang von Violett zu Gr\u00fcn, hell",purpleToGreenDivergingDark_predefined:"\u00dcbergang von Violett zu Gr\u00fcn, dunkel",purpleBlueBright_predefined:"Violett-Blau, hell",purpleBlueLightToDark_predefined:"Violett-Blau, hell zu dunkel",purpleRedBright_predefined:"Violett-Rot, hell",
purpleRedLightToDark_predefined:"Violett-Rot, hell zu dunkel",redBright_predefined:"Hellrot",redLightToDark_predefined:"Rot, hell zu dunkel",redToBlueDivergingBright_predefined:"\u00dcbergang von Rot zu Blau, hell",redToBlueDivergingDark_predefined:"\u00dcbergang von Rot zu Blau, dunkel",redToGreen_predefined:"Rot zu Gr\u00fcn",redToGreenDivergingBright_predefined:"\u00dcbergang von Rot zu Gr\u00fcn, hell",redToGreenDivergingDark_predefined:"\u00dcbergang von Rot zu Gr\u00fcn, dunkel",spectrumFullBright_predefined:"Spektrum f\u00fcr vollkommene Helligkeit",
spectrumFullDark_predefined:"Vollspektrum, dunkel",spectrumFullLight_predefined:"Vollspektrum, hell",surface_predefined:"Fl\u00e4che",temperature_predefined:"Temperatur",whiteToBlack_predefined:"Wei\u00df zu Schwarz",yellowToDarkRed_predefined:"Gelb zu Dunkelrot",yellowToGreenToDarkBlue_predefined:"Gelb zu Gr\u00fcn zu Dunkelblau",yellowGreenBright_predefined:"Gelb-Gr\u00fcn, hell",yellowGreenLightToDark_predefined:"Gelb-Gr\u00fcn, hell zu dunkel"}});