// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define({widgetLabel:"Juhised",advancedOptions:"T\u00e4iendavad v\u00f5imalused",disclaimer:"Kasutamise\u00a0osas kehtivad {esriTerms}.",esriTerms:"Esri litsentsitingimused",travelMode:"S\u00f5idure\u017eiim",viewActive:"Peatus lisatakse kaardi kl\u00f5psamisel",showStop:"<PERSON>va see peatus",addStopTitle:"Lisa uus\u00a0peatus",addStop:"Lisa\u00a0peatus",removeStop:"Eemalda peatus",reverseStops:"P\u00f6\u00f6ra peatuste j\u00e4rjekord \u00fcmber",dndHandle:"Lohistage peatumisj\u00e4rjestuse muutmiseks",
serviceTime:"Teenindusaeg",serviceDistance:"Teenindusvahemaa",leaveNow:"Lahku kohe",departBy:"Lahkumisaeg",departureTime:"V\u00e4ljumisaeg",zoomToRoute:"Suumi marsruudile",printDescription:"Prindi juhised",gmt:"GMT",stop:"L\u00f5peta",stopLabelTemplate:"L\u00f5peta {number} ({label})",unlocated:"Lokeerimata",searchFieldPlaceholder:"Otsi v\u00f5i kl\u00f5psa kaardil",viewlessSearchFieldPlaceholder:"Otsi",directionsPlaceholder:"Teekond kuvatakse siin",serviceError:"Ilmnes t\u00f5rge",etaTemplate:"{time} {gmt}",
eta:"Eeldatav saabumisaeg",distanceTemplate:"{distance} {units}",cumulativeCosts:"Kumulatiivsed kulud",intermediateCosts:"Vahepealsed\u00a0kulud",optimizeRoute:"Marsruudi optimeerimine",optimizingRoute:"Marsruudi optimeerimine",time:{min:"minutit",hr:"tundi"},units:{kilometers:{name:"kilomeetrit",abbr:"km"},meters:{name:"meetrit",abbr:"m"},miles:{name:"miili",abbr:"mi"},feet:{name:"jalga",abbr:"jl"},yards:{name:"jardi",abbr:"jr"},nauticalMiles:{name:"meremiili",abbr:"nm"}},traffic:{average:"reeglina",
none:"Liiklusteave puudub",heavy:"Tihe liiklus",light:"H\u00f5re liiklus"},messages:{errors:{notEnoughStops:"Teekonna\u00a0leidmise \u00fclesande lahendamiseks\u00a0on vaja v\u00e4hemalt kahte peatust."}}});