// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define({widgetLabel:"\u039f\u03b4\u03b7\u03b3\u03af\u03b5\u03c2",advancedOptions:"\u0395\u03c0\u03b9\u03bb\u03bf\u03b3\u03ad\u03c2 \u03b3\u03b9\u03b1 \u03c0\u03c1\u03bf\u03c7\u03c9\u03c1\u03b7\u03bc\u03ad\u03bd\u03bf\u03c5\u03c2",disclaimer:"\u03a7\u03c1\u03ae\u03c3\u03b7 \u03b8\u03ad\u03bc\u03b1\u03c4\u03bf\u03c2 \u03b3\u03b9\u03b1 {esriTerms}.",esriTerms:"\u038c\u03c1\u03bf\u03b9 \u03c7\u03c1\u03ae\u03c3\u03b7\u03c2 \u03c4\u03b7\u03c2 Esri",travelMode:"Travel mode (\u03a4\u03c1\u03cc\u03c0\u03bf\u03c2 \u03bc\u03b5\u03c4\u03ac\u03b2\u03b1\u03c3\u03b7\u03c2)",
viewActive:"\u03a4\u03bf \u03ba\u03bb\u03b9\u03ba \u03c3\u03c4\u03bf \u03c7\u03ac\u03c1\u03c4\u03b7 \u03c0\u03c1\u03bf\u03c3\u03b8\u03ad\u03c4\u03b5\u03b9 \u03bc\u03b9\u03b1 \u03c3\u03c4\u03ac\u03c3\u03b7",showStop:"\u0395\u03bc\u03c6\u03ac\u03bd\u03b9\u03c3\u03b7 \u03b1\u03c5\u03c4\u03ae\u03c2 \u03c4\u03b7\u03c2 \u03c3\u03c4\u03ac\u03c3\u03b7\u03c2",addStopTitle:"\u03a0\u03c1\u03bf\u03c3\u03b8\u03ae\u03ba\u03b7 \u03bd\u03ad\u03b1\u03c2 \u03c3\u03c4\u03ac\u03c3\u03b7\u03c2",addStop:"\u03a0\u03c1\u03bf\u03c3\u03b8\u03ae\u03ba\u03b7 \u03c3\u03c4\u03ac\u03c3\u03b7\u03c2",
removeStop:"\u039a\u03b1\u03c4\u03ac\u03c1\u03b3\u03b7\u03c3\u03b7 \u03c3\u03c4\u03ac\u03c3\u03b7\u03c2",reverseStops:"\u0391\u03bd\u03b1\u03c3\u03c4\u03c1\u03bf\u03c6\u03ae \u03c3\u03c4\u03ac\u03c3\u03b5\u03c9\u03bd",dndHandle:"\u03a3\u03cd\u03c1\u03b5\u03c4\u03b5 \u03b3\u03b9\u03b1 \u03c4\u03b7\u03bd \u03b1\u03bb\u03bb\u03b1\u03b3\u03ae \u03c4\u03b7\u03c2 \u03c3\u03b5\u03b9\u03c1\u03ac\u03c2 \u03b5\u03c0\u03af\u03c3\u03ba\u03b5\u03c8\u03b7\u03c2",serviceTime:"\u03a7\u03c1\u03cc\u03bd\u03bf\u03c2 \u03c5\u03c0\u03b7\u03c1\u03b5\u03c3\u03af\u03b1\u03c2",
serviceDistance:"\u0391\u03c0\u03cc\u03c3\u03c4\u03b1\u03c3\u03b7 \u03c5\u03c0\u03b7\u03c1\u03b5\u03c3\u03af\u03b1\u03c2",leaveNow:"\u0388\u03be\u03bf\u03b4\u03bf\u03c2 \u03c4\u03ce\u03c1\u03b1",departBy:"\u0391\u03bd\u03b1\u03c7\u03ce\u03c1\u03b7\u03c3\u03b7 \u03b1\u03c0\u03cc",departureTime:"\u038f\u03c1\u03b1 \u03b1\u03bd\u03b1\u03c7\u03ce\u03c1\u03b7\u03c3\u03b7\u03c2",zoomToRoute:"\u0395\u03c3\u03c4\u03af\u03b1\u03c3\u03b7 \u03c3\u03c4\u03b7 \u03b4\u03b9\u03b1\u03b4\u03c1\u03bf\u03bc\u03ae",
printDescription:"\u0395\u03ba\u03c4\u03cd\u03c0\u03c9\u03c3\u03b7 \u03bf\u03b4\u03b7\u03b3\u03b9\u03ce\u03bd",gmt:"GMT",stop:"\u0394\u03b9\u03b1\u03ba\u03bf\u03c0\u03ae",stopLabelTemplate:"\u0394\u03b9\u03b1\u03ba\u03bf\u03c0\u03ae {number} ({label})",unlocated:"\u0394\u03b5\u03bd \u03ad\u03c7\u03b5\u03b9 \u03ba\u03b1\u03b8\u03bf\u03c1\u03b9\u03c3\u03c4\u03b5\u03af \u03c4\u03bf\u03c0\u03bf\u03b8\u03b5\u03c3\u03af\u03b1.",searchFieldPlaceholder:"\u0391\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7 \u03ae \u03ba\u03bb\u03b9\u03ba \u03c3\u03c4\u03bf\u03bd \u03c7\u03ac\u03c1\u03c4\u03b7",
viewlessSearchFieldPlaceholder:"\u0391\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7",directionsPlaceholder:"\u0397 \u03b4\u03b9\u03b1\u03b4\u03c1\u03bf\u03bc\u03ae \u03c3\u03b1\u03c2 \u03b8\u03b1 \u03b5\u03bc\u03c6\u03b1\u03bd\u03b9\u03c3\u03c4\u03b5\u03af \u03b5\u03b4\u03ce",serviceError:"\u03a6\u03b1\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03cc\u03c4\u03b9 \u03ad\u03c7\u03b5\u03b9 \u03c0\u03c1\u03bf\u03ba\u03cd\u03c8\u03b5\u03b9 \u03ba\u03ac\u03c0\u03bf\u03b9\u03bf \u03c3\u03c6\u03ac\u03bb\u03bc\u03b1",
etaTemplate:"{time} {gmt}",eta:"\u0395\u03ba\u03c4\u03b9\u03bc\u03ce\u03bc\u03b5\u03bd\u03b7 \u03ce\u03c1\u03b1 \u03ac\u03c6\u03b9\u03be\u03b7\u03c2",distanceTemplate:"{distance} {units}",cumulativeCosts:"\u0391\u03b8\u03c1\u03bf\u03b9\u03c3\u03c4\u03b9\u03ba\u03cc \u03ba\u03cc\u03c3\u03c4\u03bf\u03c2",intermediateCosts:"\u0395\u03bd\u03b4\u03b9\u03ac\u03bc\u03b5\u03c3\u03bf \u03ba\u03cc\u03c3\u03c4\u03bf\u03c2",optimizeRoute:"\u0392\u03b5\u03bb\u03c4\u03b9\u03c3\u03c4\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 \u03b4\u03b9\u03b1\u03b4\u03c1\u03bf\u03bc\u03ae\u03c2",
optimizingRoute:"\u0392\u03b5\u03bb\u03c4\u03b9\u03c3\u03c4\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 \u03b4\u03b9\u03b1\u03b4\u03c1\u03bf\u03bc\u03ae\u03c2",time:{min:"\u03b5\u03bb\u03ac\u03c7.",hr:"\u03ce\u03c1\u03b1"},units:{kilometers:{name:"\u03c7\u03b9\u03bb\u03b9\u03cc\u03bc\u03b5\u03c4\u03c1\u03bf(\u03b1)",abbr:"\u03c7\u03bb\u03bc."},meters:{name:"\u03bc\u03ad\u03c4\u03c1\u03bf(\u03b1)",abbr:"\u03bc."},miles:{name:"\u03bc\u03af\u03bb\u03b9(\u03b1)",abbr:"\u03bc\u03af\u03bb."},feet:{name:"\u03c0\u03cc\u03b4\u03b9\u03b1",
abbr:"\u03c0\u03cc\u03b4."},yards:{name:"\u03b3\u03c5\u03ac\u03c1\u03b4\u03b1(\u03b5\u03c2)",abbr:"\u03b3\u03c1\u03b4."},nauticalMiles:{name:"\u03bd\u03b1\u03c5\u03c4\u03b9\u03ba\u03cc \u03bc\u03af\u03bb\u03b9(\u03b1)",abbr:"\u03bd.\u03bc."}},traffic:{average:"\u03c4\u03c5\u03c0\u03b9\u03ba\u03ac",none:"\u0394\u03b5\u03bd \u03c5\u03c0\u03ac\u03c1\u03c7\u03bf\u03c5\u03bd \u03c0\u03bb\u03b7\u03c1\u03bf\u03c6\u03bf\u03c1\u03af\u03b5\u03c2 \u03ba\u03af\u03bd\u03b7\u03c3\u03b7\u03c2",heavy:"\u039c\u03b5\u03b3\u03ac\u03bb\u03b7 \u03ba\u03af\u03bd\u03b7\u03c3\u03b7",
light:"\u039b\u03af\u03b3\u03b7 \u03ba\u03af\u03bd\u03b7\u03c3\u03b7"},messages:{errors:{notEnoughStops:"\u03a7\u03c1\u03b5\u03b9\u03ac\u03b6\u03bf\u03bd\u03c4\u03b1\u03b9 \u03c4\u03bf\u03c5\u03bb\u03ac\u03c7\u03b9\u03c3\u03c4\u03bf\u03bd \u03b4\u03cd\u03bf \u03c3\u03c4\u03ac\u03c3\u03b5\u03b9\u03c2 \u03b3\u03b9\u03b1 \u03c4\u03b7\u03bd \u03b5\u03c0\u03af\u03bb\u03c5\u03c3\u03b7 \u03c4\u03c9\u03bd \u03c0\u03c1\u03bf\u03b2\u03bb\u03b7\u03bc\u03ac\u03c4\u03c9\u03bd \u03bc\u03b9\u03b1\u03c2 \u03b4\u03b9\u03b1\u03b4\u03c1\u03bf\u03bc\u03ae\u03c2."}}});