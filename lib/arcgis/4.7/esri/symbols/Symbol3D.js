// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define("require exports ../core/tsSupport/declareExtendsHelper ../core/tsSupport/decorateHelper ../core/Collection ../core/collectionUtils ../core/Logger ../core/urlUtils ../core/Warning ../core/accessorSupport/decorators ../portal/Portal ./ExtrudeSymbol3DLayer ./FillSymbol3DLayer ./IconSymbol3DLayer ./LineSymbol3DLayer ./ObjectSymbol3DLayer ./PathSymbol3DLayer ./Symbol ./Symbol3DLayer ./TextSymbol3DLayer ./support/StyleOrigin ./support/Thumbnail".split(" "),function(G,H,t,e,n,p,u,f,h,c,v,w,x,y,z,
A,B,C,k,D,l,E){var m={icon:y,object:A,line:z,path:B,fill:x,extrude:w,text:D},F=n.ofType({base:k,key:"type",typeMap:m}),q=u.getLogger("esri.symbols.Symbol3D");return function(r){function b(a){a=r.call(this)||this;a.styleOrigin=null;a.thumbnail=null;a.type=null;var b=a.__accessor__&&a.__accessor__.metadatas&&a.__accessor__.metadatas.symbolLayers;a._set("symbolLayers",new (b&&b.type||n));return a}t(b,r);Object.defineProperty(b.prototype,"color",{get:function(){return null},set:function(a){q.error("Symbol3D does not support colors on the symbol level. Colors may be set on individual symbol layer materials instead.")},
enumerable:!0,configurable:!0});Object.defineProperty(b.prototype,"symbolLayers",{set:function(a){p.referenceSetter(a,this._get("symbolLayers"))},enumerable:!0,configurable:!0});b.prototype.readSymbolLayers=function(a,b,d){b=[];for(var g=0;g<a.length;g++){var c=a[g],e=k.typeJSONDictionary.read(c.type),f=m[e];f?(c=new f,c.read(a[g],d),b.push(c)):(q.warn("Unknown symbol layer type: "+e),d&&d.messages&&d.messages.push(new h("symbol-layer:unsupported","Symbol layers of type '"+(e||"unknown")+"' are not supported",
{definition:c,context:d})))}return b};b.prototype.readStyleOrigin=function(a,b,d){if(a.styleUrl&&a.name)return b=f.read(a.styleUrl,d),new l({styleUrl:b,name:a.name});if(a.styleName&&a.name)return new l({portal:d&&d.portal||v.getDefault(),styleName:a.styleName,name:a.name});d&&d.messages&&d.messages.push(new h("symbol3d:incomplete-style-origin","Style origin requires either a 'styleUrl' or 'styleName' and a 'name' property",{context:d,definition:a}))};b.prototype.writeStyleOrigin=function(a,b,d,c){a.styleUrl&&
a.name?(d=f.write(a.styleUrl,c),f.isAbsolute(d)&&(d=f.normalize(d)),b.styleOrigin={styleUrl:d,name:a.name}):a.styleName&&a.name&&(a.portal&&c&&c.portal&&!f.hasSamePortal(a.portal.restUrl,c.portal.restUrl)?c&&c.messages&&c.messages.push(new h("symbol:cross-portal","The symbol style origin cannot be persisted because it refers to an item on a different portal than the one being saved to.",{symbol:this})):b.styleOrigin={styleName:a.styleName,name:a.name})};b.prototype.normalizeCtorArgs=function(a){return a instanceof
k||a&&m[a.type]?{symbolLayers:[a]}:Array.isArray(a)?{symbolLayers:a}:a};e([c.property({json:{read:!1,write:!1}})],b.prototype,"color",null);e([c.property({type:F,nonNullable:!0,json:{write:!0}}),c.cast(p.castForReferenceSetter)],b.prototype,"symbolLayers",null);e([c.reader("symbolLayers")],b.prototype,"readSymbolLayers",null);e([c.property({type:l})],b.prototype,"styleOrigin",void 0);e([c.reader("styleOrigin")],b.prototype,"readStyleOrigin",null);e([c.writer("styleOrigin",{"styleOrigin.styleUrl":{type:String},
"styleOrigin.styleName":{type:String},"styleOrigin.name":{type:String}})],b.prototype,"writeStyleOrigin",null);e([c.property({type:E.default,json:{read:!1}})],b.prototype,"thumbnail",void 0);e([c.property({type:String,readOnly:!0,json:{read:!1}})],b.prototype,"type",void 0);return b=e([c.subclass("esri.symbols.Symbol3D")],b)}(c.declared(C))});