// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define("require exports ../../../../core/Logger ../../../../core/scheduling ./Graphics3DWebStyleSymbol ../../lib/glMatrix ../../webgl-engine/materials/HUDMaterial".split(" "),function(h,k,t,u,v,l,w){function x(a,b){switch(b.graphic.geometry.type){case "polyline":case "polygon":case "extent":case "multipoint":a.anchor="center";break;case "point":var c=b.graphics3DGraphic,d=m(c.graphics3DSymbol).symbol.symbolLayers.getItemAt(0),c=c.getCenterObjectSpace();l.vec3d.set(c,a.translation);switch(d.type){case "icon":case "text":c=
b.graphics3DGraphic;d=c._graphics[0].getScreenSize();c.isDraped()?a.hasLabelVerticalOffset||(a.anchor="center"):(c=void 0,void 0===c&&(c=y),b=b.graphics3DGraphic._graphics[0].stageObject.getGeometryRecords()[0].materials[0],b instanceof w?(b=b.getParams().anchorPos,c[0]=2*(b[0]-.5),c[1]=2*(b[1]-.5)):(c[0]=0,c[1]=0),b=c,f[0]=d[0]/2*(a.normalizedOffset[0]-b[0]),f[1]=d[1]/2*(a.normalizedOffset[1]-b[1]),a.screenOffset[0]=f[0],a.hasLabelVerticalOffset?(a.centerOffset[1]=f[1],a.centerOffsetUnits="screen"):
a.screenOffset[1]=f[1]);break;case "object":n(a,b)}break;case "mesh":n(a,b)}}function n(a,b){b=b.graphics3DGraphic._graphics[0].getBoundingBoxObjectSpace();b=[b[3]-b[0],b[4]-b[1],b[5]-b[2]];a.centerOffset[0]=1.1*Math.max(b[0],b[1])/2*a.normalizedOffset[0];var c=b[2]/2*a.normalizedOffset[1]+a.translation[2];a.translation[2]=c*(1.1-1);a.elevationOffset=c;b=l.vec3d.length(b);a.centerOffset[2]=1.1*b/2*a.normalizedOffset[2]}function m(a){return a instanceof v?a.graphics3DSymbol:a}function z(a,b){var c=
b.labelSymbol;b=b.graphics3DGraphic;var d=m(b.graphics3DSymbol).symbol;if("point-3d"===d.type&&d.supportsCallout()&&d.hasVisibleVerticalOffset()&&!b.isDraped())a.verticalOffset=p(d.verticalOffset);else if(c&&c.hasVisibleVerticalOffset()&&("point-3d"!==d.type||!d.supportsCallout()||!d.verticalOffset||b.isDraped())){a:switch(a.placement){case "above-center":b=!0;break a;default:b=!1}b?(a.verticalOffset=p(c.verticalOffset),a.anchor="bottom",a.normalizedOffset=[0,a.normalizedOffset[1],0],a.hasLabelVerticalOffset=
!0):(q.error("verticalOffset","Callouts and vertical offset on labels are currently only supported with above-center label placement (not with "+a.placement+" placement)"),a.isValid=!1)}}function p(a){return{screenLength:a.screenLength,minWorldLength:a.minWorldLength,maxWorldLength:a.maxWorldLength}}Object.defineProperty(k,"__esModule",{value:!0});var q=t.getLogger("esri.views.3d.layers.graphics.labelPlacement"),g=null;k.get=function(a){var b=a.labelClass.labelPlacement,c=e[b];c||(null!=b&&null==
g&&(q.warn("labelPlacement","'"+b+"' is not a valid label placement"),g=u.schedule(function(){g.remove();g=null})),c=e["default"]);var b=a.graphics3DGraphic._graphics[0],d=b.graphics3DSymbolLayer.getGraphicElevationContext(a.graphics3DGraphic.graphic),c={placement:c.placement,anchor:c.anchor,normalizedOffset:c.normalizedOffset,needsOffsetAdjustment:b.isDraped()?void 0:d.hasOffsetAdjustment,verticalOffset:null,screenOffset:[0,0],centerOffset:[0,0,0,-1],centerOffsetUnits:"world",translation:[0,0,0],
elevationOffset:0,hasLabelVerticalOffset:!1,isValid:!0};z(c,a);x(c,a);return c};var e={"above-center":{placement:"above-center",normalizedOffset:[0,1,0],anchor:"bottom"},"above-left":{placement:"above-left",normalizedOffset:[-1,1,0],anchor:"bottom-right"},"above-right":{placement:"above-right",normalizedOffset:[1,1,0],anchor:"bottom-left"},"below-center":{placement:"below-center",normalizedOffset:[0,-1,2],anchor:"top"},"below-left":{placement:"below-left",normalizedOffset:[-1,-1,0],anchor:"top-right"},
"below-right":{placement:"below-right",normalizedOffset:[1,-1,0],anchor:"top-left"},"center-center":{placement:"center-center",normalizedOffset:[0,0,1],anchor:"center"},"center-left":{placement:"center-left",normalizedOffset:[-1,0,0],anchor:"right"},"center-right":{placement:"center-right",normalizedOffset:[1,0,0],anchor:"left"}},r={"above-center":["default","esriServerPointLabelPlacementAboveCenter"],"above-left":["esriServerPointLabelPlacementAboveLeft"],"above-right":["esriServerPointLabelPlacementAboveRight"],
"below-center":["esriServerPointLabelPlacementBelowCenter"],"below-left":["esriServerPointLabelPlacementBelowLeft"],"below-right":["esriServerPointLabelPlacementBelowRight"],"center-center":["esriServerPointLabelPlacementCenterCenter"],"center-left":["esriServerPointLabelPlacementCenterLeft"],"center-right":["esriServerPointLabelPlacementCenterRight"]};h=function(a){var b=e[a];r[a].forEach(function(a){e[a]=b})};for(var A in r)h(A);Object.freeze&&(Object.freeze(e),Object.keys(e).forEach(function(a){Object.freeze(e[a]);
Object.freeze(e[a].normalizedOffset)}));var f=[0,0],y=[0,0]});