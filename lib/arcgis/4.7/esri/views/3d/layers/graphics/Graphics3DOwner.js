// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define(["require","exports"],function(a,b){Object.defineProperty(b,"__esModule",{value:!0});a=function(){return function(){this.featureExpressionInfoContext=this.localOriginFactory=this.layerView=this.layer=this.overlaySR=this.renderCoordsHelper=this.renderSpatialReference=this.clippingExtent=this.layerOrderDelta=this.layerOrder=this.stage=this.renderer=this.elevationProvider=this.streamDataSupplier=this.sharedResources=null;this.screenSizePerspectiveEnabled=!0}}();b.Graphics3DSymbolCreationContext=
a});