// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define(["require","exports","../../../../geometry","../../../../geometry/support/webMercatorUtils","../../lib/glMatrix"],function(y,e,p,r,t){function u(a,d){var b=a.spatialReference;b.equals(d)||(b.isWebMercator&&d.wkid===p.SpatialReference.WGS84.wkid?r.webMercatorToGeographic(a,!1,a):d.isWebMercator&&b.wkid===p.SpatialReference.WGS84.wkid&&r.geographicToWebMercator(a,!1,a))}function v(a){if(Array.isArray(a)){for(var d=0;d<a.length;d++)if(!v(a[d]))return!1;return!0}return null==a||0<=a}Object.defineProperty(e,
"__esModule",{value:!0});var w=t.vec4d,l=t.mat4d,x=[1,1,1];e.computeCentroid=function(a,d){if("extent"===a.type)return a.center;if("polygon"===a.type)return a.centroid;if("mesh"===a.type)return a.extent.center;for(var b=0,c=0,f=0,k=a.hasZ,h=0,g=0,e=a.paths;g<e.length;g++){for(var m=e[g],q=0,l=m;q<l.length;q++){var n=l[q],b=b+n[0],c=c+n[1];k&&(f+=n[2])}h+=m.length}a=new p.Point({x:b/h,y:c/h,z:k?f/h:void 0,spatialReference:a.spatialReference});d&&u(a,d);return a};e.convertToSR=u;e.enlargeExtent=function(a,
d,b){if(a){d||(d=w.create());var c=.5*a.width*(b-1);b=.5*a.height*(b-1);a.width<1E-7*a.height?c+=b/20:a.height<1E-7*a.width&&(b+=c/20);w.set4(a.xmin-c,a.ymin-b,a.xmax+c,a.ymax+b,d);return d}return null};e.updateVertexAttributeAuxpos1w=function(a,d){for(var b=0;b<a.geometries.length;++b){var c=a.geometries[b].data.vertexAttributes.auxpos1;c&&c.data[3]!==d&&(c.data[3]=d,a.geometryVertexAttrsUpdated(b))}};e.mixinColorAndOpacity=function(a,d){var b=[1,1,1,1];null!=a&&(b[0]=a[0],b[1]=a[1],b[2]=a[2]);null!==
d&&void 0!==d?b[3]=d:null!=a&&3<a.length&&(b[3]=a[3]);return b};e.overrideColor=function(a,d,b,c,f,k){void 0===k&&(k=[0,0,0,0]);for(var h=0;3>h;++h)k[h]=a&&null!=a[h]?a[h]:b&&null!=b[h]?b[h]:f[h];k[3]=null!=d?d:null!=c?c:f[3];return k};e.computeObjectScale=function(a,d,b,c){void 0===a&&(a=x);void 0===c&&(c=1);var f=Array(3);if(null==d||null==b)f[0]=1,f[1]=1,f[2]=1;else{for(var k=void 0,h=0,g=2;0<=g;g--){var e=a[g],m=void 0,l=null!=e,p=0===g&&!k&&!l,n=b[g];"symbolValue"===e||p?m=0!==n?d[g]/n:1:l&&
"proportional"!==e&&isFinite(e)&&(m=0!==n?e/n:1);null!=m&&(k=f[g]=m,h=Math.max(h,Math.abs(m)))}for(g=2;0<=g;g--)null==f[g]?f[g]=k:0===f[g]&&(f[g]=.001*h)}for(g=2;0<=g;g--)f[g]/=c;return f};e.computeSizeWithResourceSize=function(a,d){var b=d.width,c=d.depth,f=d.height;d=d.isPrimitive?10:1;if(null==b&&null==f&&null==c)return[d*a[0],d*a[1],d*a[2]];for(var b=[b,c,f],e,c=0;3>c;c++)if(f=b[c],null!=f){e=f/a[c];break}for(c=0;3>c;c++)null==b[c]&&(b[c]=a[c]*e);return b};e.validateSymbolLayerSize=function(a){null!=
a.isPrimitive&&(a=[a.width,a.depth,a.height]);return v(a)?null:"Symbol sizes may not be negative values"};e.computeObjectRotation=function(a,d,b,c){void 0===c&&(c=l.identity());a=a||0;d=d||0;b=b||0;0!==a&&l.rotateZ(c,-a/180*Math.PI,c);0!==d&&l.rotateX(c,d/180*Math.PI,c);0!==b&&l.rotateY(c,b/180*Math.PI,c);return c}});