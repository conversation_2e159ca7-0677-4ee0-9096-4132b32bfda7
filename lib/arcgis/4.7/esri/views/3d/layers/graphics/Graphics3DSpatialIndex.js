// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define("require exports dojo/Deferred ../../../../geometry/SpatialReference ../../../../geometry/support/webMercatorUtils ../../../../processors/SpatialIndex".split(" "),function(m,n,k,g,h,l){return function(){function a(){var b=this;this.spatialIndex=new l;this.spatialIndexNumPendingQueries=this.spatialIndexNumGraphics=0;this.graphicsCore=this.viewSR=this.layer=this.layerView=null;this._readyDfd=new k;var a=function(b){return b.workerClient&&b.workerClient.worker};if(a(this.spatialIndex))this._readyDfd.resolve();
else this.spatialIndex.on("start",function(){a(b.spatialIndex)&&b._readyDfd.resolve()})}a.prototype.initialize=function(b,a,c,d){this.layerView=b;this.layer=a;this.viewSR=c;this.graphicsCore=d};a.prototype.destroy=function(){this.spatialIndex&&(this.spatialIndex.destroy(),this.spatialIndex=null);this.graphicsCore=this.layerView=this.viewSR=null};a.prototype.numNodesUpdating=function(){return 0};a.prototype.isUpdating=function(){return 0<this.spatialIndexNumPendingQueries};a.prototype.hasGraphics=
function(){return 0<this.spatialIndexNumGraphics};a.prototype.intersects=function(b,a,c){var d=this;this.hasGraphics()?(this.spatialIndexNumPendingQueries++,this.spatialIndex.intersects(b,void 0,void 0,!0).then(function(b){d.spatialIndexNumPendingQueries--;c(b.results,b.results.length);d.layerView._evaluateUpdatingState()})):c([],0)};a.prototype.shouldAddToSpatialIndex=function(b,a,c){return c||a.needsElevationUpdates()};a.prototype.addGraphicsToSpatialIndex=function(b){if(this.layerView.loadedGraphics)for(var a=
this.layerView.loadedGraphics.toArray(),c=a.length,d=0;d<c;d++){var f=a[d],e=this.graphicsCore.getGraphics3DGraphicById(f.uid);e&&!e.addedToSpatialIndex&&this.shouldAddToSpatialIndex(f,e,b)&&this.addGraphicToSpatialIndex(f,e)}};a.prototype.serializeGeometry=function(b){return"mesh"===b.type?b.extent.toJSON():b.toJSON()};a.prototype.addGraphicToSpatialIndex=function(b,a){var c=b.geometry.spatialReference,d=this.viewSR,f={id:b.uid,geometry:null};if(c.equals(d))f.geometry=this.serializeGeometry(b.geometry);
else{var e=void 0;if(c.wkid===g.WGS84.wkid&&d.isWebMercator)e=h.geographicToWebMercator(b.geometry);else if(c.isWebMercator&&d.wkid===g.WGS84.wkid)e=h.webMercatorToGeographic(b.geometry);else return console.warn("Cannot convert graphic geometry to map spatial reference, elevation and scale updates are disabled"),!1;f.geometry=this.serializeGeometry(e)}this.spatialIndexNumGraphics++;this.spatialIndex.runProcess([f],this.layer.id);return a.addedToSpatialIndex=!0};a.prototype.whenLoaded=function(){return this._readyDfd.promise};
return a}()});