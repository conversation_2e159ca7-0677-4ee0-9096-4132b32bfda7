// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define("require exports ../../../../core/tsSupport/extendsHelper ../../../../geometry/Point ./Graphics3DGraphicLayer ./Graphics3DSymbolCommonCode ./Graphics3DSymbolLayer ./graphicUtils ../../../../views/3d/lib/glMatrix ../../support/projectionUtils ../../webgl-engine/Stage ../../webgl-engine/lib/Geometry ../../webgl-engine/lib/GeometryUtil ../../webgl-engine/lib/Object3D ../../webgl-engine/lib/Util ../../webgl-engine/materials/DefaultMaterial".split(" "),function(u,W,K,L,M,n,N,O,B,P,C,Q,D,R,E,S){function T(F,
f,a,b){for(var c=F.getGeometryRecords(),G=c.length,H="absolute-height"!==f.mode,k=0,h=0;h<G;h++){var p=c[h].geometry,q=[c[h].transformation[12],c[h].transformation[13],c[h].transformation[14]],m=p.getData().getVertexAttr(),l=m[U.POSITION].data,z=m.zOffset.data,m=m.mapPos.data,v=m.length/3;V(l.length/3===v*A+2,"unexpected tube geometry");var r=0,x=0;y.spatialReference=a.spatialReference;for(var w=0,d=0;d<v;d++){y.x=m[3*d];y.y=m[3*d+1];y.z=m[3*d+2];var g=n.computeElevation(a,y,f,b,H?I:null);H&&(w+=
I.terrainElevation);var t=A;0!==d&&d!==v-1||t++;for(var u=0;u<t;u++)e[0]=l[r]+q[0],e[1]=l[r+1]+q[1],e[2]=l[r+2]+q[2],b.setAltitude(g+z[x],e),l[r]=e[0]-q[0],l[r+1]=e[1]-q[1],l[r+2]=e[2]-q[2],r+=3,x+=1;p.invalidateBoundingInfo()}F.geometryVertexAttrsUpdated(h);k+=w/v}return k/G}var z=B.vec3d,J=B.mat4d,V=E.assert;u=function(e){function f(){return null!==e&&e.apply(this,arguments)||this}K(f,e);f.prototype._prepareResources=function(){if(!this._isPropertyDriven("size")){var a=O.validateSymbolLayerSize(this._getSymbolSize());
if(a){this._logWarning(a);this.reject();return}}var a=this._getStageIdHint(),b=this._getMaterialOpacityAndColor(),c=z.create(b),b=b[3],c={diffuse:c,ambient:c,opacity:b,transparent:1>b||this._isPropertyDriven("opacity"),vertexColors:this._isPropertyDriven("color")||this._isPropertyDriven("opacity")};this._material=new S(c,a+"_3dlinemat");this._context.stage.add(C.ModelContentType.MATERIAL,this._material);this.resolve()};f.prototype.destroy=function(){e.prototype.destroy.call(this);this.isFulfilled()||
this.reject();this._material&&(this._context.stage.remove(C.ModelContentType.MATERIAL,this._material.id),this._material=null)};f.prototype.createGraphics3DGraphic=function(a,b){var c=this._validateGeometry(a.geometry);if("polyline"!==c.type)return this._logWarning("unsupported geometry type for path symbol: "+c.type),null;var c="graphic"+a.uid,f=this.getGraphicElevationContext(a);return this._createAs3DShape(a,b,f,c,a.uid)};f.prototype.layerPropertyChanged=function(a,b,c){if("opacity"===a)return b=
this._getMaterialOpacity(),c=1>b||this._isPropertyDriven("opacity"),this._material.setParameterValues({opacity:b,transparent:c}),!0;if("elevationInfo"===a){this._updateElevationContext();for(var f in b){var e=b[f];if(a=c(e))e=this.getGraphicElevationContext(e.graphic),a.needsElevationUpdates=n.needsElevationUpdates3D(e.mode),a.elevationContext.set(e)}return!0}return!1};f.prototype._getPathSize=function(a){a=a.size&&this._isPropertyDriven("size")?n.getSingleSizeDriver(a.size):this._getSymbolSize();
return a/=this._context.renderCoordsHelper.unitInMeters};f.prototype._getSymbolSize=function(){return this.symbol.size||1};f.prototype._createAs3DShape=function(a,b,c,e,f){var k=a.geometry,h=k.hasZ,p=k.paths;a=[];var q=[],m=[],l=z.create(),u=this._context.renderSpatialReference===P.SphericalECEFSpatialReference,v=Array(6),r=this._getPathSize(b),k=n.getGeometryVertexData3D(p,h,k.spatialReference,this._context.renderSpatialReference,this._context.elevationProvider,this._context.renderCoordsHelper,c);
this._logGeometryCreationWarnings(k,p,"paths","PathSymbol3DLayer");if(0<p.length){for(var p=k.geometryData.outlines,h=k.eleVertexData,x=k.vertexData,w=0;w<p.length;++w){var d=p[w];if(!(1>=d.count)){var g=d.index,t=d.count;if(this._context.clippingExtent&&(n.computeBoundingBox(h,g,t,v),n.boundingBoxClipped(v,this._context.clippingExtent)))continue;n.chooseOrigin(x,g,t,l);n.subtractCoordinates(x,g,t,l);d=new Float64Array(h.buffer,3*g*h.BYTES_PER_ELEMENT,3*t);g=n.flatArrayToArrayOfArrays(x,g,t);g=D.createTubeGeometry(g,
.5*r,A,u,l);g.getVertexAttr().mapPos={size:3,data:d,offsetIdx:0,strideIdx:3};this._material.getParams().vertexColors&&(d=this._getVertexOpacityAndColor(b),g=D.addVertexColors(g,d));d=new Q(g,e+"path"+w);d.singleUse=!0;a.push(d);q.push([this._material]);d=J.identity();J.translate(d,l,d);m.push(d)}}if(0<a.length)return b=new R({geometries:a,materials:q,transformations:m,castShadow:!0,metadata:{layerUid:this._context.layer.uid,graphicId:f},idHint:e}),b=new M(this,b,a,null,null,T,c),b.alignedTerrainElevation=
k.terrainElevation,b.needsElevationUpdates=n.needsElevationUpdates3D(c.mode),b}return null};return f}(N);var U=E.VertexAttrConstants,e=z.create(),y=new L,I={verticalDistanceToGround:0,terrainElevation:0},A=10;return u});