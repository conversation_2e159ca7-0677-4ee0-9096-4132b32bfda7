// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define(["require","exports","../../../../core/urlUtils","./I3SUtil"],function(p,q,h,k){var l="version level sharedResource attributeData geometryData textureData lodSelection".split(" ");return function(){function g(a,c,b,d,f,e,g,n){var m=this;this.rootId=b;this.progressiveLoadPenalty=d;this.nodeIndex=f;this.streamDataSupplier=e;this.viewportQueries=g;this.logger=n;this._dirty=!0;this.cancelled=!1;this._loadingNodes=new Set;this._pendingNodes=1;this._nodeTraversalState={};this._version=0;this._maxLodLevel=
this.viewportQueries.maxLodLevel;this.rootUrl=h.makeAbsolute(c,a);this.traverseVisible(function(a){m.nodeTraversalState(a.id);return!0})}g.prototype.requestReload=function(){this._dirty=!0;++this._version};g.prototype.update=function(a){var c=this;if(this.cancelled||!this._dirty)return!1;var b=function(a,b){c.nodeTraversalState(b.id)};this._collectMissing(a).forEach(function(a,f){return c._loadNode(f,b)});return this._dirty=0<this._pendingNodes};g.prototype.cancel=function(){this.cancelled=!0};g.prototype.isLoading=
function(){return this._dirty};g.prototype.getNumLoading=function(){return this._loadingNodes.size};g.prototype.getNumPending=function(){return this._pendingNodes};g.prototype.nodeTraversalState=function(a){var c=this._nodeTraversalState[a];if(null!=c&&c.version===this._version)return this._nodeTraversalState[a];var b=this.nodeIndex[a];if(null==b)return null;var d=null,f=0;if(null!=b.parentNode){d=this.nodeIndex[b.parentNode.id];if(null==d)return null;d=this._nodeTraversalState[d.id];null!=d&&(f=
d.lodLevel)}var d=this.viewportQueries.hasLOD(b),e=this.viewportQueries.getLodLevel(b),f=!d||e>f;if(c)return c.lodLevel=e,c.isChosen=f,c.version=this._version,c;this._nodeTraversalState[b.id]={nodeHasLOD:d,lodLevel:e,isChosen:f,version:this._version};return this._nodeTraversalState[a]};g.prototype._loadNode=function(a,c){var b=this;this._loadingNodes.add(a.id);var d=h.makeAbsolute(a.href,a.baseUrl);this.streamDataSupplier.request(d,"json").then(function(a,e){var d={id:e.id,mbs:e.mbs,parentNode:e.parentNode,
children:e.children,featureData:e.featureData};l.forEach(function(a){d[a]=e.hasOwnProperty(a)?e[a]:null});b.nodeIndex[d.id]=d;d.baseUrl=a;c(a,d);b._loadingNodes.delete(d.id)},function(c){b.loadErrorCallback(d);b._loadingNodes.delete(a.id)})};g.prototype._collectMissing=function(a){var c=this,b=new Map;this._pendingNodes=0;this.traverseVisible(function(d,f){var e=d.id;if(c.nodeIndex[e])return!0;++c._pendingNodes;if(c._loadingNodes.has(e))return!0;k.buildTopNodeMap(b,a,{id:e,href:d.href,baseUrl:f?f.baseUrl:
""},c.entryPriority(d));return!0});return b};g.prototype.entryPriority=function(a){if(a.id===this.rootId)return 0;var c=0,b=this.nodeIndex[a.id];null!=b&&null!=b.parentNode&&(b=this._nodeTraversalState[b.parentNode.id],null!=b&&(c=b.lodLevel));a=this.viewportQueries.distToPOI(a);return-a-c*(a+this.progressiveLoadPenalty)};g.prototype.traverseVisible=function(a){var c=this.nodeIndex[this.rootId];return this._traverse({id:this.rootId,mbs:c?c.mbs:null,href:this.rootUrl},a,null)};g.prototype._traverse=
function(a,c,b){if(!c(a,b))return!1;b=this.nodeIndex[a.id];if(!b)return!0;a=this.nodeTraversalState(a.id);if(a.nodeHasLOD&&a.lodLevel===this._maxLodLevel)return!0;if(b.children){a=0;for(var d=b.children;a<d.length;a++){var f=d[a],e=this.nodeIndex[f.id];if(e&&(!e.children||0===e.children.length)){if(this.viewportQueries.isGeometryVisible(e)&&!c(f,b))return!1}else if(this.viewportQueries.isNodeVisible(f)&&!this._traverse(f,c,b))return!1}}return!0};g.prototype.loadErrorCallback=function(a){this.logger.warn("Error loading node: "+
a)};return g}()});