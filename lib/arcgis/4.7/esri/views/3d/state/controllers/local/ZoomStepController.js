// All material copyright ESRI, All Rights Reserved, unless otherwise specified.
// See https://js.arcgis.com/4.7/esri/copyright.txt for details.
//>>built
define("require exports ../../../../../core/tsSupport/extendsHelper ../../../camera/constraintUtils ../../../lib/glMatrix ../PointToPointAnimationController ../../../webgl-engine/lib/Camera ../../../../animation/easing".split(" "),function(g,h,l,m,c,n,k,p){Object.defineProperty(h,"__esModule",{value:!0});g=function(g){function b(d,a,e){a=g.call(this,d.state,a,"interaction"===e?null:void 0)||this;a.view=d;a.mode=e;a.zoomLocation=c.vec3d.create();a.tmpCamera=new k;a.tmpRayDir=c.vec3d.create();a.tmpCenter=
c.vec3d.create();a.constraintOptions={selection:15,interactionType:1,interactionFactor:null,interactionStartCamera:new k,interactionDirection:null};return a}l(b,g);Object.defineProperty(b.prototype,"isInteractive",{get:function(){return"interaction"===this.mode},enumerable:!0,configurable:!0});b.prototype.zoomStep=function(d,a){if(this.active){var e=this.view.state,f=this.constraintOptions.interactionStartCamera;this.animation.finished?f.copyFrom(e.camera):this.animation.cameraAt(1,f);this.tmpCamera.copyFrom(e.camera);
0<d?(this.pickingHelper.pickPointInScreen(a,this.zoomLocation)||this.pickingHelper.pickFreePointInScreen(a,this.zoomLocation),this.pickingHelper.pickRaySegment(this.tmpCamera.eye,this.tmpCamera.center,this.tmpCenter)&&(this.tmpCamera.center=this.tmpCenter)):this.pickingHelper.pickRaySegment(this.tmpCamera.eye,this.tmpCamera.center,this.zoomLocation)?this.tmpCamera.center=this.zoomLocation:c.vec3d.set(this.tmpCamera.center,this.zoomLocation);this.updateCamera(this.tmpCamera,Math.pow(.6,d),this.zoomLocation,
a);this.begin(this.tmpCamera)}};b.prototype.animationSettings=function(){return{apex:null,duration:.6,easing:p.outExpo}};b.prototype.updateCamera=function(d,a,e,f){c.vec3d.subtract(e,d.eye,this.tmpRayDir);f=c.vec3d.length(this.tmpRayDir);var b=f*a;1>=a&&4>b&&(b=4,a=b/f);1E-6>Math.abs(f-b)||(c.vec3d.scale(this.tmpRayDir,a),c.vec3d.subtract(e,this.tmpRayDir,d.eye),c.vec3d.lerp(d.center,e,1-a),m.applyAll(this.view,this.tmpCamera,this.constraintOptions))};return b}(n.PointToPointAnimationController);
h.ZoomStepController=g});