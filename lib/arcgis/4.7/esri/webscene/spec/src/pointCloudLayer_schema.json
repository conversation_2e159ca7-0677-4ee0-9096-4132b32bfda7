{"title": "PointCloud Layer", "type": "object", "$schema": "http://json-schema.org/draft-04/schema", "description": "Point cloud data is post-processed spatially organized lidar data that consists of large collections of 3D points. Elevations for the ground, buildings, forest canopy, highway overpasses, and anything else encountered during the lidar survey make up the point cloud data. Point cloud layers allow for fast visualisation of point cloud data in the browser.", "properties": {"id": {"type": "string", "description": "A unique identifying string for the layer"}, "itemId": {"type": "string", "description": "Optional string containing the item ID of the service if it's registered on ArcGIS Online or your organization's portal"}, "layerDefinition": {"type": "object", "description": "A layerDefinition object defining the attribute schema and drawing information for the layer.", "allOf": [{"$ref": "layerDefinition_schema.json"}, {"properties": {"drawingInfo": {}, "elevationInfo": {}, "filters": {}}, "additionalProperties": false}]}, "layerType": {"type": "string", "description": "String indicating the layer type", "enum": ["PointCloudLayer"]}, "listMode": {"type": "string", "description": "To show or hide the layer in the layer list", "enum": ["show", "hide"]}, "showLegend": {"type": "boolean", "description": "Boolean value indicating whether to display the layer in the legend. Default value is `true`.", "default": true}, "title": {"type": "string", "description": "A user-friendly string title for the layer that can be used in a table of contents. If this is not included, a title is derived from the service"}, "url": {"type": "string", "description": "The URL to the layer.", "format": "uri"}, "visibility": {"type": "boolean", "description": "Boolean property determining whether the layer is initially visible", "default": true}}, "required": ["id", "layerType", "title", "url"], "additionalProperties": false, "esriDocumentation": {"examples": [{"title": "pointCloudLayer", "description": "", "code": {"operationalLayers": [{"id": "point-cloud-layer-0", "title": "Sonoma8", "layerType": "PointCloudLayer", "url": "http://tiles.arcgis.com/tiles/V6ZHFr6zdgNZuVG0/arcgis/rest/services/BARNEGAT_BAY_LiDAR_UTM/SceneServer", "visibility": true, "layerDefinition": {"filters": [{"field": "CLASS_CODE", "type": "pointCloudValueFilter", "mode": "include", "values": [1]}]}}]}}]}}