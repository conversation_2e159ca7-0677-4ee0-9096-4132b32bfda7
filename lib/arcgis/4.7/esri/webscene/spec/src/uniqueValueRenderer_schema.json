{"title": "UniqueValue Renderer", "type": "object", "$schema": "http://json-schema.org/draft-04/schema", "description": "A unique value renderer symbolizes based on the value of an attribute.", "properties": {"authoringInfo": {"type": "object", "description": "An object containing metadata about the authoring process for creating a renderer object. This allows the authoring clients to save specific overridable settings so that next time it is accessed via the UI, their selections are remembered. Non-authoring clients can ignore it.", "$ref": "authoringInfo_schema.json"}, "backgroundFillSymbol": {"type": "object", "description": "Used for polygon features. It can only be used for bivariate types and size rendering.", "$ref": "polygonSymbol3D_schema.json"}, "defaultLabel": {"type": "string", "description": "Default label for the default symbol used to draw unspecified values."}, "defaultSymbol": {"description": "The defaultSymbol on the renderer that get assigned to features with no value or features that do not fall within the configured data.", "$ref": "symbol3D_schema.json"}, "field1": {"type": "string", "description": "Attribute field renderer uses to match values."}, "field2": {"type": "string", "description": "If needed, specify an additional attribute field the renderer uses to match values."}, "field3": {"type": "string", "description": "If needed, specify an additional attribute field the renderer uses to match values."}, "fieldDelimiter": {"type": "string", "description": "String inserted between the values if multiple attribute fields are specified.", "default": ", "}, "legendOptions": {"type": "object", "description": "A legend containing one title, which is a string describing the renderer in the legend.", "$ref": "rendererLegendOptions_schema.json"}, "type": {"type": "string", "description": "Specifies the type of renderer used.", "enum": ["uniqueValue"]}, "uniqueValueInfos": {"type": "array", "description": "An array of uniqueValueInfo objects.", "items": {"type": "object", "$ref": "uniqueValueInfo_schema.json"}}, "valueExpression": {"type": "string", "description": "An [Arcade expression](https://developers.arcgis.com/arcade/) evaluating to either a string or a number."}, "valueExpressionTitle": {"type": "string", "description": "The title identifying and describing the associated [Arcade](https://developers.arcgis.com/arcade/) expression as defined in the `valueExpression` property."}, "visualVariables": {"type": "array", "description": "An array of objects used to set rendering properties.", "items": {"type": "object", "$ref": "visualVariable_schema.json"}}}, "required": ["type", "uniqueValueInfos"], "additionalProperties": false, "esriDocumentation": {"examples": [{"title": "UniqueValue", "description": "", "code": {"renderer": {"type": "uniqueValue", "field1": "COUNTRY", "uniqueValueInfos": [{"value": "US", "label": "US", "symbol": {"type": "PointSymbol3D", "symbolLayers": [{"type": "Icon", "resource": {"primitive": "circle"}, "material": {"color": [253, 127, 111]}, "size": 12}]}}, {"value": "Canada", "label": "Canada", "symbol": {"type": "PointSymbol3D", "symbolLayers": [{"type": "Icon", "resource": {"primitive": "circle"}, "material": {"color": [126, 176, 213]}, "size": 12}]}}, {"value": "Brazil", "label": "Brazil", "symbol": {"type": "PointSymbol3D", "symbolLayers": [{"type": "Icon", "resource": {"primitive": "circle"}, "material": {"color": [178, 224, 97]}, "size": 12}]}}]}}}]}}