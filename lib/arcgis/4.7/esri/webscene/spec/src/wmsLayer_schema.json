{"title": "WMS Layer (WMS)", "type": "object", "$schema": "http://json-schema.org/draft-04/schema", "description": "A layer consuming a Web Map Service (WMS). The WMS specification is an international specification for serving and consuming dynamic maps on the web.", "properties": {"copyright": {"type": "string", "description": "A string containing copyright and access information for a WMS layer. This is copied from the capabilities document exposed by the WMS service."}, "customLayerParameters": {"type": "object", "description": "A sequence of custom parameters to WMS layer requests. These parameters are applied to `Getmap` and `GetFeatureInfo` requests. The `customLayerParameters` property takes precedence if `customParameters` is also present.", "patternProperties": {".*": {"type": "string"}}}, "customParameters": {"type": "object", "description": "A sequence of custom parameters to all WMS requests. These parameters are applied to `GetCapabilities`, `GetMap`, and `GetFeatureinfo` requests. If used with the `customLayerParameters` property, `customParameters` will not take precedence.", "patternProperties": {".*": {"type": "string"}}}, "extent": {"type": "array", "description": "The rectangular map extent that should be requested from the service.", "items": {"type": "array", "items": {"type": "number"}, "minItems": 2, "maxItems": 2}, "minItems": 2, "maxItems": 2}, "featureInfoFormat": {"type": "string", "description": "Format of the feature, e.g.`text/plain`"}, "featureInfoUrl": {"type": "string", "description": "The URL for the WMS GetFeatureInfo call."}, "format": {"type": "string", "description": "A string containing the image format to be requested from a WMS service.", "default": "png"}, "id": {"type": "string", "description": "A unique identifying string for the layer."}, "isReference": {"type": "boolean", "description": "This is applicable if used as a baseMapLayer. A boolean value indicating whether or not the baseMapLayer draws on top (true) of other layers, including operationalLayers, or below (false).", "default": false}, "itemId": {"type": "string", "description": "Unique string value indicating an item registered in ArcGIS Online or your organization's portal."}, "layerType": {"type": "string", "description": "String indicating the layer type.", "enum": ["WMS"]}, "layers": {"type": "array", "description": "An array of layer objects defining the styling, geometry, and attribute information for the features.", "items": {"type": "object", "$ref": "wmsLayer_layer_schema.json"}, "uniqueItems": true}, "listMode": {"type": "string", "description": "To show or hide layers in the layer list", "enum": ["show", "hide"]}, "mapUrl": {"type": "string", "description": "A string containing the URL of the WMS map. When using a WMS layer, you should also supply the url property. `mapUrl` is the URL returned by the capabilities to be used for the getMap requests.", "format": "uri"}, "maxHeight": {"type": "number", "description": "A number defining the maximum height, in pixels, that should be requested from the service."}, "maxScale": {"type": "number", "description": "A number representing the maximum scale at which the layer will be visible. The number is the scale's denominator.", "minimum": 0}, "maxWidth": {"type": "number", "description": "A number defining the maximum width, in pixels, that should be requested from the service."}, "minScale": {"type": "number", "description": "A number representing the minimum scale at which the layer will be visible. The number is the scale's denominator.", "minimum": 0}, "opacity": {"type": "number", "description": "The degree of transparency applied to the layer on the client side, where 0 is full transparency and 1 is no transparency.", "minimum": 0, "maximum": 1, "default": 1}, "refreshInterval": {"type": "number", "description": "Refresh interval of the layer in minutes. Non-zero value indicates automatic layer refresh at the specified interval. Value of 0 indicates auto refresh is not enabled.", "default": 0}, "showLegend": {"type": "boolean", "description": "Boolean value indicating whether to display the layer in the legend."}, "spatialReferences": {"type": "array", "description": "An array of numbers containing well-known IDs for spatial references supported by the service.", "items": {"type": "number", "description": "well-known ID"}}, "title": {"type": "string", "description": "A user-friendly string title for the layer that can be used in a table of contents."}, "url": {"type": "string", "description": "The URL to the WMS service (`getCapabilities` URL).", "format": "uri"}, "version": {"type": "string", "description": "A string containing the version number of the service."}, "visibility": {"type": "boolean", "description": "Boolean property determining whether the layer is initially visible in the web map.", "default": true}, "visibleLayers": {"type": "array", "description": "An array of layers that should appear visible. The array contains the names of the visible layers.", "items": {"type": "string"}, "uniqueItems": true}}, "required": ["layerType", "title"], "additionalProperties": false, "esriDocumentation": {"examples": [{"title": "WMS Layer (WMS)", "description": "WMS layer as an operationalLayer", "code": {"operationalLayers": [{"id": "15e0eda27c4-layer-0", "opacity": 1, "title": "IEM GOES IR WMS Service", "url": "https://mesonet.agron.iastate.edu/cgi-bin/wms/goes/conus_ir.cgi", "visibility": true, "layerType": "WMS", "featureInfoFormat": "text/plain", "featureInfoUrl": "https://mesonet.agron.iastate.edu/cgi-bin/mapserv/mapserv?map=/opt/iem/data/wms/goes/conus_ir.map", "mapUrl": "https://mesonet.agron.iastate.edu/cgi-bin/mapserv/mapserv?map=/opt/iem/data/wms/goes/conus_ir.map", "version": "1.3.0"}]}}, {"title": "WMS Layer basemap", "description": "WMS layer as a basemap layer", "code": {"baseMapLayers": [{"id": "15e14e20f82-layer-0", "opacity": 1, "title": "Open Government Data WMS Zürich", "url": "http://www.gis.stadt-zuerich.ch/maps/services/wms/WMS-ZH-STZH-OGD/MapServer/WMSServer", "visibility": true, "layerType": "WMS", "featureInfoFormat": "text/html", "featureInfoUrl": "http://www.gis.stadt-zuerich.ch/maps/services/wms/WMS-ZH-STZH-OGD/MapServer/WmsServer", "mapUrl": "http://www.gis.stadt-zuerich.ch/maps/services/wms/WMS-ZH-STZH-OGD/MapServer/WmsServer", "layers": [{"name": "Uebersichtsplan_2016", "title": "Uebersichtsplan_2016"}], "visibleLayers": ["Uebersichtsplan_2016"], "version": "1.3.0"}]}}]}}