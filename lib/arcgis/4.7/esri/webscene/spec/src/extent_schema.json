{"title": "extent", "type": "object", "$schema": "http://json-schema.org/draft-04/schema", "description": "This object defines the bounding geometry given the lower-left and upper-right corners of the bounding box. A [spatial reference](spatialReference.md) is also required.", "properties": {"spatialReference": {"type": "object", "description": "An object used to specify the spatial reference of the given geometry.", "$ref": "spatialReference_schema.json"}, "xmax": {"type": "number", "description": "A numeric value indicating the top-right X-coordinate of an extent envelope."}, "xmin": {"description": "A numeric value indicating the bottom-left X-coordinate of an extent envelope.", "oneOf": [{"type": "number"}, {"type": "null"}, {"type": "string", "enum": ["NaN"]}]}, "ymax": {"type": "number", "description": "A numeric value indicating the top-right Y-coordinate of an extent envelope."}, "ymin": {"type": "number", "description": "A numeric value indicating the bottom-left Y-coordinate of an extent envelope."}}, "additionalProperties": false, "esriDocumentation": {"examples": [{"title": "extent", "code": {"extent": {"xmin": 13871520.850500003, "ymin": 3910293.086000003, "xmax": 14574034.873400003, "ymax": 4686306.161399998, "spatialReference": {"wkid": 102100, "latestWkid": 3857}}}}]}}