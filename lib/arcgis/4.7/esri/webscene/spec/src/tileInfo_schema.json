{"title": "tileInfo", "type": "object", "$schema": "http://json-schema.org/draft-04/schema", "description": "Tile information, returned from the WMTS OGC Web Service. The tileInfo will contain the spatial reference of the layer. tileInfo is the same json representation as the ArcGIS Map/Image service tileInfo except that it may contain a levelValue on the lod objects that should be used instead of the level in the templateUrl.", "properties": {"cols": {"type": "integer", "description": "Requested tile's column."}, "compressionQuality": {"type": "number", "description": "Compression quality of the tile.", "minimum": 0, "maximum": 100}, "dpi": {"type": "number", "description": "The dpi of the tiling scheme."}, "format": {"type": "string", "description": "Image format of the cached tiles.", "enum": ["jpg", "png", "png24", "png32", "png8", "pdf", "bmp", "gif", "svg", "svgz", "emf", "ps", "mixed", "lerc"]}, "lods": {"type": "array", "description": "An array of levels of detail that define the tiling scheme.", "items": {"type": "object", "$ref": "lod_schema.json"}, "uniqueItems": true}, "origin": {"type": "object", "description": "The tiling scheme origin.", "$ref": "point_geometry_schema.json"}, "rows": {"type": "integer", "description": "Requested tile's row."}, "spatialReference": {"type": "object", "description": "The spatial reference of the tiling schema.", "$ref": "spatialReference_schema.json"}}, "additionalProperties": false}