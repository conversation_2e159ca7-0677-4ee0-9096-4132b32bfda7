{"title": "popupInfo", "type": "object", "$schema": "http://json-schema.org/draft-04/schema", "description": "Defines the look and feel of popup windows when a user clicks or queries a feature.", "properties": {"description": {"description": "A string that appears in the body of the popup window as a description. It is also possible to specify the description as HTML-formatted content.", "oneOf": [{"type": "string"}, {"type": "null"}]}, "expressionInfos": {"type": "array", "description": "List of Arcade expressions added to the pop-up.", "items": {"type": "object", "$ref": "popupExpressionInfo_schema.json"}}, "fieldInfos": {"type": "array", "description": "Array of fieldInfo information properties. This information is provided by the service layer definition.", "items": {"type": "object", "$ref": "fieldInfo_schema.json"}}, "layerOptions": {"type": "object", "description": "Additional options that can be defined for the popup layer.", "$ref": "popupLayerOptions_schema.json"}, "mediaInfos": {"type": "array", "description": "Array of various mediaInfo to display. Can be of type `image`, `piechart`, `barchart`, `columnchart`, or `linechart`. The order given is the order in which it displays.", "items": {"type": "object", "$ref": "mediaInfo_schema.json"}}, "popupElements": {"type": "array", "description": "An array of popupElement objects that represent an ordered list of popup elements.", "items": {"type": "object", "$ref": "popupElement_schema.json"}}, "relatedRecordsInfo": {"type": "object", "description": "Indicates whether to enable related records if they exist on a layer.", "$ref": "relatedRecordsInfo_schema.json"}, "showAttachments": {"type": "boolean", "description": "Indicates whether attachments will be loaded for feature layers that have attachments."}, "title": {"type": "string", "description": "A string that appears at the top of the popup window as a title."}}, "additionalProperties": false, "esriDocumentation": {"examples": [{"title": "popupInfo", "code": {"popupInfo": {"title": "Postcode status", "fieldInfos": [{"fieldName": "expression/expr1", "visible": true, "format": {"digitSeparator": true, "places": 0}}, {"fieldName": "expression/expr2", "visible": true}, {"fieldName": "ID", "label": "ID", "tooltip": "Identifier", "visible": true, "format": {"places": 0, "digitSeparator": true}, "stringFieldOption": "textbox"}, {"fieldName": "relationships/0/Status", "label": "Status", "isEditable": false, "visible": true, "stringFieldOption": "textbox"}, {"fieldName": "relationships/2/Postcode", "label": "Postcode", "isEditable": false, "visible": false, "stringFieldOption": "textbox"}], "showAttachments": true, "relatedRecordsInfo": {"showRelatedRecords": true, "orderByFields": [{"field": "relationships/0/Identifier", "order": "asc"}, {"field": "relationships/1/Status", "order": "desc"}]}, "mediaInfos": [{"title": "Chart stats", "type": "barchart", "caption": "For a better idea...", "value": {"fields": ["relationships/0/Pledge"], "tooltipField": "relationships/2/Official"}}, {"type": "barchart", "title": "{pop2000} / 2 = {expression/expr4}", "value": {"fields": ["expression/expr4", "pop2000"]}}], "popupElements": [{"type": "text"}, {"type": "fields"}, {"type": "media"}, {"type": "attachments"}], "expressionInfos": [{"name": "expr1", "title": "Population (Expr)", "expression": "$feature.pop2000", "returnType": "number"}, {"name": "expr2", "title": "Name (Abbr) (Expr)", "expression": "$feature.state_name + ' (' + $feature.state_abbr + ')'", "returnType": "string"}]}}}]}}