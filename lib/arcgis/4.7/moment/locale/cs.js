//>>built
(function(f,e){"object"===typeof exports&&"undefined"!==typeof module&&"function"===typeof require?e(require("../moment")):"function"===typeof define&&define.amd?define(["../moment"],e):e(f.moment)})(this,function(f){function e(c){return 1<c&&5>c&&1!==~~(c/10)}function a(c,b,k,d){var a=c+" ";switch(k){case "s":return b||d?"p\u00e1r sekund":"p\u00e1r sekundami";case "ss":return b||d?a+(e(c)?"sekundy":"sekund"):a+"sekundami";case "m":return b?"minuta":d?"minutu":"minutou";case "mm":return b||d?a+(e(c)?
"minuty":"minut"):a+"minutami";case "h":return b?"hodina":d?"hodinu":"hodinou";case "hh":return b||d?a+(e(c)?"hodiny":"hodin"):a+"hodinami";case "d":return b||d?"den":"dnem";case "dd":return b||d?a+(e(c)?"dny":"dn\u00ed"):a+"dny";case "M":return b||d?"m\u011bs\u00edc":"m\u011bs\u00edcem";case "MM":return b||d?a+(e(c)?"m\u011bs\u00edce":"m\u011bs\u00edc\u016f"):a+"m\u011bs\u00edci";case "y":return b||d?"rok":"rokem";case "yy":return b||d?a+(e(c)?"roky":"let"):a+"lety"}}var g="leden \u00fanor b\u0159ezen duben kv\u011bten \u010derven \u010dervenec srpen z\u00e1\u0159\u00ed \u0159\u00edjen listopad prosinec".split(" "),
h="led \u00fano b\u0159e dub kv\u011b \u010dvn \u010dvc srp z\u00e1\u0159 \u0159\u00edj lis pro".split(" ");return f.defineLocale("cs",{months:g,monthsShort:h,monthsParse:function(a,b){var c,d=[];for(c=0;12>c;c++)d[c]=new RegExp("^"+a[c]+"$|^"+b[c]+"$","i");return d}(g,h),shortMonthsParse:function(a){var b,c=[];for(b=0;12>b;b++)c[b]=new RegExp("^"+a[b]+"$","i");return c}(h),longMonthsParse:function(a){var b,c=[];for(b=0;12>b;b++)c[b]=new RegExp("^"+a[b]+"$","i");return c}(g),weekdays:"ned\u011ble pond\u011bl\u00ed \u00fater\u00fd st\u0159eda \u010dtvrtek p\u00e1tek sobota".split(" "),
weekdaysShort:"ne po \u00fat st \u010dt p\u00e1 so".split(" "),weekdaysMin:"ne po \u00fat st \u010dt p\u00e1 so".split(" "),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm",l:"D. M. YYYY"},calendar:{sameDay:"[dnes v] LT",nextDay:"[z\u00edtra v] LT",nextWeek:function(){switch(this.day()){case 0:return"[v ned\u011bli v] LT";case 1:case 2:return"[v] dddd [v] LT";case 3:return"[ve st\u0159edu v] LT";case 4:return"[ve \u010dtvrtek v] LT";
case 5:return"[v p\u00e1tek v] LT";case 6:return"[v sobotu v] LT"}},lastDay:"[v\u010dera v] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulou ned\u011bli v] LT";case 1:case 2:return"[minul\u00e9] dddd [v] LT";case 3:return"[minulou st\u0159edu v] LT";case 4:case 5:return"[minul\u00fd] dddd [v] LT";case 6:return"[minulou sobotu v] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"p\u0159ed %s",s:a,ss:a,m:a,mm:a,h:a,hh:a,d:a,dd:a,M:a,MM:a,y:a,yy:a},dayOfMonthOrdinalParse:/\d{1,2}\./,
ordinal:"%d.",week:{dow:1,doy:4}})});