//>>built
define("dojo/_base/array dojo/_base/json dojo/_base/kernel dojo/_base/lang dojo/date/stamp dojox".split(" "),function(I,t,J,B,E,w){B.getObject("json",!0,w);return w.json.ref={resolveJson:function(e,g){function m(a,F,f,b,n,h,G){var c,v,d;f=p in a?a[p]:f;if(p in a||void 0!==f&&b)f=(u+f).replace(q,"$2$3");b=h||a;if(void 0!==f){w&&(a.__id=f);!g.schemas||a instanceof Array||!(d=f.match(/^(.+\/)[^\.\[]*$/))||(n=g.schemas[d[1]]);if(k[f]&&a instanceof Array==k[f]instanceof Array)b=k[f],delete b.$ref,delete b._loadObject,
v=!0;else if(d=n&&n.prototype)x.prototype=d,b=new x;k[f]=b;y&&(y[f]=g.time)}for(;n;){if(d=n.properties)for(c in a){var z=d[c];z&&"date-time"==z.format&&"string"==typeof a[c]&&(a[c]=E.fromISOString(a[c]))}n=n["extends"]}n=a.length;for(c in a){if(c==n)break;if(a.hasOwnProperty(c)){d=a[c];if("object"==typeof d&&d&&!(d instanceof Date)&&"__parent"!=c)if(l=d[t]||A&&d[p],a==r||l&&d.__parent||(d.__parent=G?G:b),l)if(delete a[c],h=l.toString().replace(/(#)([^\.\[])/,"$1.$2").match(/(^([^\[]*\/)?[^#\.\[]*)#?([\.\[].*)?/),
k[(u+l).replace(q,"$2$3")]?l=k[(u+l).replace(q,"$2$3")]:(l="$"==h[1]||"this"==h[1]||""===h[1]?e:k[(u+h[1]).replace(q,"$2$3")])&&h[3]&&h[3].replace(/(\[([^\]]+)\])|(\.?([^\.\[]+))/g,function(a,b,c,d,f){l=l&&l[c?c.replace(/[\"\'\\]/,""):f]}),l)d=l;else{if(!F){var H;H||r.push(b);H=!0;d=m(d,!1,d[t],!0,z);d._loadObject=g.loader}}else F||(d=m(d,r==a,void 0===f?void 0:D(f,c),!1,z,b!=a&&"object"==typeof b[c]&&b[c],a));a[c]=d;if(b!=a&&!b.__isDirty&&(h=b[c],b[c]=d,!(!v||d===h||b._loadObject||"_"==c.charAt(0)&&
"_"==c.charAt(1)||"$ref"==c||d instanceof Date&&h instanceof Date&&d.getTime()==h.getTime()||"function"==typeof d&&"function"==typeof h&&d.toString()==h.toString())&&k.onUpdate))k.onUpdate(b,c,h,d)}}if(v&&(p in a||b instanceof Array))for(c in b){if(!(b.__isDirty||!b.hasOwnProperty(c)||a.hasOwnProperty(c)||"_"==c.charAt(0)&&"_"==c.charAt(1)||b instanceof Array&&isNaN(c))){if(k.onUpdate&&"_loadObject"!=c&&"_idAttr"!=c)k.onUpdate(b,c,b[c],void 0);for(delete b[c];b instanceof Array&&b.length&&void 0===
b[b.length-1];)b.length--}}else if(k.onLoad)k.onLoad(b);return b}g=g||{};var p=g.idAttribute||"id",t=this.refAttribute,A=g.idAsRef,u=g.idPrefix||"",w=g.assignAbsoluteIds,k=g.index||{},y=g.timeStamps,l,r=[],q=/^(.*\/)?(\w+:\/\/)|[^\/\.]+\/\.\.\/|^.*\/(\/)/,D=this._addProp,x=function(){};e&&"object"==typeof e&&(e=m(e,!1,g.defaultId,!0),m(r,!1));return e},fromJson:function(e,g){var m;try{m=eval("("+e+")")}catch(p){throw new SyntaxError("Invalid JSON string: "+p.message+" parsing: "+e);}return m?this.resolveJson(m,
g):m},toJson:function(e,g,m,p,C){function A(){var a=(D++).toString();return r.hasOwnProperty(a)?A():a}function u(a,e,f){if("object"==typeof a&&a){if(a instanceof Date)return'"'+E.toISOString(a,{zulu:!0})+'"';var b=a.__id;if(b){if("#"!=e&&(B&&!b.match(/#/)||l[b]))return f=b,"#"!=b.charAt(0)&&(f=a.__clientId==b?"cid:"+b:b.substring(0,m.length)==m?b.substring(m.length):b),a={},a[y]=f,t.toJson(a,g);e=b}else q?a instanceof Array||(e=A(),a.__id=e,r[e]=a):(a.__id=e,r[e]=a);l[e]=a;f=f||"";var n=g?f+t.toJsonIndentStr:
"",h=g?"\n":"",b=g?" ":"";if(a instanceof Array)return"["+I.map(a,function(a,b){a=u(a,q?void 0:k(e,b),n);"string"!=typeof a&&(a="undefined");return h+n+a}).join(","+b)+h+f+"]";var p=[];q&&"undefined"===typeof a[C]&&p.push(h+n+t._escapeString(C)+":"+b+t.toJson(a.__id));for(var c in a)if(a.hasOwnProperty(c)){var v;if("number"==typeof c)v='"'+c+'"';else if("string"!=typeof c||"_"==c.charAt(0)&&"_"==c.charAt(1))continue;else v=t._escapeString(c);var d=u(a[c],q?void 0:k(e,c),n);"string"==typeof d&&p.push(h+
n+v+":"+b+d)}return"{"+p.join(","+b)+h+f+"}"}return"function"==typeof a&&w.json.ref.serializeFunctions?a.toString():t.toJson(a)}var B=this._useRefs,k=this._addProp,y=this.refAttribute;m=m||"";var l={},r={},q=C?!0:!1,D=1;e=u(e,q?void 0:"#","");if(!p)for(var x in r)delete r[x].__id;return e},_addProp:function(e,g){return e+(e.match(/#/)?1==e.length?"":".":"#")+g},refAttribute:"$ref",_useRefs:!1,serializeFunctions:!1}});