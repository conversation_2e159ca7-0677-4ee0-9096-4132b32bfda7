.dojoxGrid {position: relative; background-color: #EBEADB; font-family: Geneva, Arial, Helvetica, sans-serif; -moz-outline-style: none; outline: none; overflow: hidden; height: 0;}.dojoxGrid table {padding: 0;}.dojoxGrid td {-moz-outline: none;}.dojoxGridMasterHeader {position: relative;}.dojoxGridMasterView {position: relative;}.dojoxGridMasterMessages {position: relative; padding: 1em; text-align: center; background-color: white;}.dojoxGridView {position: absolute; overflow: hidden;}.dojoxGridHeader {position: absolute; overflow: hidden; cursor: default;}.dojoxGridHeader {background-color: #E8E1CF;}.dojoxGridHeader table {text-align: center;}.dojoxGridHeader .dojoxGridCell {border: 1px solid; border-color: #F6F4EB #ACA899 #ACA899 #F6F4EB; background: url("images/grid_dx_gradient.gif") #E8E1CF top repeat-x; padding-bottom: 2px;}.dojoxGridHeader .dojoxGridCellOver {background-image: none; background-color: white; border-bottom-color: #FEBE47; margin-bottom: 0; padding-bottom: 0; border-bottom-width: 3px;}.dojoxGridHeader .dojoxGridCellFocus {border: 1px dashed blue;}.dojoxGridHeader.dojoxGridCellFocus.dojoxGridCellOver {background-image: none; background-color: white; border-bottom-color: #FEBE47; margin-bottom: 0; padding-bottom: 0; border-bottom-width: 3px;}.dojoxGridArrowButtonNode {display: none; padding-left: 16px;}.dojoxGridArrowButtonChar {display:inline;} .dojoxGridArrowButtonNode:hover {cursor: default;}.dojoxGridArrowButtonChar:hover {cursor: default;}.dojoxGridSortUp:hover {cursor: default;}.dojoxGridSortDown:hover {cursor: default;}.dijit_a11y .dojoxGridArrowButtonChar {display:inline !important;}.dojoxGridScrollbox {position: relative; overflow: auto; background-color: white; width: 100%;}.dojoxGridContent {position: relative; overflow: hidden; -moz-outline-style: none; outline: none;}.dojoxGridRowbar {border: 1px solid; border-color: #F6F4EB #ACA899 #ACA899 #F6F4EB; border-top: none; background: url("images/grid_dx_gradient.gif") #E8E1CF top repeat-x;}.dojoxGridRowbarInner {border-top: 1px solid #F6F4EB;}.dojoxGridRowbarOver {background-image: none; background-color: white; border-top-color: #FEBE47; border-bottom-color: #FEBE47;}.dojoxGridRowbarSelected {background-color: #D9E8F9;}.dojoxGridRow {position: relative; width: 9000em;}.dojoxGridRow {border: 1px solid #E8E4D8; border-color: #F8F7F1; border-left: none; border-right: none; background-color: white; border-top: none;}.dojoxGridRowOver {border-top-color: #FEBE47; border-bottom-color: #FEBE47;}.dojoxGridRowOdd {background-color: #FFFDF3;}.dojoxGridRowSelected {background-color: #D9E8F9;}.dojoxGridRowTable {table-layout: fixed; width: 0; empty-cells: show;}.dj_ie .dojoxGridRowTable {border-collapse: collapse;}.dojoxGridInvisible {visibility: hidden;} .Xdojo-ie .dojoxGridInvisible {display: none;} .dojoxGridInvisible td, .dojoxGridHeader .dojoxGridInvisible td {border-top-width: 0; border-bottom-width: 0; padding-top: 0; padding-bottom: 0; height: 0; overflow: hidden;}.dojoxGrid .dojoxGridCell {border: 1px solid; border-color: #EBEADB; border-right-color: #D5CDB5; padding: 3px 3px 3px 3px; text-align: left; overflow: hidden; word-wrap: break-word;}.dojoxGrid .dojoxGridFixedRowHeight .dojoxGridCell {white-space: nowrap; word-break: keep-all; word-wrap: normal; text-overflow: ellipsis;}.dojoxGridCellFocus {border: 1px dashed blue;}.dojoxGridCellOver {border: 1px dotted #FEBE47;}.dojoxGridCellFocus.dojoxGridCellOver {border: 1px dashed green;}.dojoxGridRowEditing td {background-color: #F4FFF4;}.dojoxGridRow-inserting td {background-color: #F4FFF4;}.dojoxGridRow-inflight td {background-color: #F2F7B7;}.dojoxGridRow-error td {background-color: #F8B8B6;}.dojoxGridInput, .dojoxGridSelect, .dojoxGridTextarea {margin: 0; padding: 0; border-style: none; width: 100%; font-size: 100%; font-family: inherit;}.dojoxGridHiddenFocus {position: absolute; top: -1000px; height: 0; width: 0;}.dijit_a11y .dojoxGridRowbarSelected {border-top: 1px solid white; border-bottom: 1px dashed black; border-top: 0; background: none;}.dijit_a11y .dojoxGridRowbarSelected .dojoxGridRowbarInner {border: 0; border-top: 1px solid white;}.dijit_a11y .dojoxGridRowSelected {border: 1px solid black !important;}.dojoxGridRowTable .dojoDndHorizontal th.dojoDndItem {display: table-cell; margin: 0;}.dojoxGridDndAvatar {font-size: 100%;}.dojoxGrid .dojoDndItemBefore {border-left-color: red;}.dojoxGrid .dojoDndItemAfter {border-right-color: red;}.dijit_a11y .dojoDndItemBefore {border-left: double;}.dijit_a11y .dojoDndItemAfter {border-right: double;}.dojoxGridDndAvatarItem td {border: 1px solid; border-color: #F6F4EB #ACA899 #ACA899 #F6F4EB; background: url("images/grid_dx_gradient.gif") #E8E1CF top repeat-x; padding: 0pt; margin: 0pt;}.dojoxGridDndAvatarItem td.dojoxGridDndAvatarItemImage {border: 0; border-color: #F6F4EB #ACA899 #ACA899 #F6F4EB; background-color: transparent; padding: 3px; padding-bottom: 2px; margin: 0;}.dojoDndMove .dojoxGridDndAvatarItem .dojoxGridDndAvatarItemImage {background-image: url("../../../dojo/resources/images/dndNoMove.png"); background-repeat: no-repeat; background-position: center center;}.dojoDndCopy .dojoxGridDndAvatarItem .dojoxGridDndAvatarItemImage {background-image: url("../../../dojo/resources/images/dndNoCopy.png"); background-repeat: no-repeat; background-position: center center;}.dojoDndMove .dojoDndAvatarCanDrop .dojoxGridDndAvatarItem .dojoxGridDndAvatarItemImage {background-image: url("../../../dojo/resources/images/dndMove.png"); background-repeat: no-repeat; background-position: center center;}.dojoDndCopy .dojoDndAvatarCanDrop .dojoxGridDndAvatarItem .dojoxGridDndAvatarItemImage {background-image: url("../../../dojo/resources/images/dndCopy.png"); background-repeat: no-repeat; background-position: center center;}.dojoxGridColPlaceBottom {background: transparent url("images/grid_sort_up.gif") no-repeat scroll left top;}.dojoxGridColPlaceTop {background: transparent url("images/grid_sort_down.gif") no-repeat scroll left top;}.dojoxGridColPlaceTop, .dojoxGridColPlaceBottom {font-size:1px; height:6px; z-index:10000; top:0; overflow:hidden; position:absolute; line-height:1px; width:8px;}.dojoxGridResizeColLine {width: 1px; background-color: #777; position: absolute; cursor: col-resize; z-index:10000;}.dojoxGridColNoResize, .dojoxGridColNoResize .dojoDndItemOver {cursor: not-allowed !important;}.dojoxGridColResize, .dojoxGridColResize .dojoDndItemOver,.dojoxGridColumnResizing,.dojoxGridColumnResizing .dojoDndItemOver,.dojoxGridColumnResizing .dojoxGridHeader {cursor: col-resize !important;}.dojoxGridColPlaceBottom {background: transparent url("images/grid_sort_up.gif") no-repeat scroll left top;}.dojoxGridColPlaceTop {background: transparent url("images/grid_sort_down.gif") no-repeat scroll left top;}.dojoxGridColPlaceTop, .dojoxGridColPlaceBottom {font-size:1px; height:6px; z-index:10000; top:0; overflow:hidden; position:absolute; line-height:1px; width:8px;}.dojoxGridResizeColLine {width: 1px; background-color: #777; position: absolute;}.dojoxGridExpandoCell {vertical-align: middle;}.dojoxGridSummarySpan {visibility: hidden;}.dojoxGridSummaryRow .dojoxGridSummarySpan,.dojoxGridRowCollapsed .dojoxGridSummarySpan {visibility: visible;}.dojoxGridNoChildren .dojoxGridExpando {visibility: hidden !important; width: 0px !important;}.dj_ie .dojoxGridRtl .dojoxGridHeader table {float:none;}.dojoxGridRtl .dojoxGridCell {text-align:right;}.dj_ie8 .dojoxGridRtl .dojoxGridCell {border-left: none;}.dj_ie .dojoxGridRtl .dojoxGridMasterView .dojoxGridRowTable {border-left: #e5dac8 1px solid}.dojoxGridRtl .dojoxGridArrowButtonNode {float:left;}.nihilo .dojoxGrid {background-color: #e9e9e9; font-size: 0.85em;}.nihilo .dojoxGridMasterMessages {background-color: #fefefe;}.nihilo .dojoxGridHeader {background-color: #e9e9e9;}.nihilo .dojoxGridHeader .dojoxGridCell {border-width: 1px; padding-bottom: 0px; border-color: transparent #ACA899 #919191 transparent; background: url(../../../dijit/themes/nihilo/images/titleBar.png) #e9e9e9 repeat-x top; color: #000 !important;}.nihilo .dojoxGridHeader .dojoxGridCellOver {background: url(../../../dijit/themes/nihilo/images/titleBarActive.png) #e9e9e9 repeat-x top;}.nihilo .dojoxGridHeader .dojoxGridCellFocus {border-color: #ACA899 #919191; border-style: dashed;}.nihilo .dojoxGridArrowButtonChar {float: right; display: none;}.nihilo .dojoxGridArrowButtonNode {display: block !important; padding-left: 0px; float: right; background:url("../../../dijit/themes/nihilo/images/spriteArrows.png") left center no-repeat; width: 11px; height: 1em; margin-top: 1px;}.dj_ie6 .nihilo .dojoxGridArrowButtonNode {background-image:url("../../../dijit/themes/nihilo/images/spriteArrows.gif"); margin-left: 0px;}.nihilo .dojoxGridSortUp .dojoxGridArrowButtonNode {background-position: -21px;}.dijit_a11y .nihilo .dojoxGridArrowButtonNode {display: none !important;}.nihilo .dojoxGridScrollbox {background-color: #fefefe;}.nihilo .dojoxGridRowbar {border: none; background: url(../../../dijit/themes/nihilo/images/titleBar.png) #e9e9e9 repeat-y right; border-right: 1px solid #ccc; padding: 0px;}.nihilo .dojoxGridRowbarInner {border: none; border-bottom: 1px solid #ccc;}.nihilo .dojoxGridRowbarOver {background: url(../../../dijit/themes/nihilo/images/titleBarActive.png) #e9e9e9 repeat-y right;}.nihilo .dojoxGridRowbarSelected {background: url(../../../dijit/themes/nihilo/images/titleBar.png) #D9E8F9 no-repeat center; border-right: 1px solid #ccc;}.nihilo .dojoxGridRow {border: none; background-color: white;}.nihilo .dojoxGridRowOver {border-top-color: #ccc; border-bottom-color: #ccc;}.nihilo .dojoxGridRowOver .dojoxGridCell {background-color: #ffe284;}.nihilo .dojoxGridRowOdd {background-color: #f2f5f9;}.nihilo .dojoxGridRowSelected {background-color: #aec7e3;}.dijit_a11y .nihilo .dojoxGridRowSelected {background-color: #aec7e3; border-style: solid;} .nihilo .dojoxGridCell {border: 1px dotted #D5CDB5; border-left-color: transparent; border-top-color: transparent;}.dj_ff2 .nihilo .dojoxGridCell {border-left-width: 0px; border-top-width: 0px;}.dj_ie6 .nihilo .dojoxGridCell {border: 1px solid white; border-right: 1px solid #D5CDB5;}.nihilo .dojoxGridCellFocus {border: 1px dashed darkblue;}.nihilo .dojoxGridCellOver {border: 1px dotted #a6a6a6;}.nihilo .dojoxGridCellFocus.dojoxGridCellOver {border: 1px dashed darkblue;}.nihilo .dojoxGridRowEditing td {background-color: #ffe284;}.nihilo .dojoxGridRow-inserting td {background-color: #F4FFF4;}.nihilo .dojoxGridRow-inflight td {background-color: #F2F7B7;}.nihilo .dojoxGridRow-error td {background-color: #F8B8B6;}.nihilo .dojoxGrid .dojoDndItemBefore {border-left-color: #ffe284;}.nihilo .dojoxGrid .dojoDndItemAfter {border-right-color: #ffe284;}.nihilo .dojoxGridExpando {float: left; height: 18px; width: 18px; text-align: center; margin-top: -3px;}.dijitRtl .nihilo .dojoxGridExpando {float: right;}.nihilo .dojoxGridExpandoCell {padding-top: 5px;}.nihilo .dojoxGridExpandoNode {height: 18px; background-image: url('../../../dijit/themes/nihilo/images/spriteTree.gif');}.nihilo .dojoxGridExpandoOpened .dojoxGridExpandoNode {background: url('../../../dijit/themes/nihilo/images/spriteTree.gif') no-repeat -18px top;}.nihilo .dojoxGridExpandoLoading .dojoxGridExpandoNode {background: url('../../../dijit/themes/nihilo/images/treeExpand_loading.gif');}.nihilo .dojoxGridTreeModel .dojoxGridNoChildren .dojoxGridExpando {visibility: visible !important; width: 18px !important;}.nihilo .dojoxGridTreeModel .dojoxGridNoChildren .dojoxGridExpandoNode {background: url('../../../dijit/themes/nihilo/images/spriteTree.gif') no-repeat -36px top;}.nihilo .dojoxGridExpandoNodeInner {visibility: hidden;}.dijit_a11y .dojoxGridExpandoNodeInner {visibility: visible;} .nihilo .dojoxGridSummaryRow .dojoxGridCell {border-top-color: #999; border-top-style: solid;}.nihilo .dojoxGridSpacerCell,.nihilo .dojoxGridExpandoCell,.nihilo .dojoxGridSummaryRow .dojoxGridSpacerCell {border-color: transparent; border-right-color: #D5CDB5;}.nihilo .dojoxGridSummaryRow .dojoxGridTotalCell,.nihilo .dojoxGridRowCollapsed .dojoxGridExpandoCell,.nihilo .dojoxGridTreeModel .dojoxGridExpandoCell {border-bottom-color: #D5CDB5;}.nihilo .dojoxGridSubRowAlt {background-color: #F8F8F8;}.nihilo .dojoxGridRowOdd .dojoxGridSubRowAlt {background-color: #EDEFF3;}.nihilo .dojoxGridRowTable .dojoDndHorizontal th.dojoDndItem {padding: 3px;}