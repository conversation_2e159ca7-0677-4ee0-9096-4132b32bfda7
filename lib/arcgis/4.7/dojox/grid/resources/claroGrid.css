.dojoxGrid {position: relative; background-color: #EBEADB; font-family: Geneva, Arial, Helvetica, sans-serif; -moz-outline-style: none; outline: none; overflow: hidden; height: 0;}.dojoxGrid table {padding: 0;}.dojoxGrid td {-moz-outline: none;}.dojoxGridMasterHeader {position: relative;}.dojoxGridMasterView {position: relative;}.dojoxGridMasterMessages {position: relative; padding: 1em; text-align: center; background-color: white;}.dojoxGridView {position: absolute; overflow: hidden;}.dojoxGridHeader {position: absolute; overflow: hidden; cursor: default;}.dojoxGridHeader {background-color: #E8E1CF;}.dojoxGridHeader table {text-align: center;}.dojoxGridHeader .dojoxGridCell {border: 1px solid; border-color: #F6F4EB #ACA899 #ACA899 #F6F4EB; background: url("images/grid_dx_gradient.gif") #E8E1CF top repeat-x; padding-bottom: 2px;}.dojoxGridHeader .dojoxGridCellOver {background-image: none; background-color: white; border-bottom-color: #FEBE47; margin-bottom: 0; padding-bottom: 0; border-bottom-width: 3px;}.dojoxGridHeader .dojoxGridCellFocus {border: 1px dashed blue;}.dojoxGridHeader.dojoxGridCellFocus.dojoxGridCellOver {background-image: none; background-color: white; border-bottom-color: #FEBE47; margin-bottom: 0; padding-bottom: 0; border-bottom-width: 3px;}.dojoxGridArrowButtonNode {display: none; padding-left: 16px;}.dojoxGridArrowButtonChar {display:inline;} .dojoxGridArrowButtonNode:hover {cursor: default;}.dojoxGridArrowButtonChar:hover {cursor: default;}.dojoxGridSortUp:hover {cursor: default;}.dojoxGridSortDown:hover {cursor: default;}.dijit_a11y .dojoxGridArrowButtonChar {display:inline !important;}.dojoxGridScrollbox {position: relative; overflow: auto; background-color: white; width: 100%;}.dojoxGridContent {position: relative; overflow: hidden; -moz-outline-style: none; outline: none;}.dojoxGridRowbar {border: 1px solid; border-color: #F6F4EB #ACA899 #ACA899 #F6F4EB; border-top: none; background: url("images/grid_dx_gradient.gif") #E8E1CF top repeat-x;}.dojoxGridRowbarInner {border-top: 1px solid #F6F4EB;}.dojoxGridRowbarOver {background-image: none; background-color: white; border-top-color: #FEBE47; border-bottom-color: #FEBE47;}.dojoxGridRowbarSelected {background-color: #D9E8F9;}.dojoxGridRow {position: relative; width: 9000em;}.dojoxGridRow {border: 1px solid #E8E4D8; border-color: #F8F7F1; border-left: none; border-right: none; background-color: white; border-top: none;}.dojoxGridRowOver {border-top-color: #FEBE47; border-bottom-color: #FEBE47;}.dojoxGridRowOdd {background-color: #FFFDF3;}.dojoxGridRowSelected {background-color: #D9E8F9;}.dojoxGridRowTable {table-layout: fixed; width: 0; empty-cells: show;}.dj_ie .dojoxGridRowTable {border-collapse: collapse;}.dojoxGridInvisible {visibility: hidden;} .Xdojo-ie .dojoxGridInvisible {display: none;} .dojoxGridInvisible td, .dojoxGridHeader .dojoxGridInvisible td {border-top-width: 0; border-bottom-width: 0; padding-top: 0; padding-bottom: 0; height: 0; overflow: hidden;}.dojoxGrid .dojoxGridCell {border: 1px solid; border-color: #EBEADB; border-right-color: #D5CDB5; padding: 3px 3px 3px 3px; text-align: left; overflow: hidden; word-wrap: break-word;}.dojoxGrid .dojoxGridFixedRowHeight .dojoxGridCell {white-space: nowrap; word-break: keep-all; word-wrap: normal; text-overflow: ellipsis;}.dojoxGridCellFocus {border: 1px dashed blue;}.dojoxGridCellOver {border: 1px dotted #FEBE47;}.dojoxGridCellFocus.dojoxGridCellOver {border: 1px dashed green;}.dojoxGridRowEditing td {background-color: #F4FFF4;}.dojoxGridRow-inserting td {background-color: #F4FFF4;}.dojoxGridRow-inflight td {background-color: #F2F7B7;}.dojoxGridRow-error td {background-color: #F8B8B6;}.dojoxGridInput, .dojoxGridSelect, .dojoxGridTextarea {margin: 0; padding: 0; border-style: none; width: 100%; font-size: 100%; font-family: inherit;}.dojoxGridHiddenFocus {position: absolute; top: -1000px; height: 0; width: 0;}.dijit_a11y .dojoxGridRowbarSelected {border-top: 1px solid white; border-bottom: 1px dashed black; border-top: 0; background: none;}.dijit_a11y .dojoxGridRowbarSelected .dojoxGridRowbarInner {border: 0; border-top: 1px solid white;}.dijit_a11y .dojoxGridRowSelected {border: 1px solid black !important;}.dojoxGridRowTable .dojoDndHorizontal th.dojoDndItem {display: table-cell; margin: 0;}.dojoxGridDndAvatar {font-size: 100%;}.dojoxGrid .dojoDndItemBefore {border-left-color: red;}.dojoxGrid .dojoDndItemAfter {border-right-color: red;}.dijit_a11y .dojoDndItemBefore {border-left: double;}.dijit_a11y .dojoDndItemAfter {border-right: double;}.dojoxGridDndAvatarItem td {border: 1px solid; border-color: #F6F4EB #ACA899 #ACA899 #F6F4EB; background: url("images/grid_dx_gradient.gif") #E8E1CF top repeat-x; padding: 0pt; margin: 0pt;}.dojoxGridDndAvatarItem td.dojoxGridDndAvatarItemImage {border: 0; border-color: #F6F4EB #ACA899 #ACA899 #F6F4EB; background-color: transparent; padding: 3px; padding-bottom: 2px; margin: 0;}.dojoDndMove .dojoxGridDndAvatarItem .dojoxGridDndAvatarItemImage {background-image: url("../../../dojo/resources/images/dndNoMove.png"); background-repeat: no-repeat; background-position: center center;}.dojoDndCopy .dojoxGridDndAvatarItem .dojoxGridDndAvatarItemImage {background-image: url("../../../dojo/resources/images/dndNoCopy.png"); background-repeat: no-repeat; background-position: center center;}.dojoDndMove .dojoDndAvatarCanDrop .dojoxGridDndAvatarItem .dojoxGridDndAvatarItemImage {background-image: url("../../../dojo/resources/images/dndMove.png"); background-repeat: no-repeat; background-position: center center;}.dojoDndCopy .dojoDndAvatarCanDrop .dojoxGridDndAvatarItem .dojoxGridDndAvatarItemImage {background-image: url("../../../dojo/resources/images/dndCopy.png"); background-repeat: no-repeat; background-position: center center;}.dojoxGridColPlaceBottom {background: transparent url("images/grid_sort_up.gif") no-repeat scroll left top;}.dojoxGridColPlaceTop {background: transparent url("images/grid_sort_down.gif") no-repeat scroll left top;}.dojoxGridColPlaceTop, .dojoxGridColPlaceBottom {font-size:1px; height:6px; z-index:10000; top:0; overflow:hidden; position:absolute; line-height:1px; width:8px;}.dojoxGridResizeColLine {width: 1px; background-color: #777; position: absolute; cursor: col-resize; z-index:10000;}.dojoxGridColNoResize, .dojoxGridColNoResize .dojoDndItemOver {cursor: not-allowed !important;}.dojoxGridColResize, .dojoxGridColResize .dojoDndItemOver,.dojoxGridColumnResizing,.dojoxGridColumnResizing .dojoDndItemOver,.dojoxGridColumnResizing .dojoxGridHeader {cursor: col-resize !important;}.dojoxGridColPlaceBottom {background: transparent url("images/grid_sort_up.gif") no-repeat scroll left top;}.dojoxGridColPlaceTop {background: transparent url("images/grid_sort_down.gif") no-repeat scroll left top;}.dojoxGridColPlaceTop, .dojoxGridColPlaceBottom {font-size:1px; height:6px; z-index:10000; top:0; overflow:hidden; position:absolute; line-height:1px; width:8px;}.dojoxGridResizeColLine {width: 1px; background-color: #777; position: absolute;}.dojoxGridExpandoCell {vertical-align: middle;}.dojoxGridSummarySpan {visibility: hidden;}.dojoxGridSummaryRow .dojoxGridSummarySpan,.dojoxGridRowCollapsed .dojoxGridSummarySpan {visibility: visible;}.dojoxGridNoChildren .dojoxGridExpando {visibility: hidden !important; width: 0px !important;}.dj_ie .dojoxGridRtl .dojoxGridHeader table {float:none;}.dojoxGridRtl .dojoxGridCell {text-align:right;}.dj_ie8 .dojoxGridRtl .dojoxGridCell {border-left: none;}.dj_ie .dojoxGridRtl .dojoxGridMasterView .dojoxGridRowTable {border-left: #e5dac8 1px solid}.dojoxGridRtl .dojoxGridArrowButtonNode {float:left;}.claro .dojoxGrid {margin:0px; padding:0px; border-collapse:collapse; background-color: #fff; border: 1px solid #DBDBDB;}.claro .dojoxGridMasterMessages {background-color: #fefefe;}.claro .dojoxGridLoading,.claro .dojoxGridError {background-position:left center; background-repeat: no-repeat; padding-left:25px;}.claro .dojoxGridLoading {background-image:url("../../../dijit/icons/images/loadingAnimation.gif");}.claro .dojoxGridError {background-image: url('../../../dijit/icons/images/commonIconsObjActEnabled.png'); background-position: -496px; width: 16px; height: 16px;}.claro .dojoxGridHeader {background: transparent; margin-left: -2px;}.claro .dojoxGridHeader .dojoxGridCell {padding: 2px 5px; vertical-align: top; background: transparent; border-style:solid; border-width:1px; border-color: #FFFFFF #BCBCBC #BCBCBC #FFFFFF;}.dj_ie6 .claro .dojoxGridHeader .dojoxGridCell {border-color:#BCBCBC #BCBCBC #BCBCBC #e5edf4;}.claro .dojoxGridHeader .dojoxGridCellOver {background: #9dcfff;}.claro .dojoxGridSortNode {text-decoration:none; display:block; white-space: normal; background: none; border: none; padding: 0;}.claro .dojoxGridCellOver .dojoxGridSortNode {background-color:#9dcfff;}.claro .dojoxGridArrowButtonChar {display:none; float:right;}.claro .dojoxGridArrowButtonNode {background:transparent url("../../../dijit/themes/claro/images/spriteArrows.png") no-repeat scroll left center; display:block; float:right; height:1em; margin:2px 4px 0 5px; padding-left:0; width:7px;}.claro .dojoxGridSortUp .dojoxGridArrowButtonNode {background-position:-21px 50%;}.claro .dojoxGridMasterHeader {background: url("images/header.png") #EDF2F7 repeat-x bottom; background: -moz-linear-gradient(top, #EDF2F7, #D0DFEA); background: -webkit-gradient(linear, left top, left bottom, from(#EDF2F7), to(#D0DFEA)); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#FFEDF2F7, endColorstr=#FFD0DFEA); border: 1px solid #FFFFFF; border-right: none;}.claro .dojoxGridMasterHeader .dojoxGridRowTable {border-left: 1px solid #BCBCBC; border-right: 1px solid #FFFFFF; background-color: transparent;}.dj_ie .claro .dojoxGridHeader .dojoxGridRowTable {border-collapse:separate;}.dj_ie6 .claro .dojoxGridHeader .dojoxGridRowTable,.dj_ie7 .claro .dojoxGridHeader .dojoxGridRowTable {border-collapse:collapse; border-right: 0px;}.claro .dojoxGridHeader .dojoxGridRowTable tr {background: none;}.claro .dojoxGridHeader tr:first-child .dojoxGridCell {border-top: 1px solid transparent;}.claro .dojoxGridHeader:first-child .dojoxGridRowTable {border-left-width: 0;}.claro .dojoxGridHeader:first-child {margin-left: -1px;}.claro .dojoxGridScrollbox {background-color: #fefefe;}.claro .dojoxGridRowbar {background:url("images/header.png") #e5edf4 repeat-x top; border:none; border-right:1px solid #BCBCBC;}.claro .dojoxGridRowbarTable {background:transparent url("images/header_shadow.png") repeat-x scroll center bottom;}.dj_ie6 .claro .dojoxGridRowbar,.dj_ie6 .claro .dojoxGridRowbarTable {background-image:none;}.claro .dojoxGridRowbarInner {border:none; border-bottom:1px solid #ccc;}.claro .dojoxGridRowbarOver .dojoxGridRowbarTable {background-color:#abd5fd;}.claro .dojoxGridRowbarSelected {background-color:#abd5fd; border-right:1px solid #ccc;}.claro .dojoxGridRow {border:none; background-color:#fff;}.dj_ie .claro .dojoxGridMasterView .dojoxGridRowTable {border-collapse:separate;}.dj_ie6 .claro .dojoxGridMasterView .dojoxGridRowTable,.dj_ie7 .claro .dojoxGridMasterView .dojoxGridRowTable {border-collapse: collapse;}.claro .dojoxGridRowTable tr {background:url("images/row_back.png") #fff repeat-x;}.claro .dojoxGridRowOdd .dojoxGridRowTable tr {background-color:#f7fcff;}.claro .dojoxGridRowSelected .dojoxGridRowTable tr {background-color:#cee6fa;}.claro .dojoxGrid .dojoxGridCell {outline: none; padding: 3px 5px; border:1px solid transparent; border-color: transparent #E5DAC8 #E5DAC8 transparent;}.dj_ie7 .claro .dojoxGridCell,.dj_ie7 .claro .dojoxGridHeader .dojoxGridCell {border-left: 0px;}.dj_ie6 .claro .dojoxGridCell {border-color: #F5F5F5;}.dj_ie6 .claro .dojoxGridRowOdd .dojoxGridCell {border-left-color:#f4f9fd; border-right-color:#f4f9fd;}.dj_ie6 .claro .dojoxGridRowSelected .dojoxGridCell {border-left-color:#d3e9fb; border-right-color:#d3e9fb;}.claro .dojoxGridRowSelected .dojoxGridCell {border-top:1px solid #BFD6EB; border-bottom:1px solid #BFD6EB;}.claro .dojoxGridCellFocus {outline: none; border:1px dashed darkblue !important;}.claro .dojoxGridRowOver .dojoxGridCell {background:url("images/row_back.png") #ABD6FF repeat-x; border-top:1px solid #769DC0; border-bottom:1px solid #769DC0;}.dj_ie6 .claro .dojoxGridRowOver .dojoxGridCell,.dj_ie7 .claro .dojoxGridRowOver .dojoxGridCell {border-right:1px solid #ABD6FF;}.claro .dojoxGridRowActive .dojoxGridCell {background:url("images/td_button_down.png") #7DBEFA repeat-x;}.dj_ie6 .claro .dojoxGridRowActive .dojoxGridCell,.dj_ie7 .claro .dojoxGridRowActive .dojoxGridCell {border-left:1px solid #7DBEFA; border-right:1px solid #7DBEFA;}.claro .dojoxGridDoubleAffordance .dojoxGridRowOver .dojoxGridCellOver {border:solid 1px #769dc0; background-color:#93cafe; border-collapse:separate;}.claro .dojoxGridDoubleAffordance .dojoxGridRowActive .dojoxGridCell{background-image:url("images/row_back.png");}.claro .dojoxGridDoubleAffordance .dojoxGridRowActive .dojoxGridCellActive {background:url("images/td_button_down.png") #93cafe repeat-x;}.dj_ie6 .claro .dojoxGridCell {background-image:none !important;}.claro .dojoxGridRowEditing td {background-color: #cee6fa;}.claro .dojoxGridRow-inserting td {background-color: #F4FFF4;}.claro .dojoxGridRow-inflight td {background-color: #F2F7B7;}.claro .dojoxGridRow-error td {background-color: #F8B8B6;}.claro .dojoxGrid .dojoDndItemBefore {border-left-color: #3559ac;}.claro .dojoxGrid .dojoDndItemAfter {border-right-color: #3559ac;}.claro .dojoxGridExpando {float: left; height: 18px; width: 18px; text-align: center; margin-top: -3px;}.dijitRtl .claro .dojoxGridExpando {float: right;}.claro .dojoxGridExpandoCell {padding-top: 5px; background-position: left top !important;}.claro .dojoxGridExpandoNode {background-image: url('../../../dijit/themes/claro/images/treeExpandImages.png'); width: 16px; height: 16px; cursor: pointer; background-position: 1px 0px;}.dj_ie6 .claro .dojoxGridExpandoNode {background-image: url('../../../dijit/themes/claro/images/treeExpandImages8bit.png');}.claro .dojoxGridRowOver .dojoxGridExpandoNode {background-position: -17px 0px;}.claro .dojoxGridExpandoOpened .dojoxGridExpandoNode {background-position: -35px 0px;}.claro .dojoxGridRowOver .dojoxGridExpandoOpened .dojoxGridExpandoNode {background-position: -53px 0px;}.claro .dojoxGridExpandoLoading .dojoxGridExpandoNode {background-image: url('../../../dijit/icons/images/loadingAnimation.gif');}.claro .dojoxGridTreeModel .dojoxGridNoChildren .dojoxGridExpando {visibility: visible !important; width: 18px !important;}.claro .dojoxGridTreeModel .dojoxGridNoChildren .dojoxGridExpandoNode,.dj_ie6 .claro .dojoxGridTreeModel .dojoxGridNoChildren .dojoxGridExpandoNode {background-image:none;}.claro .dojoxGridExpandoNodeInner {visibility: hidden;}.dijit_a11y .dojoxGridExpandoNodeInner {visibility: visible;}.claro .dojoxGridSummaryRow .dojoxGridCell {border:1px solid transparent;}.dj_ie6 .claro .dojoxGridSummaryRow .dojoxGridCell {border-color:#fff}.claro tr.dojoxGridSubRowAlt {background-color:#f4f9fd;}.claro .dojoxGridRowOdd tr.dojoxGridSubRowAlt {background-color:#fff;}.claro .dojoxGridRow .dojoxGridRowTable tr.dojoxGridRowSelected {background-color:#cee6fa;}