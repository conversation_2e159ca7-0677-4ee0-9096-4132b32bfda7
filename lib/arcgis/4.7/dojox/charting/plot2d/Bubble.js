//>>built
define("dojo/_base/lang dojo/_base/declare dojo/_base/array dojo/has ./CartesianBase ./_PlotEvents ./common dojox/lang/functional dojox/lang/functional/reversed dojox/lang/utils dojox/gfx/fx".split(" "),function(p,t,e,y,z,A,B,C,D,r,E){var F=D.lambda("item.purgeGroup()");return t("dojox.charting.plot2d.Bubble",[z,A],{defaultParams:{animate:null},optionalParams:{stroke:{},outline:{},shadow:{},fill:{},filter:{},styleFunc:null,font:"",fontColor:"",labelFunc:null},constructor:function(h,f){this.opt=p.clone(p.mixin(this.opt,
this.defaultParams));r.updateWithObject(this.opt,f);r.updateWithPattern(this.opt,f,this.optionalParams);this.opt.labelFunc||(this.opt.labelFunc=function(f,h,e){return this._getLabel(f.size,h,e)});this.animate=this.opt.animate},render:function(h,f){var k;if(this.zoom&&!this.isDataDirty())return this.performZoom(h,f);this.resetEvents();if(this.dirty=this.isDirty())e.forEach(this.series,F),this._eventSeries={},this.cleanGroup(),k=this.getGroup(),C.forEachRev(this.series,function(a){a.cleanGroup(k)});
for(var q=this.chart.theme,p=this._hScaler.scaler.getTransformerFromModel(this._hScaler),r=this._vScaler.scaler.getTransformerFromModel(this._vScaler),t=this.events(),u=0;u<this.series.length;u++){var c=this.series[u];if(this.dirty||c.dirty)if(c.cleanGroup(),c.data.length)if("number"==typeof c.data[0])console.warn("dojox.charting.plot2d.Bubble: the data in the following series cannot be rendered as a bubble chart; ",c);else{var g=q.next("circle",[this.opt,c]),l=e.map(c.data,function(a){return a?{x:p(a.x)+
f.l,y:h.height-f.b-r(a.y),radius:a.size/2*this._vScaler.bounds.scale}:null},this);if(c.hidden)c.dyn.fill=g.series.fill,c.dyn.stroke=g.series.stroke;else{k=c.group;var d=null,m=null,n=null,w=this.opt.styleFunc,v=function(a){return w?q.addMixin(g,"circle",[a,w(a)],!0):q.addMixin(g,"circle",a,!0)};g.series.shadow&&(n=e.map(l,function(a,b){return this.isNullValue(a)?null:(b=v(c.data[b]).series.shadow,b=k.createCircle({cx:a.x+b.dx,cy:a.y+b.dy,r:a.radius}).setStroke(b).setFill(b.color),this.animate&&this._animateBubble(b,
h.height-f.b,a.radius),b)},this),n.length&&(c.dyn.shadow=n[n.length-1].getStroke()));g.series.outline&&(m=e.map(l,function(a,b){return this.isNullValue(a)?null:(b=v(c.data[b]),b=B.makeStroke(b.series.outline),b.width=2*b.width+(g.series.stroke&&g.series.stroke.width||0),b=k.createCircle({cx:a.x,cy:a.y,r:a.radius}).setStroke(b),this.animate&&this._animateBubble(b,h.height-f.b,a.radius),b)},this),m.length&&(c.dyn.outline=m[m.length-1].getStroke()));d=e.map(l,function(a,b){if(!this.isNullValue(a)){var e=
v(c.data[b]),g={x:a.x-a.radius,y:a.y-a.radius,width:2*a.radius,height:2*a.radius},d=this._plotFill(e.series.fill,h,f),d=this._shapeFill(d,g),d=k.createCircle({cx:a.x,cy:a.y,r:a.radius}).setFill(d).setStroke(e.series.stroke);d.setFilter&&e.series.filter&&d.setFilter(e.series.filter);this.animate&&this._animateBubble(d,h.height-f.b,a.radius);this.createLabel(k,c.data[b],g,e);return d}return null},this);d.length&&(c.dyn.fill=d[d.length-1].getFill(),c.dyn.stroke=d[d.length-1].getStroke());if(t){var x=
Array(d.length);e.forEach(d,function(a,b){null!==a&&(a={element:"circle",index:b,run:c,shape:a,outline:m&&m[b]||null,shadow:n&&n[b]||null,x:c.data[b].x,y:c.data[b].y,r:c.data[b].size/2,cx:l[b].x,cy:l[b].y,cr:l[b].radius},this._connectEvents(a),x[b]=a)},this);this._eventSeries[c.name]=x}else delete this._eventSeries[c.name];c.dirty=!1}}else c.dirty=!1,q.skip();else q.skip(),this._reconnectEvents(c.name)}this.dirty=!1;y("dojo-bidi")&&this._checkOrientation(this.group,h,f);return this},_animateBubble:function(e,
f,k){E.animateTransform(p.delegate({shape:e,duration:1200,transform:[{name:"translate",start:[0,f],end:[0,0]},{name:"scale",start:[0,1/k],end:[1,1]},{name:"original"}]},this.animate)).play()}})});