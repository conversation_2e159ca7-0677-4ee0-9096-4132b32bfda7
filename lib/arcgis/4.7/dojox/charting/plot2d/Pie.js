//>>built
define("dojo/_base/lang dojo/_base/array dojo/_base/declare dojo/dom-geometry dojo/_base/Color ./Base ./_PlotEvents ./common dojox/gfx dojox/gfx/matrix dojox/lang/functional dojox/lang/utils dojo/has".split(" "),function(J,C,M,T,Z,N,O,aa,E,U,v,K,V){return M("dojox.charting.plot2d.Pie",[N,O],{defaultParams:{labels:!0,ticks:!1,fixed:!0,precision:1,labelOffset:20,labelStyle:"default",htmlLabels:!0,radGrad:"native",fanSize:5,startAngle:0,innerRadius:0,minWidth:0,zeroDataMessage:""},optionalParams:{radius:0,
omitLabels:!1,stroke:{},outline:{},shadow:{},fill:{},filter:{},styleFunc:null,font:"",fontColor:"",labelWiring:{}},constructor:function(h,a){this.opt=J.clone(this.defaultParams);K.updateWithObject(this.opt,a);K.updateWithPattern(this.opt,a,this.optionalParams);this.axes=[];this.run=null;this.dyn=[];this.runFilter=[];a&&a.hasOwnProperty("innerRadius")&&(this._plotSetInnerRadius=!0)},clear:function(){this.inherited(arguments);this.dyn=[];this.run=null;return this},setAxis:function(h){return this},addSeries:function(h){this.run=
h;return this},getSeriesStats:function(){return J.delegate(aa.defaultStats)},getRequiredColors:function(){return this.run?this.run.data.length:0},render:function(h,a){if(!this.dirty)return this;this.resetEvents();this.dirty=!1;this._eventSeries={};this.cleanGroup();var f=this.group,d=this.chart.theme;!this._plotSetInnerRadius&&d&&d.pieInnerRadius&&(this.opt.innerRadius=d.pieInnerRadius);var l=(h.width-a.l-a.r)/2,g=(h.height-a.t-a.b)/2,b=Math.min(l,g),k="font"in this.opt?this.opt.font:d.axis.tick.titleFont||
"",w=k?E.normalizedLength(E.splitFontString(k).size):0,u=this.opt.hasOwnProperty("fontColor")?this.opt.fontColor:d.axis.tick.fontColor,F=U._degToRad(this.opt.startAngle),q=F,r,x,B,P,G=this.run.data,Q=this.events(),H=J.hitch(this,function(){var m=d.clone(),c=v.map(G,function(a){var c=[this.opt,this.run];null!==a&&"number"!=typeof a&&c.push(a);this.opt.styleFunc&&c.push(this.opt.styleFunc(a));return m.next("slice",c,!0)},this);"radius"in this.opt&&(b=this.opt.radius<b?this.opt.radius:b);var y={cx:a.l+
l,cy:a.t+g,r:b},e=new Z(u);this.opt.innerRadius&&(e.a=.1);var n=this._createRing(f,y).setStroke(e);this.opt.innerRadius&&n.setFill(e);this.opt.zeroDataMessage&&this.renderLabel(f,y.cx,y.cy+w/3,this.opt.zeroDataMessage,{series:{font:k,fontColor:u}},null,"middle");this.dyn=[];C.forEach(G,function(m,b){this.dyn.push({fill:this._plotFill(c[b].series.fill,h,a),stroke:c[b].series.stroke})},this)});if(!this.run&&!this.run.data.ength)return H(),this;if("number"==typeof G[0]){r=v.map(G,"x ? Math.max(x, 0) : 0");
if(v.every(r,"\x3c\x3d 0"))return H(),this;x=v.map(r,"/this",v.foldl(r,"+",0));this.opt.labels&&(B=C.map(x,function(m){return 0<m?this._getLabel(100*m)+"%":""},this))}else{r=v.map(G,"x ? Math.max(x.y, 0) : 0");if(!r.length||v.every(r,"\x3c\x3d 0"))return H(),this;x=v.map(r,"/this",v.foldl(r,"+",0));this.opt.labels&&(B=C.map(x,function(m,a){if(0>m)return"";a=G[a];return a.hasOwnProperty("text")?a.text:this._getLabel(100*m)+"%"},this))}var I=v.map(G,function(a){var m=[this.opt,this.run];null!==a&&"number"!=
typeof a&&m.push(a);this.opt.styleFunc&&m.push(this.opt.styleFunc(a));return d.next("slice",m,!0)},this);this.opt.labels&&(r=v.foldl1(v.map(B,function(a,c){return E._base._getTextBox(a,{font:I[c].series.font}).w},this),"Math.max(a, b)")/2,0>this.opt.labelOffset&&(b=Math.min(l-2*r,g-w)+this.opt.labelOffset));this.opt.hasOwnProperty("radius")&&(b=this.opt.radius<.9*b?this.opt.radius:.9*b);!this.opt.radius&&this.opt.labels&&"columns"==this.opt.labelStyle?(b/=2,l>g&&l>2*b&&(b*=l/(2*b)),b>=.8*g&&(b=.8*
g)):b>=.9*g&&(b=.9*g);P=b-this.opt.labelOffset;var c={cx:a.l+l,cy:a.t+g,r:b};this.dyn=[];var L=Array(x.length),n=[],p=q,R=this.opt.minWidth;C.forEach(x,function(a,c){if(0===a)n[c]={step:0,end:p,start:p,weak:!1};else{a=p+2*a*Math.PI;c===x.length-1&&(a=F+2*Math.PI);var m=a-p;n[c]={step:m,start:p,end:a,weak:m*b<R};p=a}});if(0<R){r=0;var z=R/b,H=0,e;for(e=n.length-1;0<=e;e--)n[e].weak&&(++r,H+=n[e].step,n[e].step=z);z*=r;if(z>Math.PI){z=Math.PI/r;for(e=0;e<n.length;++e)n[e].weak&&(n[e].step=z);z=Math.PI}if(0<
r)for(z=1-(z-H)/2/Math.PI,e=0;e<n.length;++e)n[e].weak||(n[e].step*=z);for(e=0;e<n.length;++e)n[e].start=e?n[e].end:p,n[e].end=n[e].start+n[e].step;for(e=n.length-1;0<=e;--e)if(0!==n[e].step){n[e].end=p+2*Math.PI;break}}var p=q,D,t;C.some(x,function(m,A){var y,e=G[A],d=I[A];if(1<=m){t=this._plotFill(d.series.fill,h,a);t=this._shapeFill(t,{x:c.cx-c.r,y:c.cy-c.r,width:2*c.r,height:2*c.r});t=this._pseudoRadialFill(t,{x:c.cx,y:c.cy},c.r);y=this._createRing(f,c).setFill(t).setStroke(d.series.stroke);this.dyn.push({fill:t,
stroke:d.series.stroke});Q&&(D={element:"slice",index:A,run:this.run,shape:y,x:A,y:"number"==typeof e?e:e.y,cx:c.cx,cy:c.cy,cr:b},this._connectEvents(D),L[A]=D);for(A+=1;A<x.length;A++)d=I[A],this.dyn.push({fill:d.series.fill,stroke:d.series.stroke});return!0}if(0===n[A].step)return this.dyn.push({fill:d.series.fill,stroke:d.series.stroke}),!1;m=n[A].step;y=c.cx+b*Math.cos(p);var g=c.cy+b*Math.sin(p),l=c.cx+b*Math.cos(p+m),r=c.cy+b*Math.sin(p+m),k=U._degToRad(this.opt.fanSize);if(d.series.fill&&"radial"===
d.series.fill.type&&"fan"===this.opt.radGrad&&m>k){var w=f.createGroup(),k=Math.ceil(m/k),u=m/k;t=this._shapeFill(d.series.fill,{x:c.cx-c.r,y:c.cy-c.r,width:2*c.r,height:2*c.r});var q,v,z,F,B,C;for(q=0;q<k;++q)v=p+(q-.2)*u,z=p+(q+1+.2)*u,F=0==q?y:c.cx+b*Math.cos(v),B=0==q?g:c.cy+b*Math.sin(v),C=q==k-1?l:c.cx+b*Math.cos(z),z=q==k-1?r:c.cy+b*Math.sin(z),this._createSlice(w,c,b,F,B,C,z,v,u).setFill(this._pseudoRadialFill(t,{x:c.cx,y:c.cy},b,p+(q+.5)*u,p+(q+.5)*u));k=d.series.stroke;this._createSlice(w,
c,b,y,g,l,r,p,m).setStroke(k);y=w}else{k=d.series.stroke;y=this._createSlice(f,c,b,y,g,l,r,p,m).setStroke(k);if((t=d.series.fill)&&"radial"===t.type)t=this._shapeFill(t,{x:c.cx-c.r,y:c.cy-c.r,width:2*c.r,height:2*c.r}),"linear"===this.opt.radGrad&&(t=this._pseudoRadialFill(t,{x:c.cx,y:c.cy},b,p,p+m));else if(t&&"linear"===t.type){g=J.clone(y.getBoundingBox());if("svg"===E.renderer){l={w:0,h:0};try{l=T.position(y.rawNode)}catch(ba){}l.h>g.height&&(g.height=l.h);l.w>g.width&&(g.width=l.w)}t=this._plotFill(t,
h,a);t=this._shapeFill(t,g)}y.setFill(t)}this.dyn.push({fill:t,stroke:d.series.stroke});Q&&(D={element:"slice",index:A,run:this.run,shape:y,x:A,y:"number"==typeof e?e:e.y,cx:c.cx,cy:c.cy,cr:b},this._connectEvents(D),L[A]=D);p+=m;return!1},this);if(this.opt.labels){var K=V("dojo-bidi")&&this.chart.isRightToLeft();if("default"==this.opt.labelStyle)p=q=F,C.some(x,function(a,b){if(0>=a&&!this.opt.minWidth)return!1;var m=I[b];if(1<=a)return this.renderLabel(f,c.cx,c.cy+w/2,B[b],m,0<this.opt.labelOffset),
!0;a=q+2*a*Math.PI;b+1==x.length&&(a=F+2*Math.PI);if(this.opt.omitLabels&&.001>a-q)return!1;var d=p+n[b].step/2,e=c.cx+P*Math.cos(d);this.renderLabel(f,K?h.width-e:e,c.cy+P*Math.sin(d)+w/2,B[b],m,0<this.opt.labelOffset);p+=n[b].step;q=a;return!1},this);else if("columns"==this.opt.labelStyle){var M=this.opt.omitLabels,p=q=F,S=[],W=0;for(r=x.length-1;0<=r;--r)x[r]&&++W;C.forEach(x,function(a,c){a=q+2*a*Math.PI;c+1==x.length&&(a=F+2*Math.PI);if(0!==this.minWidth||.001<=a-q){var b=p+n[c].step/2;1!==W||
this.opt.minWidth||(b=(q+a)/2);S.push({angle:b,left:0>Math.cos(b),theme:I[c],index:c,omit:M?.001>a-q:!1})}q=a;p+=n[c].step},this);var X=E._base._getTextBox("a",{font:k,whiteSpace:"nowrap"}).h;this._getProperLabelRadius(S,X,1.1*c.r);var Y=c.cx-2*c.r,N=c.cx+2*c.r;C.forEach(S,function(a){if(!a.omit){var b=I[a.index],d=0;b&&b.axis&&b.axis.tick&&b.axis.tick.labelGap&&(d=b.axis.tick.labelGap);var e=E._base._getTextBox(B[a.index],{font:b.series.font,whiteSpace:"nowrap",paddingLeft:d+"px"}).w,h=c.cx+a.labelR*
Math.cos(a.angle),g=c.cy+a.labelR*Math.sin(a.angle),l=a.left?Y+e:N-e,d=a.left?Y:l+d,k=c.r,k=f.createPath().moveTo(c.cx+k*Math.cos(a.angle),c.cy+k*Math.sin(a.angle));Math.abs(a.labelR*Math.cos(a.angle))<2*c.r-e&&k.lineTo(h,g);k.lineTo(l,g).setStroke(a.theme.series.labelWiring);k.moveToBack();h=this.renderLabel(f,d,X/3+g||0,B[a.index],b,!1,"left");Q&&!this.opt.htmlLabels&&(b=E._base._getTextBox(B[a.index],{font:a.theme.series.font}).w||0,e=E.normalizedLength(E.splitFontString(a.theme.series.font).size),
D={element:"labels",index:a.index,run:this.run,shape:h,x:d,y:g,label:B[a.index]},d=h.getShape(),g=T.position(this.chart.node,!0),d=J.mixin({type:"rect"},{x:d.x,y:d.y-2*e}),d.x+=g.x,d.y+=g.y,d.x=Math.round(d.x),d.y=Math.round(d.y),d.width=Math.ceil(b),d.height=Math.ceil(e),D.aroundRect=d,this._connectEvents(D),L[x.length+a.index]=D)}},this)}}var O=0;this._eventSeries[this.run.name]=v.map(G,function(a){return 0>=a?null:L[O++]});V("dojo-bidi")&&this._checkOrientation(this.group,h,a);return this},_getProperLabelRadius:function(h,
a,f){if(1==h.length)h[0].labelR=f;else{var d={},l={},g=2,b=2,k,w;for(k=0;k<h.length;++k)w=Math.abs(Math.sin(h[k].angle)),h[k].left?g>w&&(g=w,d=h[k]):b>w&&(b=w,l=h[k]);d.labelR=l.labelR=f;this._caculateLabelR(d,h,a);this._caculateLabelR(l,h,a)}},_caculateLabelR:function(h,a,f){var d,l,g=a.length,b=h.labelR,k=a[h.index].left?-f:f;l=0;f=h.index;for(d=(f+1)%g;l<g&&a[f].left===a[d].left;++l)b=(Math.sin(a[f].angle)*b+k)/Math.sin(a[d].angle),b=Math.max(h.labelR,b),a[d].labelR=b,f=(f+1)%g,d=(d+1)%g;l>=g&&
(a[0].labelR=h.labelR);l=0;f=h.index;for(d=(f||g)-1;l<g&&a[f].left===a[d].left;++l)b=(Math.sin(a[f].angle)*b-k)/Math.sin(a[d].angle),b=Math.max(h.labelR,b),a[d].labelR=b,f=(f||g)-1,d=(d||g)-1},_createRing:function(h,a){var f=this.opt.innerRadius;0<f?f=f/100*a.r:0>f&&(f=-f);return f?h.createPath({}).setAbsoluteMode(!0).moveTo(a.cx,a.cy-a.r).arcTo(a.r,a.r,0,!1,!0,a.cx+a.r,a.cy).arcTo(a.r,a.r,0,!0,!0,a.cx,a.cy-a.r).closePath().moveTo(a.cx,a.cy-f).arcTo(f,f,0,!1,!0,a.cx+f,a.cy).arcTo(f,f,0,!0,!0,a.cx,
a.cy-f).closePath():h.createCircle(a)},_createSlice:function(h,a,f,d,l,g,b,k,w){var u=this.opt.innerRadius;0<u?u=u/100*a.r:0>u&&(u=-u);if(u){var v=a.cx+u*Math.cos(k),q=a.cy+u*Math.sin(k),r=a.cx+u*Math.cos(k+w);a=a.cy+u*Math.sin(k+w);return h.createPath({}).setAbsoluteMode(!0).moveTo(v,q).lineTo(d,l).arcTo(f,f,0,w>Math.PI,!0,g,b).lineTo(r,a).arcTo(u,u,0,w>Math.PI,!1,v,q).closePath()}return h.createPath({}).setAbsoluteMode(!0).moveTo(a.cx,a.cy).lineTo(d,l).arcTo(f,f,0,w>Math.PI,!0,g,b).lineTo(a.cx,
a.cy).closePath()}})});