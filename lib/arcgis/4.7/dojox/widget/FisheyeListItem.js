//>>built
define("dojo/_base/declare dojo/_base/sniff dojo/dom dojo/dom-attr dojo/dom-class dojo/dom-style dojo/dom-construct dojo/_base/window dijit/_WidgetBase dijit/_TemplatedMixin dijit/_Contained".split(" "),function(d,e,f,b,c,g,h,k,l,m,n){return d("dojox.widget.FisheyeListItem",[l,m,n],{iconSrc:"",label:"",id:"",templateString:'\x3cdiv class\x3d"dojoxFisheyeListItem"\x3e  \x3cimg class\x3d"dojoxFisheyeListItemImage" data-dojo-attach-point\x3d"imgNode" data-dojo-attach-event\x3d"onmouseover:onMouseOver,onmouseout:onMouseOut,onclick:onClick"\x3e  \x3cdiv class\x3d"dojoxFisheyeListItemLabel" data-dojo-attach-point\x3d"lblNode"\x3e\x3c/div\x3e\x3c/div\x3e',
_isNode:function(a){if("function"==typeof Element)try{return a instanceof Element}catch(p){}else return a&&!isNaN(a.nodeType);return!1},_hasParent:function(a){return!!(a&&a.parentNode&&this._isNode(a.parentNode))},postCreate:function(){var a;".png"==this.iconSrc.toLowerCase().substring(this.iconSrc.length-4)&&7>e("ie")?(this._hasParent(this.imgNode)&&""!=this.id&&(a=this.imgNode.parentNode,b.set(a,"id",this.id)),g.set(this.imgNode,"filter","progid:DXImageTransform.Microsoft.AlphaImageLoader(src\x3d'"+
this.iconSrc+"', sizingMethod\x3d'scale')"),this.imgNode.src=this._blankGif.toString()):(this._hasParent(this.imgNode)&&""!=this.id&&(a=this.imgNode.parentNode,b.set(a,"id",this.id)),this.imgNode.src=this.iconSrc);this.lblNode&&h.place(k.doc.createTextNode(this.label),this.lblNode);f.setSelectable(this.domNode,!1);this.startup()},startup:function(){this.parent=this.getParent()},onMouseOver:function(a){this.parent.isOver||this.parent._setActive(a);""!=this.label&&(c.add(this.lblNode,"dojoxFishSelected"),
this.parent._positionLabel(this))},onMouseOut:function(a){c.remove(this.lblNode,"dojoxFishSelected")},onClick:function(a){}})});