//>>built
define("dojo/request dojo/when dojo/_base/lang dojo/json dojo/io-query dojo/_base/declare ./Request".split(" "),function(e,l,f,g,p,m,n){return m(n,{stringify:g.stringify,_getTarget:function(b){var a=this.target;return"/"==a.slice(-1)?a+b:a+"/"+b},get:function(b,a){a=a||{};a=f.mixin({Accept:this.accepts},this.headers,a.headers||a);var c=this;return e(this._getTarget(b),{headers:a}).then(function(a){return c._restore(c.parse(a),!0)})},autoEmitEvents:!1,put:function(b,a){a=a||{};var c="id"in a?a.id:
this.getIdentity(b),d="undefined"!==typeof c,h=this,g="beforeId"in a?null===a.beforeId?{"Put-Default-Position":"end"}:{"Put-Before":a.beforeId}:d&&!1!==a.overwrite?null:{"Put-Default-Position":this.defaultNewToStart?"start":"end"},k=e(d?this._getTarget(c):this.target,{method:d&&!a.incremental?"PUT":"POST",data:this.stringify(b),headers:f.mixin({"Content-Type":"application/json",Accept:this.accepts,"If-Match":!0===a.overwrite?"*":null,"If-None-Match":!1===a.overwrite?"*":null},g,this.headers,a.headers)});
return k.then(function(c){var d={};"beforeId"in a&&(d.beforeId=a.beforeId);c=d.target=c&&h._restore(h.parse(c),!0)||b;l(k.response,function(a){h.emit(201===a.status?"add":"update",d)});return c})},add:function(b,a){a=a||{};a.overwrite=!1;return this.put(b,a)},remove:function(b,a){a=a||{};var c=this;return e(this._getTarget(b),{method:"DELETE",headers:f.mixin({},this.headers,a.headers)}).then(function(a){var d=a&&c.parse(a);c.emit("delete",{id:b,target:d});return a?d:!0})}})});